"""
使用F12+F1操作流程的自动买入脚本
正确的同花顺操作流程：F12打开买入口 -> F1进入买入界面 -> 输入信息 -> 回车确认
"""
import sys
import os
import time
import pyautogui
from datetime import datetime
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ths_trader import ThsTrader
from config import get_config

class F12F1AutoBuy:
    """使用F12+F1操作流程的自动买入系统"""
    
    def __init__(self):
        self.config = get_config()
        # 设置pyautogui参数
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.5
        
    def connect_ths(self):
        """连接同花顺"""
        print("🔗 连接同花顺...")
        trader = ThsTrader(self.config)
        
        if trader.login():
            print("✅ 同花顺连接成功")
            return trader
        else:
            print("❌ 同花顺连接失败")
            return None
    
    def f12_f1_buy_process(self, stock_code, price, amount):
        """
        使用F12+F1的正确买入流程
        
        Args:
            stock_code: 股票代码
            price: 买入价格
            amount: 买入数量
        """
        print("🎯 使用F12+F1正确操作流程")
        print("=" * 50)
        
        try:
            print("⏰ 准备开始操作...")
            for i in range(3, 0, -1):
                print(f"   {i}秒后开始...")
                time.sleep(1)
            
            print("\n🔑 步骤1: 按F12打开买入输入口...")
            pyautogui.press('f12')
            time.sleep(1.5)  # 等待界面响应
            print("✅ 已按F12，买入输入口已打开")
            
            print("🔑 步骤2: 按F1进入买入输入界面...")
            pyautogui.press('f1')
            time.sleep(1.5)  # 等待界面切换
            print("✅ 已按F1，进入买入输入界面")
            
            print(f"🔤 步骤3: 输入股票代码 {stock_code}...")
            # 此时焦点应该在股票代码输入框
            pyautogui.write(stock_code)
            time.sleep(0.5)
            pyautogui.press('tab')  # 移动到价格输入框
            time.sleep(0.5)
            print(f"✅ 已输入股票代码: {stock_code}")
            
            print(f"💰 步骤4: 输入买入价格 {price}...")
            # 现在焦点在价格输入框
            pyautogui.write(str(price))
            time.sleep(0.5)
            pyautogui.press('tab')  # 移动到数量输入框
            time.sleep(0.5)
            print(f"✅ 已输入买入价格: {price}")
            
            print(f"📦 步骤5: 输入买入数量 {amount}...")
            # 现在焦点在数量输入框
            pyautogui.write(str(amount))
            time.sleep(0.5)
            print(f"✅ 已输入买入数量: {amount}")
            
            # 确认信息
            print(f"\n🔍 请确认输入的信息:")
            print(f"   - 股票代码: {stock_code}")
            print(f"   - 买入价格: {price}")
            print(f"   - 买入数量: {amount}")
            print(f"   - 总金额: {price * amount}元")
            
            confirm = input("\n信息正确请输入 'YES' 确认买入: ").strip()
            
            if confirm == 'YES':
                print("🛒 步骤6: 按回车确认买入...")
                pyautogui.press('enter')
                time.sleep(1)
                print("✅ 已按回车确认买入")
                
                # 等待确认对话框
                print("⏳ 等待确认对话框...")
                time.sleep(2)
                
                # 如果有确认对话框，再次按回车
                print("🔄 如有确认对话框，再次按回车...")
                pyautogui.press('enter')
                time.sleep(1)
                
                print("🎉 买入操作完成！")
                return True
            else:
                print("❌ 用户取消买入")
                return False
                
        except Exception as e:
            print(f"❌ F12+F1操作流程异常: {e}")
            return False
    
    def auto_buy_stock(self, stock_code, stock_name, price, amount):
        """完整的自动买入流程"""
        print("🚀 F12+F1自动买入系统")
        print("=" * 60)
        
        total_amount = price * amount
        
        print(f"📊 交易参数:")
        print(f"   股票代码: {stock_code}")
        print(f"   股票名称: {stock_name}")
        print(f"   买入价格: {price}元")
        print(f"   买入数量: {amount}股")
        print(f"   总金额: {total_amount}元")
        
        # 1. 连接同花顺
        trader = self.connect_ths()
        if not trader:
            return False
        
        # 2. 确认准备
        print(f"\n📋 操作准备:")
        print(f"1. 确保同花顺主界面已打开")
        print(f"2. 程序将使用F12+F1快捷键操作")
        print(f"3. 请不要移动鼠标或按键")
        
        ready = input("\n准备就绪请按回车开始...")
        
        # 3. 执行F12+F1买入流程
        success = self.f12_f1_buy_process(stock_code, price, amount)
        
        if success:
            # 记录交易日志
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            log_entry = f"{timestamp} - F12+F1买入 {stock_name}({stock_code}) {amount}股 @{price}元 总额{total_amount}元\n"
            
            with open("f12_f1_buy_log.txt", "a", encoding="utf-8") as f:
                f.write(log_entry)
            
            print("📝 交易日志已记录")
            return True
        else:
            return False

def buy_000528():
    """使用F12+F1流程买入000528柳工"""
    auto_buy = F12F1AutoBuy()
    
    return auto_buy.auto_buy_stock(
        stock_code='000528',
        stock_name='柳工',
        price=8.52,
        amount=100
    )

def buy_custom():
    """使用F12+F1流程买入自定义股票"""
    auto_buy = F12F1AutoBuy()
    
    print("📝 请输入股票信息:")
    stock_code = input("股票代码: ").strip()
    stock_name = input("股票名称: ").strip()
    price = float(input("买入价格: ").strip())
    amount = int(input("买入数量: ").strip())
    
    return auto_buy.auto_buy_stock(stock_code, stock_name, price, amount)

def test_f12_f1_keys():
    """测试F12+F1按键功能"""
    print("🧪 测试F12+F1按键功能")
    print("=" * 40)
    
    print("请确保同花顺主界面已打开")
    input("准备就绪后按回车开始测试...")
    
    print("⏰ 3秒后开始测试...")
    for i in range(3, 0, -1):
        print(f"   {i}...")
        time.sleep(1)
    
    print("🔑 测试按F12...")
    pyautogui.press('f12')
    time.sleep(2)
    
    check_f12 = input("F12是否打开了买入输入口？(y/n): ").lower().strip()
    
    if check_f12 == 'y':
        print("✅ F12功能正常")
        
        print("🔑 测试按F1...")
        pyautogui.press('f1')
        time.sleep(2)
        
        check_f1 = input("F1是否进入了买入输入界面？(y/n): ").lower().strip()
        
        if check_f1 == 'y':
            print("✅ F1功能正常")
            print("🎉 F12+F1按键测试通过！")
            return True
        else:
            print("❌ F1功能异常")
            return False
    else:
        print("❌ F12功能异常")
        return False

def main():
    """主函数"""
    print("🎯 F12+F1自动买入系统")
    print("=" * 50)
    print("使用正确的同花顺操作流程:")
    print("F12打开买入口 -> F1进入买入界面 -> 输入信息 -> 回车确认")
    print()
    
    print("请选择操作:")
    print("1. 买入000528 (柳工)")
    print("2. 自定义买入")
    print("3. 测试F12+F1按键")
    print("4. 退出")
    
    choice = input("\n请选择 (1-4): ").strip()
    
    try:
        if choice == '1':
            success = buy_000528()
        elif choice == '2':
            success = buy_custom()
        elif choice == '3':
            success = test_f12_f1_keys()
        elif choice == '4':
            print("👋 程序退出")
            return
        else:
            print("❌ 无效选择")
            return
        
        print("\n" + "=" * 50)
        if success:
            print("🎉 F12+F1自动买入完成！")
            print("✅ 使用了正确的同花顺操作流程")
            print("📋 请在同花顺中查看委托状态")
        else:
            print("❌ F12+F1自动买入失败")
            print("💡 建议:")
            print("   1. 确保同花顺主界面已打开")
            print("   2. 检查F12+F1快捷键是否正常")
            print("   3. 重新尝试操作")
        print("=" * 50)
        
    except KeyboardInterrupt:
        print("\n❌ 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")

if __name__ == "__main__":
    main()
