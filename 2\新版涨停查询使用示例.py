#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
新版涨停正股转债查询使用示例

展示如何使用升级后的涨停查询系统
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from 涨停正股转债查询 import LimitUpStockBondQuery


def example_with_database():
    """使用数据库的完整示例"""
    print("🚀 新版涨停正股转债查询系统")
    print("=" * 60)
    
    # 数据库配置
    db_config = {
        'host': '***********',
        'port': 3306,
        'user': 'iQuant',
        'password': 'NAAnwaRsb8YGN3F5',
        'database': 'iquant',
        'charset': 'utf8mb4'
    }
    
    try:
        # 初始化查询器
        print("📊 初始化查询系统...")
        query_tool = LimitUpStockBondQuery(db_config)
        
        # 连接数据库
        if query_tool.connect_database():
            print("✅ 数据库连接成功")
            
            # 查询最近10天的涨停股票转债
            print(f"\n🔍 查询最近10天涨停股票对应的可转债...")
            result = query_tool.get_limit_up_stocks_with_bonds(days=10)
            
            # 显示详细报告
            query_tool.display_limit_up_bonds_report(result)
            
            # 导出数据
            print(f"\n📁 导出查询结果...")
            output_file = query_tool.export_limit_up_bonds(result)
            
            if output_file:
                print(f"✅ 数据已导出到: {output_file}")
            
            # 关闭连接
            query_tool.close()
            
        else:
            print("❌ 数据库连接失败，使用模拟数据演示")
            example_without_database()
            
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        print("使用模拟数据演示...")
        example_without_database()


def example_without_database():
    """不使用数据库的演示示例"""
    print(f"\n{'='*60}")
    print("📊 模拟数据演示")
    print(f"{'='*60}")
    
    # 数据库配置（仅用于初始化）
    db_config = {
        'host': '***********',
        'port': 3306,
        'user': 'iQuant',
        'password': 'NAAnwaRsb8YGN3F5',
        'database': 'iquant',
        'charset': 'utf8mb4'
    }
    
    try:
        # 初始化查询器（会自动加载可转债映射关系）
        query_tool = LimitUpStockBondQuery(db_config)
        
        if not query_tool.bond_stock_mapping:
            print("❌ 可转债映射关系加载失败")
            return
        
        print(f"✅ 可转债映射关系加载成功")
        print(f"📊 包含 {len(query_tool.bond_stock_mapping)} 只正股对应的可转债")
        
        # 模拟涨停股票数据（使用有可转债的股票）
        stocks_with_bonds = []
        for stock_code, bonds in list(query_tool.bond_stock_mapping.items())[:5]:
            # 只选择有实时数据的可转债
            realtime_bonds = [bond for bond in bonds if bond.get('has_realtime_data')]
            if realtime_bonds:
                stocks_with_bonds.append((stock_code, realtime_bonds))
        
        print(f"\n🎯 演示有实时数据的涨停股票转债:")
        
        for i, (stock_code, bonds) in enumerate(stocks_with_bonds[:3], 1):
            print(f"\n{i}. 模拟股票 {stock_code} 涨停:")
            print(f"   对应可转债 ({len(bonds)} 只):")
            
            for bond in bonds:
                market_str = f"({bond.get('market', 'N/A')})"
                
                # 显示实时价格信息
                if bond.get('current_price') and str(bond.get('current_price')) != 'nan':
                    try:
                        price = float(bond['current_price'])
                        price_info = f"价格:{price:.2f}"
                    except:
                        price_info = "价格:--"
                else:
                    price_info = "价格:--"
                
                # 显示涨跌幅
                if bond.get('change_rate') and str(bond.get('change_rate')) != 'nan':
                    try:
                        change = float(bond['change_rate'])
                        price_info += f" 涨幅:{change:.2f}%"
                    except:
                        pass
                
                # 显示评级
                rating_str = f" 评级:{bond.get('rating')}" if bond.get('rating') else ""
                
                print(f"     • {bond['bond_code']} {bond['bond_name']} {market_str} [{price_info}]{rating_str} 🔥")
        
        # 显示系统特性
        print(f"\n💡 新版系统特性:")
        print(f"  ✅ 使用东方财富终极完整可转债数据")
        print(f"  ✅ 包含实时价格、涨跌幅、成交量等行情数据")
        print(f"  ✅ 包含转股价值、转股溢价率等转股数据")
        print(f"  ✅ 包含评级、到期日期等基础信息")
        print(f"  ✅ 自动识别活跃交易的可转债")
        print(f"  ✅ 完整的CSV导出功能")
        
        # 统计信息
        total_bonds = sum(len(bonds) for bonds in query_tool.bond_stock_mapping.values())
        realtime_bonds = sum(1 for bonds in query_tool.bond_stock_mapping.values() 
                           for bond in bonds if bond.get('has_realtime_data'))
        
        print(f"\n📊 数据统计:")
        print(f"  正股数量: {len(query_tool.bond_stock_mapping)}")
        print(f"  可转债总数: {total_bonds}")
        print(f"  有实时数据: {realtime_bonds}")
        print(f"  数据完整性: {realtime_bonds/total_bonds*100:.1f}%")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()


def show_system_info():
    """显示系统信息"""
    print(f"\n{'='*60}")
    print("📋 新版涨停查询系统信息")
    print(f"{'='*60}")
    
    print("🔧 系统升级内容:")
    print("  1. 数据源升级:")
    print("     • 使用东方财富终极完整可转债数据")
    print("     • 包含979只可转债的完整信息")
    print("     • 469只正常状态可转债")
    print("     • 100只活跃可转债的实时行情")
    
    print("\n  2. 字段升级:")
    print("     • 实时价格: 债现价、涨跌幅、成交量、成交额")
    print("     • 转股数据: 转股价、转股价值、转股溢价率")
    print("     • 基础信息: 评级、到期日期、发行规模")
    print("     • 状态识别: 正常/退市状态自动识别")
    
    print("\n  3. 功能升级:")
    print("     • 智能数据合并: 结合多个API数据源")
    print("     • 实时行情显示: 活跃可转债实时价格")
    print("     • 完整性统计: 数据完整性实时统计")
    print("     • 增强导出: 包含所有新字段的CSV导出")
    
    print("\n  4. 显示优化:")
    print("     • 🔥 标记有实时数据的可转债")
    print("     • 价格格式化显示")
    print("     • 转股信息详细展示")
    print("     • 评级和到期信息显示")
    
    print("\n📁 相关文件:")
    print("  • 涨停正股转债查询.py - 主查询工具")
    print("  • 东方财富终极完整可转债_20250807_004218.csv - 数据源")
    print("  • 新版涨停查询使用示例.py - 使用示例")
    
    print("\n🚀 使用方法:")
    print("  1. 确保数据文件存在")
    print("  2. 配置数据库连接（可选）")
    print("  3. 运行查询获取涨停股票对应的可转债")
    print("  4. 查看详细报告和导出数据")


def main():
    """主函数"""
    # 显示系统信息
    show_system_info()
    
    # 运行示例
    print(f"\n{'='*60}")
    print("🎯 运行示例")
    print(f"{'='*60}")
    
    # 首先尝试数据库连接
    example_with_database()


if __name__ == "__main__":
    main()
