#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试真实数据库涨停查询

验证从 iquant_daily_price 表查询涨停数据的功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from 涨停正股转债查询 import LimitUpStockBondQuery


def test_database_connection():
    """测试数据库连接"""
    print("🔍 测试数据库连接...")
    
    # 数据库配置
    db_config = {
        'host': '***********',
        'port': 3306,
        'user': 'iQuant',
        'password': 'NAAnwaRsb8YGN3F5',
        'database': 'iquant',
        'charset': 'utf8mb4'
    }
    
    try:
        # 初始化查询器
        query_tool = LimitUpStockBondQuery(db_config)
        
        # 测试连接
        if query_tool.connect_database():
            print("✅ 数据库连接成功！")
            
            # 测试简单查询
            print("\n🔍 测试数据表结构...")
            try:
                if query_tool.db_type == 'mysql.connector':
                    cursor = query_tool.connection.cursor(dictionary=True)
                else:
                    import pymysql.cursors
                    cursor = query_tool.connection.cursor(pymysql.cursors.DictCursor)
                
                # 查询表结构
                cursor.execute("DESCRIBE iquant_daily_price")
                columns = cursor.fetchall()
                
                print("✅ 数据表字段:")
                for col in columns:
                    field_name = col.get('Field') or col.get('field')
                    field_type = col.get('Type') or col.get('type')
                    print(f"  • {field_name}: {field_type}")
                
                # 查询数据量
                cursor.execute("SELECT COUNT(*) as total FROM iquant_daily_price")
                count_result = cursor.fetchone()
                total_records = count_result.get('total') or count_result.get('COUNT(*)')
                print(f"\n📊 总记录数: {total_records:,}")
                
                # 查询最新日期
                cursor.execute("SELECT MAX(day) as latest_date FROM iquant_daily_price")
                date_result = cursor.fetchone()
                latest_date = date_result.get('latest_date') or date_result.get('MAX(day)')
                print(f"📅 最新日期: {latest_date}")
                
                # 查询涨停记录数量
                cursor.execute("SELECT COUNT(*) as limit_up_count FROM iquant_daily_price WHERE limit_up = 1")
                limit_result = cursor.fetchone()
                limit_count = limit_result.get('limit_up_count') or limit_result.get('COUNT(*)')
                print(f"📈 涨停记录数: {limit_count:,}")
                
                cursor.close()
                
                return True
                
            except Exception as e:
                print(f"❌ 数据表查询失败: {e}")
                return False
            
        else:
            print("❌ 数据库连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_limit_up_query():
    """测试涨停查询功能"""
    print(f"\n{'='*60}")
    print("🔍 测试涨停查询功能")
    print(f"{'='*60}")
    
    # 数据库配置
    db_config = {
        'host': '***********',
        'port': 3306,
        'user': 'iQuant',
        'password': 'NAAnwaRsb8YGN3F5',
        'database': 'iquant',
        'charset': 'utf8mb4'
    }
    
    try:
        # 初始化查询器
        query_tool = LimitUpStockBondQuery(db_config)
        
        if query_tool.connect_database():
            print("✅ 数据库连接成功")
            
            # 测试查询最近10天的涨停数据
            print(f"\n🔍 查询最近10天涨停数据...")
            limit_up_df = query_tool.get_limit_up_stocks_recent_days(days=10)
            
            if not limit_up_df.empty:
                print(f"✅ 查询成功！找到 {len(limit_up_df)} 条涨停记录")
                print(f"📊 涉及股票: {limit_up_df['symbol'].nunique()} 只")
                
                # 显示前10条记录
                print(f"\n📋 前10条涨停记录:")
                for i, (_, row) in enumerate(limit_up_df.head(10).iterrows(), 1):
                    symbol = row['symbol']
                    day = row['day']
                    close = row['close']
                    change_pct = row['change_pct']
                    volume = row.get('volume', 0)
                    
                    # 格式化成交量
                    if volume > 100000000:
                        vol_str = f"{volume/100000000:.1f}亿"
                    elif volume > 10000:
                        vol_str = f"{volume/10000:.1f}万"
                    else:
                        vol_str = f"{volume}"
                    
                    print(f"  {i:2d}. {symbol} - {day} 收盘:{close:.2f} 涨幅:{change_pct:.2f}% 量:{vol_str}")
                
                # 统计涨停股票
                unique_stocks = limit_up_df['symbol'].unique()
                print(f"\n📈 涨停股票列表 (共{len(unique_stocks)}只):")
                for i, stock in enumerate(unique_stocks[:20], 1):  # 只显示前20只
                    stock_data = limit_up_df[limit_up_df['symbol'] == stock]
                    latest_record = stock_data.iloc[0]  # 最新记录
                    limit_count = len(stock_data)
                    
                    print(f"  {i:2d}. {stock} - 最新:{latest_record['day']} "
                          f"价格:{latest_record['close']:.2f} 涨停次数:{limit_count}")
                
                return limit_up_df
                
            else:
                print("⚠️  最近10天没有涨停记录")
                
                # 尝试查询更长时间范围
                print(f"\n🔍 尝试查询最近30天...")
                limit_up_df_30 = query_tool.get_limit_up_stocks_recent_days(days=30)
                
                if not limit_up_df_30.empty:
                    print(f"✅ 30天内找到 {len(limit_up_df_30)} 条涨停记录")
                    return limit_up_df_30
                else:
                    print("⚠️  最近30天也没有涨停记录")
                    return None
            
        else:
            print("❌ 数据库连接失败")
            return None
            
    except Exception as e:
        print(f"❌ 涨停查询失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_complete_workflow():
    """测试完整的涨停转债查询流程"""
    print(f"\n{'='*60}")
    print("🔍 测试完整涨停转债查询流程")
    print(f"{'='*60}")
    
    # 数据库配置
    db_config = {
        'host': '***********',
        'port': 3306,
        'user': 'iQuant',
        'password': 'NAAnwaRsb8YGN3F5',
        'database': 'iquant',
        'charset': 'utf8mb4'
    }
    
    try:
        # 初始化查询器
        query_tool = LimitUpStockBondQuery(db_config)
        
        if query_tool.connect_database():
            print("✅ 数据库连接成功")
            print(f"✅ 可转债映射关系已加载: {len(query_tool.bond_stock_mapping)} 只正股")
            
            # 执行完整查询
            print(f"\n🔍 执行完整的涨停转债查询...")
            result = query_tool.get_limit_up_stocks_with_bonds(days=10)
            
            # 显示结果
            if result and 'summary' in result:
                summary = result['summary']
                print(f"\n📊 查询结果汇总:")
                print(f"  查询时间范围: {summary.get('query_date_range', 'N/A')}")
                print(f"  涨停股票总数: {summary.get('total_limit_up', 0)} 只")
                print(f"  有对应可转债: {summary.get('with_bonds', 0)} 只")
                print(f"  无对应可转债: {summary.get('without_bonds', 0)} 只")
                print(f"  对应可转债总数: {summary.get('total_bonds', 0)} 只")
                
                # 显示详细报告
                if result.get('stocks_with_bonds'):
                    print(f"\n📋 详细报告:")
                    query_tool.display_limit_up_bonds_report(result)
                    
                    # 导出数据
                    print(f"\n📁 导出数据...")
                    output_file = query_tool.export_limit_up_bonds(result)
                    if output_file:
                        print(f"✅ 数据已导出到: {output_file}")
                
                return True
            else:
                print("❌ 查询结果为空")
                return False
            
        else:
            print("❌ 数据库连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 完整流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🚀 真实数据库涨停查询测试")
    print("=" * 60)
    
    # 测试1: 数据库连接
    success1 = test_database_connection()
    
    # 测试2: 涨停查询
    success2 = False
    if success1:
        limit_up_data = test_limit_up_query()
        success2 = limit_up_data is not None
    
    # 测试3: 完整流程
    success3 = False
    if success1:
        success3 = test_complete_workflow()
    
    print(f"\n{'='*60}")
    print("📊 测试结果汇总")
    print(f"{'='*60}")
    print(f"数据库连接: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"涨停数据查询: {'✅ 成功' if success2 else '❌ 失败'}")
    print(f"完整流程测试: {'✅ 成功' if success3 else '❌ 失败'}")
    
    if success1 and success2 and success3:
        print(f"\n🎉 所有测试通过！")
        print(f"💡 真实数据库涨停查询系统运行正常")
        print(f"🔥 现在可以获取真实的涨停数据和对应的可转债信息")
    else:
        print(f"\n⚠️  部分测试失败，请检查:")
        if not success1:
            print("  • 数据库连接配置")
        if not success2:
            print("  • 涨停数据是否存在")
        if not success3:
            print("  • 可转债映射关系")


if __name__ == "__main__":
    main()
