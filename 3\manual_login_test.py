"""
手动登录测试脚本
测试手动登录同花顺客户端
"""
import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ths_trader import ThsTrader
from config import get_config
import requests
import pyautogui
import time
from datetime import datetime

def get_stock_price_info(stock_code):
    """
    获取股票实时价格信息

    Args:
        stock_code: 股票代码

    Returns:
        dict: 包含买卖价格信息的字典
    """
    try:
        # 这里可以接入实时行情API
        # 暂时返回模拟数据
        if stock_code == '000528':
            return {
                'code': '000528',
                'name': '柳工',
                'current_price': 8.50,
                'buy1_price': 8.49,  # 买一价
                'buy2_price': 8.48,  # 买二价
                'sell1_price': 8.51, # 卖一价
                'sell2_price': 8.52, # 卖二价
                'volume': 1000000
            }
        else:
            return None
    except Exception as e:
        print(f"获取股票价格信息失败: {e}")
        return None

def gui_input_stock_code(stock_code, price, amount):
    """
    使用正确的同花顺操作流程：F12打开买入口 -> F1进入买入界面 -> 输入信息

    Args:
        stock_code: 股票代码
        price: 买入价格
        amount: 买入数量
    """
    print(f"🎯 使用正确的同花顺操作流程...")
    print(f"   目标股票代码: {stock_code}")
    print(f"   买入价格: {price}")
    print(f"   买入数量: {amount}")

    try:
        # 设置pyautogui参数
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.5

        print("⏰ 3秒后开始正确的操作流程...")
        for i in range(3, 0, -1):
            print(f"   {i}...")
            time.sleep(1)

        print("� 步骤1: 按F12打开买入输入口...")
        pyautogui.press('f12')
        time.sleep(1)
        print("✅ 已按F12")

        print("🔑 步骤2: 按F1进入买入输入界面...")
        pyautogui.press('f1')
        time.sleep(1)
        print("✅ 已按F1")

        print("🔤 步骤3: 输入股票代码...")
        # 直接输入股票代码（此时焦点应该在代码输入框）
        pyautogui.write(stock_code)
        time.sleep(0.5)
        pyautogui.press('tab')  # 移动到下一个输入框
        time.sleep(0.5)
        print(f"✅ 已输入股票代码: {stock_code}")

        print("💰 步骤4: 输入买入价格...")
        # 输入买入价格
        pyautogui.write(str(price))
        time.sleep(0.5)
        pyautogui.press('tab')  # 移动到下一个输入框
        time.sleep(0.5)
        print(f"✅ 已输入买入价格: {price}")

        print("📦 步骤5: 输入买入数量...")
        # 输入买入数量
        pyautogui.write(str(amount))
        time.sleep(0.5)
        print(f"✅ 已输入买入数量: {amount}")

        # 确认信息
        print(f"\n🔍 请确认同花顺界面中的信息:")
        print(f"   - 证券代码: {stock_code}")
        print(f"   - 买入价格: {price}")
        print(f"   - 买入数量: {amount}")

        confirm = input("\n信息正确请输入 'YES' 确认买入: ").strip()

        if confirm == 'YES':
            print("🛒 步骤6: 按回车确认买入...")
            pyautogui.press('enter')
            time.sleep(1)
            print("✅ 已按回车确认买入")
            return True
        else:
            print("❌ 用户取消买入")
            return False

    except Exception as e:
        print(f"❌ 操作流程异常: {e}")
        print("📋 请手动执行以下步骤:")
        print(f"   1. 按F12打开买入输入口")
        print(f"   2. 按F1进入买入输入界面")
        print(f"   3. 输入股票代码: {stock_code}")
        print(f"   4. 输入买入价格: {price}")
        print(f"   5. 输入买入数量: {amount}")
        print(f"   6. 按回车确认买入")
        return False

def test_buy_stock_000528(trader, is_dry_run=True):
    """
    测试买入000528股票（柳工）
    使用GUI自动化输入交易信息

    Args:
        trader: 交易实例
        is_dry_run: 是否为模拟模式

    Returns:
        bool: 是否成功
    """
    stock_code = '000528'
    stock_name = '柳工'
    buy_amount = 100  # 1手 = 100股

    print(f"准备买入: {stock_name}({stock_code})")
    print(f"买入数量: {buy_amount}股 (1手)")

    try:
        # 获取股票价格信息
        price_info = get_stock_price_info(stock_code)
        if not price_info:
            print("❌ 无法获取股票价格信息")
            return False

        # 使用卖二价作为买入价格
        buy_price = price_info['sell2_price']
        current_price = price_info['current_price']

        print(f"当前价格: {current_price}")
        print(f"卖一价: {price_info['sell1_price']}")
        print(f"卖二价: {price_info['sell2_price']} ← 使用此价格买入")
        print(f"买一价: {price_info['buy1_price']}")
        print(f"买二价: {price_info['buy2_price']}")

        # 计算买入金额
        total_amount = buy_price * buy_amount
        print(f"买入价格: {buy_price}")
        print(f"买入金额: {total_amount}元")

        if is_dry_run:
            print("🔄 模拟模式 - 演示GUI自动化输入")
            print(f"✅ 模拟买入: {stock_name}({stock_code})")
            print(f"   价格: {buy_price}元/股")
            print(f"   数量: {buy_amount}股")
            print(f"   总金额: {total_amount}元")

            # 演示GUI自动化输入
            demo_gui = input("\n是否演示GUI自动化输入？(y/n): ").lower().strip()
            if demo_gui == 'y':
                return gui_input_stock_code(stock_code, buy_price, buy_amount)
            else:
                return True
        else:
            print("🔄 实盘模式 - 直接使用GUI自动化执行买入")

            # 跳过easytrader，直接使用GUI自动化
            print("⚠️ easytrader无法正确输入股票代码，使用GUI自动化...")

            # 使用GUI自动化输入股票信息
            gui_success = gui_input_stock_code(stock_code, buy_price, buy_amount)

            if gui_success:
                print(f"✅ GUI自动化买入完成: {stock_name}({stock_code})")

                # 记录交易日志
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                log_entry = f"{timestamp} - GUI买入 {stock_name}({stock_code}) {buy_amount}股 @{buy_price}元 总额{total_amount}元\n"

                with open("gui_trading_log.txt", "a", encoding="utf-8") as f:
                    f.write(log_entry)

                return True
            else:
                print(f"❌ GUI自动化买入失败: {stock_name}({stock_code})")
                return False

    except Exception as e:
        print(f"❌ 买入测试异常: {e}")
        return False

def manual_login_test():
    """手动登录测试"""
    print("同花顺手动登录测试")
    print("=" * 60)
    
    # 获取配置
    config = get_config()
    
    print("配置信息:")
    print(f"  客户端路径: {config['account_config']['exe_path']}")
    print(f"  测试模式: {config['trade_config']['dry_run']}")
    
    # 检查客户端文件
    exe_path = config['account_config']['exe_path']
    if not os.path.exists(exe_path):
        print(f"\n❌ 同花顺客户端文件不存在: {exe_path}")
        print("请检查配置文件中的exe_path路径是否正确")
        return False
    
    print(f"\n✅ 同花顺客户端文件存在")
    
    # 创建交易实例
    trader = ThsTrader(config)
    
    print("\n准备启动同花顺客户端...")
    print("⚠️  注意事项:")
    print("1. 程序会启动同花顺客户端")
    print("2. 请在客户端中手动输入您的账号密码")
    print("3. 如果有验证码，请手动输入")
    print("4. 登录成功后，程序会自动检测连接状态")
    
    input("\n按回车键开始启动同花顺客户端...")
    
    # 尝试连接
    print("\n正在启动同花顺客户端...")
    success = trader.login()
    
    if success:
        print("\n🎉 连接成功！")
        
        # 测试基本功能
        print("\n测试基本功能...")
        
        # 测试获取账户信息
        try:
            print("正在获取账户信息...")
            account_info = trader.get_account_info()
            
            if account_info:
                print("✅ 账户信息获取成功")
                
                balance = account_info.get('balance')
                position = account_info.get('position')
                
                if balance:
                    print("💰 资金信息:")
                    if isinstance(balance, dict):
                        for key, value in balance.items():
                            print(f"    {key}: {value}")
                    else:
                        print(f"    {balance}")
                else:
                    print("⚠️  资金信息不可用")
                
                if position:
                    print(f"📈 持仓信息: {len(position)} 只股票")
                    if isinstance(position, list) and position:
                        print("持仓详情:")
                        for i, pos in enumerate(position[:3]):  # 只显示前3只
                            print(f"    {i+1}. {pos}")
                else:
                    print("📈 当前无持仓")
                    
            else:
                print("⚠️  无法获取账户信息")
                
        except Exception as e:
            print(f"❌ 获取账户信息失败: {e}")
        
        # 跳过获取今日成交（避免点击当日成交）
        print("\n⚠️ 跳过获取今日成交（避免界面操作）")
        print("✅ 今日成交查询已跳过")
        
        # 测试数据源连接
        print("\n测试数据源连接...")
        try:
            data = trader.get_data_source()
            if data:
                print(f"✅ 数据源连接成功，获取到 {len(data)} 条记录")
                print("最新记录:")
                for i, record in enumerate(data[:3]):
                    print(f"    {i+1}. {record['name']}({record['code']}) - {record['bkname']}")
            else:
                print("❌ 数据源连接失败")
        except Exception as e:
            print(f"❌ 数据源测试失败: {e}")
        
        # 测试模拟买入000528
        print("\n测试模拟买入000528...")
        success = test_buy_stock_000528(trader, config['trade_config']['dry_run'])
        if success:
            print("✅ 000528模拟买入测试成功")
        else:
            print("❌ 000528模拟买入测试失败")

        # 如果是测试模式，演示其他模拟买入
        if config['trade_config']['dry_run']:
            print("\n测试其他模拟买入功能...")
            if data and len(data) > 0:
                test_stock = data[0]
                print(f"模拟买入: {test_stock['name']}({test_stock['code']})")
                print("✅ 模拟买入功能正常（测试模式）")
            else:
                print("⚠️  无数据可用于测试")
        else:
            print("\n⚠️  当前为实盘模式，跳过其他买入测试")
        
        return True
        
    else:
        print("\n❌ 连接失败")
        print("\n故障排除建议:")
        print("1. 确保同花顺客户端已正确安装")
        print("2. 检查客户端路径配置是否正确")
        print("3. 尝试以管理员权限运行程序")
        print("4. 确保没有其他同花顺进程在运行")
        print("5. 检查同花顺版本是否与easytrader兼容")
        
        return False

def check_prerequisites():
    """检查前置条件"""
    print("检查前置条件...")
    
    # 检查依赖
    try:
        import easytrader
        print("✅ easytrader 已安装")
    except ImportError:
        print("❌ easytrader 未安装")
        return False
    
    try:
        import win32api
        print("✅ pywin32 已安装")
    except ImportError:
        print("❌ pywin32 未安装")
        return False
    
    return True

def main():
    """主函数"""
    if not check_prerequisites():
        print("\n❌ 前置条件检查失败，请先安装必要的依赖包")
        return
    
    success = manual_login_test()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 手动登录测试成功！")
        print("✅ 同花顺客户端连接正常")
        print("✅ 基本功能可用")
        print("\n现在您可以:")
        print("1. 运行完整的交易系统: python start_trading.py")
        print("2. 查看使用示例: python example_usage.py")
        print("3. 运行监控系统进行自动交易")
    else:
        print("❌ 手动登录测试失败")
        print("请根据上述建议解决问题后重试")

if __name__ == '__main__':
    main()
