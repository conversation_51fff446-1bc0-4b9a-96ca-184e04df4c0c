"""
同花顺连接诊断工具
帮助诊断连接问题
"""
import os
import sys
import subprocess
import time
import psutil

def check_python_architecture():
    """检查Python架构"""
    print("=== Python 架构检查 ===")
    import platform
    print(f"Python 版本: {sys.version}")
    print(f"Python 架构: {platform.architecture()[0]}")
    print(f"系统架构: {platform.machine()}")
    
    if platform.architecture()[0] == '64bit':
        print("⚠️  当前使用64位Python，可能与32位同花顺客户端不兼容")
        print("建议: 如果同花顺是32位，考虑使用32位Python")
    else:
        print("✅ 使用32位Python")

def check_ths_process():
    """检查同花顺进程"""
    print("\n=== 同花顺进程检查 ===")
    
    ths_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'exe']):
        try:
            if proc.info['name'] and '同花顺' in proc.info['name']:
                ths_processes.append(proc.info)
            elif proc.info['exe'] and 'xiadan' in proc.info['exe'].lower():
                ths_processes.append(proc.info)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if ths_processes:
        print(f"发现 {len(ths_processes)} 个同花顺相关进程:")
        for proc in ths_processes:
            print(f"  PID: {proc['pid']}, 名称: {proc['name']}, 路径: {proc['exe']}")
        print("⚠️  建议关闭所有同花顺进程后重试")
        return True
    else:
        print("✅ 未发现同花顺进程")
        return False

def check_ths_executable(exe_path):
    """检查同花顺可执行文件"""
    print(f"\n=== 同花顺可执行文件检查 ===")
    print(f"配置路径: {exe_path}")
    
    if not os.path.exists(exe_path):
        print("❌ 文件不存在")
        
        # 尝试查找常见路径
        common_paths = [
            r"C:\同花顺\xiadan.exe",
            r"C:\Program Files\同花顺\xiadan.exe",
            r"C:\Program Files (x86)\同花顺\xiadan.exe",
            r"D:\同花顺\xiadan.exe",
            r"D:\同花顺软件\同花顺\xiadan.exe"
        ]
        
        print("尝试查找常见路径:")
        for path in common_paths:
            if os.path.exists(path):
                print(f"✅ 找到: {path}")
            else:
                print(f"❌ 不存在: {path}")
        
        return False
    else:
        print("✅ 文件存在")
        
        # 检查文件属性
        stat = os.stat(exe_path)
        print(f"文件大小: {stat.st_size} 字节")
        print(f"修改时间: {time.ctime(stat.st_mtime)}")
        
        return True

def test_manual_start(exe_path):
    """测试手动启动同花顺"""
    print(f"\n=== 测试手动启动同花顺 ===")
    
    if not os.path.exists(exe_path):
        print("❌ 可执行文件不存在，跳过测试")
        return False
    
    try:
        print("尝试启动同花顺客户端...")
        process = subprocess.Popen([exe_path], shell=True)
        print(f"✅ 启动成功，进程ID: {process.pid}")
        
        # 等待几秒让程序启动
        time.sleep(5)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ 进程正在运行")
            
            # 尝试终止进程
            try:
                process.terminate()
                process.wait(timeout=10)
                print("✅ 进程已终止")
            except subprocess.TimeoutExpired:
                process.kill()
                print("⚠️  强制终止进程")
            
            return True
        else:
            print(f"❌ 进程已退出，返回码: {process.returncode}")
            return False
            
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

def test_easytrader_connection(exe_path):
    """测试easytrader连接"""
    print(f"\n=== 测试 EasyTrader 连接 ===")
    
    try:
        import easytrader
        print("✅ easytrader 导入成功")
        
        # 创建trader实例
        trader = easytrader.use('ths')
        print("✅ 创建trader实例成功")
        
        # 尝试连接（不指定路径）
        print("尝试默认连接...")
        try:
            trader.connect()
            print("✅ 默认连接成功")
            return True
        except Exception as e:
            print(f"❌ 默认连接失败: {e}")
        
        # 尝试连接（指定路径）
        if exe_path and os.path.exists(exe_path):
            print(f"尝试指定路径连接: {exe_path}")
            try:
                trader.connect(exe_path=exe_path)
                print("✅ 指定路径连接成功")
                return True
            except Exception as e:
                print(f"❌ 指定路径连接失败: {e}")
        
        return False
        
    except ImportError:
        print("❌ easytrader 未安装")
        return False
    except Exception as e:
        print(f"❌ easytrader 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("同花顺连接诊断工具")
    print("=" * 60)
    
    # 获取配置
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from config import get_config
        config = get_config()
        exe_path = config['account_config']['exe_path']
    except Exception as e:
        print(f"❌ 无法加载配置: {e}")
        exe_path = r"D:\同花顺软件\同花顺\xiadan.exe"  # 使用默认路径
    
    # 执行各项检查
    check_python_architecture()
    has_process = check_ths_process()
    has_executable = check_ths_executable(exe_path)
    
    if has_process:
        print("\n⚠️  检测到同花顺进程正在运行")
        print("建议先关闭所有同花顺进程，然后重新运行测试")
        return
    
    if has_executable:
        manual_start_ok = test_manual_start(exe_path)
        easytrader_ok = test_easytrader_connection(exe_path)
    else:
        print("\n❌ 同花顺可执行文件不存在，无法进行进一步测试")
        manual_start_ok = False
        easytrader_ok = False
    
    # 总结
    print("\n" + "=" * 60)
    print("诊断总结:")
    print(f"  Python架构: {'64位' if sys.maxsize > 2**32 else '32位'}")
    print(f"  同花顺进程: {'运行中' if has_process else '未运行'}")
    print(f"  可执行文件: {'存在' if has_executable else '不存在'}")
    print(f"  手动启动: {'成功' if manual_start_ok else '失败'}")
    print(f"  EasyTrader连接: {'成功' if easytrader_ok else '失败'}")
    
    if easytrader_ok:
        print("\n🎉 诊断成功！EasyTrader可以正常连接同花顺")
    else:
        print("\n❌ 诊断失败，建议:")
        print("1. 确保同花顺客户端路径正确")
        print("2. 尝试以管理员权限运行")
        print("3. 检查同花顺版本是否兼容")
        print("4. 考虑使用32位Python（如果同花顺是32位）")

if __name__ == '__main__':
    main()
