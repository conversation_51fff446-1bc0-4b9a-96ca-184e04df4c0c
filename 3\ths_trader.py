"""
同花顺交易接口
基于easytrader实现的自动交易系统
"""
import easytrader
import requests
import time
import json
import logging

# 验证码弹窗处理交由 5/worker.py 或 5/app.py 侧完成，避免跨目录导入问题
from datetime import datetime
from typing import Dict, List, Optional, Any
import threading


class ThsTrader:
    """同花顺交易接口类"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化交易接口

        Args:
            config: 配置字典，包含以下字段：
                - data_source_url: 数据源URL
                - token: API token
                - account_config: 账户配置
                - trade_config: 交易配置
        """
        self.config = config
        self.data_source_url = config.get('data_source_url')
        self.token = config.get('token')
        self.account_config = config.get('account_config', {})
        self.trade_config = config.get('trade_config', {})

        # 交易客户端
        self.trader = None
        self.is_logged_in = False

        # 数据监控
        self.last_data_ids = set()
        self.monitoring = False
        self.monitor_thread = None

        # 日志配置
        log_file = 'trading.log'
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def login(self) -> bool:
        """
        登录同花顺客户端（手动登录模式）

        Returns:
            bool: 登录是否成功
        """
        try:
            # 使用easytrader连接同花顺
            self.trader = easytrader.use('ths')

            # 手动登录模式 - 需要用户手动在客户端中输入账号密码
            self.logger.info("启动同花顺客户端，请手动登录...")
            print("\n" + "="*50)
            print("🔔 重要提示:")
            print("1. 程序将启动同花顺客户端")
            print("2. 请在弹出的客户端窗口中手动输入账号密码")
            print("3. 登录成功后，程序将自动检测并连接")
            print("4. 如果出现验证码，请手动输入")
            print("="*50)

            # 使用connect方法连接（不传递账号密码）
            exe_path = self.account_config.get('exe_path')
            if exe_path:
                self.logger.info(f"使用指定路径启动同花顺: {exe_path}")
                self.trader.connect(exe_path=exe_path)
            else:
                self.logger.info("使用默认路径启动同花顺")
                self.trader.connect()

            self.is_logged_in = True
            self.logger.info("同花顺客户端连接成功")
            print("✅ 连接成功！可以开始交易了。")
            return True

        except Exception as e:
            self.logger.error(f"连接失败: {e}")
            print(f"❌ 连接失败: {e}")
            print("\n可能的解决方案:")
            print("1. 确保同花顺客户端路径正确")
            print("2. 以管理员权限运行程序")
            print("3. 关闭已运行的同花顺进程后重试")
            print("4. 检查同花顺版本是否兼容easytrader")
            return False

    def get_data_source(self) -> Optional[List[Dict]]:
        """
        获取数据源信息

        Returns:
            Optional[List[Dict]]: 数据列表或None
        """
        try:
            url = f"{self.data_source_url}?Token={self.token}"
            response = requests.get(url, timeout=10)
            response.raise_for_status()

            data = response.json()
            if data.get('Code') == '200':
                return data.get('Data', [])
            else:
                self.logger.error(f"数据源返回错误: {data.get('Msg')}")
                return None

        except Exception as e:
            self.logger.error(f"获取数据源失败: {e}")
            return None

    def check_new_records(self) -> List[Dict]:
        """
        检查新记录

        Returns:
            List[Dict]: 新记录列表
        """
        data = self.get_data_source()
        if not data:
            return []

        new_records = []
        current_ids = set()

        for record in data:
            record_id = record.get('id')
            current_ids.add(record_id)

            # 如果是新记录
            if record_id not in self.last_data_ids:
                new_records.append(record)

        # 更新已知ID集合
        self.last_data_ids = current_ids

        if new_records:
            self.logger.info(f"发现 {len(new_records)} 条新记录")

        return new_records

    def buy_stock(self, stock_code: str, stock_name: str, amount: Optional[int] = None, price: Optional[float] = None) -> bool:
        """
        买入股票

        Args:
            stock_code: 股票代码
            stock_name: 股票名称
            amount: 买入数量（手）
            price: 买入价格，None表示市价

        Returns:
            bool: 是否成功下单
        """
        if not self.is_logged_in:
            self.logger.error("未登录，无法下单")
            return False

        try:
            # 使用配置中的默认参数
            buy_amount = amount or self.trade_config.get('default_amount', 100)

            self.logger.info(f"准备买入: {stock_name}({stock_code})")
            self.logger.info(f"买入数量: {buy_amount}手")
            self.logger.info(f"买入价格: {price if price else '市价'}")

            # 尝试多种买入方法
            result = None

            # 方法1: 使用easytrader的buy方法
            try:
                if price:
                    # 限价买入
                    result = self.trader.buy(stock_code, price=price, amount=buy_amount)
                    self.logger.info(f"使用限价买入方法")
                else:
                    # 市价买入
                    result = self.trader.market_buy(stock_code, amount=buy_amount)
                    self.logger.info(f"使用市价买入方法")
            except Exception as e1:
                self.logger.warning(f"easytrader买入方法失败: {e1}")

                # 如果是窗口句柄问题，重连一次再重试
                try:
                    if 'window handle' in str(e1).lower():
                        self.logger.info("检测到窗口句柄问题，尝试重新连接同花顺后重试一次...")
                        self.login()
                        if price:
                            result = self.trader.buy(stock_code, price=price, amount=buy_amount)
                        else:
                            result = self.trader.market_buy(stock_code, amount=buy_amount)
                        self.logger.info("重试下单已完成")
                except Exception as e1_retry:
                    self.logger.warning(f"重连后下单仍失败: {e1_retry}")

                # 方法2: 尝试直接调用交易接口
                try:
                    if not result and hasattr(self.trader, 'buy'):
                        # 部分 easytrader 版本此接口不可用，保留以兼容
                        result = self.trader.buy(
                            security=stock_code,
                            price=price if price is not None else 0,
                            amount=buy_amount
                        )
                        self.logger.info(f"使用直接接口买入")
                except Exception as e2:
                    self.logger.warning(f"直接接口买入失败: {e2}")

                    # 方法3: 手动提示用户
                    self.logger.error(f"自动买入失败，请手动在同花顺中买入:")
                    self.logger.error(f"  股票代码: {stock_code}")
                    self.logger.error(f"  股票名称: {stock_name}")
                    self.logger.error(f"  买入数量: {buy_amount}手")
                    self.logger.error(f"  买入价格: {price if price is not None else '市价'}")

                    print(f"\n" + "="*50)
                    print(f"🔔 请手动买入股票:")
                    print(f"📊 股票代码: {stock_code}")
                    print(f"📈 股票名称: {stock_name}")
                    print(f"💰 买入价格: {price if price is not None else '市价'}")
                    print(f"📦 买入数量: {buy_amount}手")
                    total_str = f"{price * buy_amount * 100}元" if price is not None else "未知(市价)"
                    print(f"💵 总金额: {total_str}")
                    print(f"="*50)

                    # 返回True表示已提示用户手动操作
                    return True

            if result:
                self.logger.info(f"买入订单提交成功: {stock_name}({stock_code}), 数量: {buy_amount}手")
                self.logger.info(f"订单结果: {result}")
                return True
            else:
                self.logger.error(f"买入失败，未收到有效响应")
                return False

        except Exception as e:
            self.logger.error(f"买入过程异常 {stock_name}({stock_code}): {e}")

            # 异常情况下也提示手动操作
            print(f"\n" + "="*50)
            print(f"❌ 自动买入异常，请手动买入:")
            print(f"📊 股票代码: {stock_code}")
            print(f"📈 股票名称: {stock_name}")
            print(f"💰 买入价格: {price}元")
            print(f"📦 买入数量: {amount}手")
            print(f"="*50)

            return False

    def sell_stock(self, stock_code: str, stock_name: str, amount: Optional[int] = None, price: Optional[float] = None) -> bool:
        """卖出股票
        Args:
            stock_code: 股票代码
            stock_name: 股票名称
            amount: 卖出数量（手）
            price: 卖出价格，None表示市价
        Returns:
            bool: 是否成功下单
        """
        if not self.is_logged_in:
            self.logger.error("未登录，无法下单")
            return False
        try:
            sell_amount = amount or self.trade_config.get('default_amount', 100)
            self.logger.info(f"准备卖出: {stock_name}({stock_code}) {sell_amount}手 价格: {price if price else '市价'}")
            result = None
            try:
                if price:
                    result = self.trader.sell(stock_code, price=price, amount=sell_amount)
                    self.logger.info("使用限价卖出方法")
                else:
                    if hasattr(self.trader, 'market_sell'):
                        result = self.trader.market_sell(stock_code, amount=sell_amount)
                        self.logger.info("使用市价卖出方法")
            except Exception as e1:
                self.logger.warning(f"easytrader卖出方法失败: {e1}")
                try:
                    if hasattr(self.trader, 'sell'):
                        result = self.trader.sell(security=stock_code, price=price, amount=sell_amount)
                        self.logger.info("使用直接接口卖出")
                except Exception as e2:
                    self.logger.error(f"直接接口卖出失败: {e2}")
                    return False
            if result:
                self.logger.info(f"卖出订单提交成功: {stock_name}({stock_code}), 数量: {sell_amount}手 结果: {result}")
                return True
            else:
                self.logger.error("卖出失败，未收到有效响应")
                return False
        except Exception as e:
            self.logger.error(f"卖出过程异常 {stock_name}({stock_code}): {e}")
            return False

    def get_position(self) -> Optional[List[Dict]]:
        """获取持仓(F6)"""
        if not self.is_logged_in:
            return None
        try:
            if hasattr(self.trader, 'position'):
                return self.trader.position
            return None
        except Exception as e:
            self.logger.error(f"获取持仓失败: {e}")
            return None

    def get_today_entrusts(self) -> Optional[List[Dict]]:
        """获取当日委托(F8)"""
        if not self.is_logged_in:
            return None
        try:
            if hasattr(self.trader, 'today_entrusts'):
                return self.trader.today_entrusts
            return None
        except Exception as e:
            self.logger.error(f"获取当日委托失败: {e}")
            return None

    def get_balance(self) -> Optional[Dict]:
        """获取资金情况"""
        if not self.is_logged_in:
            return None
        try:
            if hasattr(self.trader, 'balance'):
                return self.trader.balance
            return None
        except Exception as e:
            self.logger.error(f"获取资金失败: {e}")
            return None


    def process_new_record(self, record: Dict) -> None:
        """
        处理新记录，触发买入

        Args:
            record: 新记录数据
        """
        stock_code = record.get('code')
        stock_name = record.get('name')
        bk_name = record.get('bkname')
        add_time = record.get('addtime')

        self.logger.info(f"处理新记录: {stock_name}({stock_code}) - 板块: {bk_name} - 时间: {add_time}")

        # 检查是否在交易时间
        if not self.is_trading_time():
            self.logger.warning("当前不在交易时间，跳过买入")
            return

        # 检查股票代码格式
        if not self.is_valid_stock_code(stock_code):
            self.logger.warning(f"无效的股票代码: {stock_code}")
            return

        # 执行买入
        success = self.buy_stock(stock_code, stock_name)
        if success:
            self.logger.info(f"成功触发买入: {stock_name}({stock_code})")
        else:
            self.logger.error(f"买入失败: {stock_name}({stock_code})")

    def is_trading_time(self) -> bool:
        """
        检查是否在交易时间内

        Returns:
            bool: 是否在交易时间
        """
        now = datetime.now()
        current_time = now.time()
        weekday = now.weekday()

        # 周末不交易
        if weekday >= 5:
            return False

        # 交易时间: 9:30-11:30, 13:00-15:00
        morning_start = datetime.strptime("09:30", "%H:%M").time()
        morning_end = datetime.strptime("11:30", "%H:%M").time()
        afternoon_start = datetime.strptime("13:00", "%H:%M").time()
        afternoon_end = datetime.strptime("15:00", "%H:%M").time()

        return (morning_start <= current_time <= morning_end) or \
               (afternoon_start <= current_time <= afternoon_end)

    def is_valid_stock_code(self, code: str) -> bool:
        """
        验证股票代码格式

        Args:
            code: 股票代码

        Returns:
            bool: 是否有效
        """
        if not code or len(code) != 6:
            return False

        # 检查是否为数字
        return code.isdigit()

    def start_monitoring(self, interval: int = 5) -> None:
        """
        开始监控数据源

        Args:
            interval: 监控间隔（秒）
        """
        if self.monitoring:
            self.logger.warning("监控已在运行中")
            return

        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        self.logger.info(f"开始监控数据源，间隔: {interval}秒")

    def stop_monitoring(self) -> None:
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=10)
        self.logger.info("停止监控数据源")

    def _monitor_loop(self, interval: int) -> None:
        """
        监控循环

        Args:
            interval: 监控间隔
        """
        # 初始化已知记录
        initial_data = self.get_data_source()
        if initial_data:
            self.last_data_ids = {record.get('id') for record in initial_data}
            self.logger.info(f"初始化完成，已知记录数: {len(self.last_data_ids)}")

        while self.monitoring:
            try:
                new_records = self.check_new_records()
                for record in new_records:
                    self.process_new_record(record)

                time.sleep(interval)

            except Exception as e:
                self.logger.error(f"监控循环出错: {e}")
                time.sleep(interval)

    def get_account_info(self) -> Optional[Dict]:
        """
        获取账户信息

        Returns:
            Optional[Dict]: 账户信息或None
        """
        if not self.is_logged_in:
            return None

        try:
            # 尝试获取资金信息
            balance = None
            if hasattr(self.trader, 'balance'):
                balance = self.trader.balance

            # 尝试获取持仓信息
            position = None
            if hasattr(self.trader, 'position'):
                position = self.trader.position

            return {
                'balance': balance,
                'position': position
            }
        except Exception as e:
            self.logger.error(f"获取账户信息失败: {e}")
            return None

    def get_today_trades(self) -> Optional[List[Dict]]:
        """
        获取今日成交记录（已禁用，避免点击当日成交）

        Returns:
            Optional[List[Dict]]: 成交记录或None
        """
        # 直接返回空列表，避免点击同花顺界面的"当日成交"
        self.logger.info("跳过获取今日成交记录（避免界面操作）")
        return []
