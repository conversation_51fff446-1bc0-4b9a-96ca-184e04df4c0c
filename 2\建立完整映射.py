#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
建立完整的可转债-正股映射关系

结合真实可转债列表和映射文件，建立完整的双向映射关系
"""

import pandas as pd
import re
from datetime import datetime
from typing import Dict, List, Tuple


class CompleteBondStockMapping:
    """完整可转债正股映射器"""
    
    def __init__(self):
        """初始化映射器"""
        self.bond_to_stock = {}  # 可转债 -> 正股
        self.stock_to_bonds = {}  # 正股 -> 可转债列表
        self.bond_info = {}      # 可转债详细信息
        
    def load_mapping_file(self, mapping_file: str) -> pd.DataFrame:
        """
        加载映射文件
        
        Args:
            mapping_file: 映射文件路径
            
        Returns:
            pd.DataFrame: 映射数据
        """
        try:
            df = pd.read_csv(mapping_file, encoding='utf-8')
            print(f"加载映射文件: {len(df)} 条记录")
            return df
        except Exception as e:
            print(f"加载映射文件失败: {e}")
            return pd.DataFrame()
    
    def load_bond_list(self, bond_file: str) -> pd.DataFrame:
        """
        加载可转债列表
        
        Args:
            bond_file: 可转债文件路径
            
        Returns:
            pd.DataFrame: 可转债数据
        """
        try:
            df = pd.read_csv(bond_file, encoding='utf-8-sig')
            print(f"加载可转债列表: {len(df)} 条记录")
            return df
        except Exception as e:
            print(f"加载可转债列表失败: {e}")
            return pd.DataFrame()
    
    def normalize_bond_code(self, bond_code: str) -> str:
        """
        标准化可转债代码

        Args:
            bond_code: 原始可转债代码

        Returns:
            str: 标准化后的代码
        """
        if not bond_code:
            return ""

        # 移除引号和空格
        code = str(bond_code).strip().replace('"', '')

        # 移除后缀 .SH 或 .SZ
        code = code.replace('.SH', '').replace('.SZ', '')

        # 确保是6位数字
        if code.isdigit() and len(code) == 6:
            return code

        return ""
    
    def normalize_stock_code(self, stock_code: str) -> str:
        """
        标准化正股代码

        Args:
            stock_code: 原始正股代码

        Returns:
            str: 标准化后的代码
        """
        if not stock_code:
            return ""

        # 转换为字符串并移除引号和空格
        code = str(stock_code).strip().replace('"', '')

        # 处理浮点数格式（如 600301.0）
        if '.' in code:
            code = code.split('.')[0]

        # 确保是6位数字
        if code.isdigit() and len(code) == 6:
            return code

        return ""
    
    def build_mapping(self, mapping_df: pd.DataFrame, bond_df: pd.DataFrame) -> Dict:
        """
        建立完整映射关系
        
        Args:
            mapping_df: 映射数据
            bond_df: 可转债数据
            
        Returns:
            Dict: 映射统计信息
        """
        print("\n开始建立映射关系...")
        
        # 1. 处理映射文件中的数据
        mapping_count = 0
        debug_count = 0
        for _, row in mapping_df.iterrows():
            stock_code = self.normalize_stock_code(row['stock_id'])
            bond_code = self.normalize_bond_code(row['bond_id'])
            bond_name = str(row['bond_name']).strip()

            # 调试前几条记录
            if debug_count < 5:
                print(f"调试: 原始股票={row['stock_id']}, 原始债券={row['bond_id']} -> 标准化债券={bond_code}, 标准化股票={stock_code}")
                debug_count += 1

            if stock_code and bond_code:
                # 建立可转债 -> 正股映射
                self.bond_to_stock[bond_code] = {
                    'stock_code': stock_code,
                    'bond_name': bond_name,
                    'source': 'mapping_file'
                }

                # 建立正股 -> 可转债映射
                if stock_code not in self.stock_to_bonds:
                    self.stock_to_bonds[stock_code] = []

                self.stock_to_bonds[stock_code].append({
                    'bond_code': bond_code,
                    'bond_name': bond_name,
                    'source': 'mapping_file'
                })

                mapping_count += 1
        
        print(f"从映射文件建立了 {mapping_count} 个映射关系")
        
        # 2. 补充可转债列表中的信息
        bond_info_count = 0
        for _, row in bond_df.iterrows():
            bond_code = str(row['bond_code']).strip()
            bond_name = str(row['bond_name']).strip()
            exchange = str(row['exchange']).strip()
            pre_close = row.get('pre_close', 0)
            
            if bond_code:
                # 保存可转债详细信息
                self.bond_info[bond_code] = {
                    'bond_name': bond_name,
                    'exchange': exchange,
                    'pre_close': pre_close,
                    'volunit': row.get('volunit', 10),
                    'decimal_point': row.get('decimal_point', 4)
                }
                bond_info_count += 1
        
        print(f"补充了 {bond_info_count} 个可转债的详细信息")
        
        # 3. 统计信息
        stats = {
            'total_bonds_in_mapping': len(self.bond_to_stock),
            'total_stocks_in_mapping': len(self.stock_to_bonds),
            'total_bonds_with_info': len(self.bond_info),
            'bonds_with_both': len(set(self.bond_to_stock.keys()) & set(self.bond_info.keys())),
            'bonds_only_in_mapping': len(set(self.bond_to_stock.keys()) - set(self.bond_info.keys())),
            'bonds_only_in_list': len(set(self.bond_info.keys()) - set(self.bond_to_stock.keys()))
        }
        
        return stats
    
    def query_stock_by_bond(self, bond_code: str) -> Dict:
        """
        根据可转债代码查询正股
        
        Args:
            bond_code: 可转债代码
            
        Returns:
            Dict: 查询结果
        """
        result = {
            'bond_code': bond_code,
            'found': False,
            'stock_info': None,
            'bond_info': None
        }
        
        # 查找正股信息
        if bond_code in self.bond_to_stock:
            result['found'] = True
            result['stock_info'] = self.bond_to_stock[bond_code].copy()
        
        # 补充可转债详细信息
        if bond_code in self.bond_info:
            result['bond_info'] = self.bond_info[bond_code].copy()
        
        return result
    
    def query_bonds_by_stock(self, stock_code: str) -> Dict:
        """
        根据正股代码查询可转债
        
        Args:
            stock_code: 正股代码
            
        Returns:
            Dict: 查询结果
        """
        result = {
            'stock_code': stock_code,
            'found': False,
            'bonds': []
        }
        
        if stock_code in self.stock_to_bonds:
            result['found'] = True
            
            for bond_info in self.stock_to_bonds[stock_code]:
                bond_code = bond_info['bond_code']
                
                # 补充详细信息
                enhanced_info = bond_info.copy()
                if bond_code in self.bond_info:
                    enhanced_info.update(self.bond_info[bond_code])
                
                result['bonds'].append(enhanced_info)
        
        return result
    
    def export_complete_mapping(self, filename: str = None) -> str:
        """
        导出完整映射关系
        
        Args:
            filename: 文件名
            
        Returns:
            str: 导出文件路径
        """
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"完整可转债正股映射_{timestamp}.csv"
        
        try:
            # 准备导出数据
            export_data = []
            
            for bond_code, stock_info in self.bond_to_stock.items():
                row = {
                    'bond_code': bond_code,
                    'stock_code': stock_info['stock_code'],
                    'bond_name': stock_info['bond_name'],
                    'source': stock_info['source']
                }
                
                # 补充可转债详细信息
                if bond_code in self.bond_info:
                    bond_detail = self.bond_info[bond_code]
                    row.update({
                        'exchange': bond_detail['exchange'],
                        'pre_close': bond_detail['pre_close'],
                        'volunit': bond_detail['volunit'],
                        'decimal_point': bond_detail['decimal_point']
                    })
                else:
                    row.update({
                        'exchange': '',
                        'pre_close': 0,
                        'volunit': 0,
                        'decimal_point': 0
                    })
                
                export_data.append(row)
            
            # 导出到CSV
            df = pd.DataFrame(export_data)
            df = df.sort_values(['exchange', 'bond_code'])  # 按交易所和代码排序
            
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"✓ 完整映射已导出到: {filename}")
            
            return filename
        
        except Exception as e:
            print(f"✗ 导出失败: {e}")
            return ""
    
    def display_statistics(self, stats: Dict):
        """显示统计信息"""
        print(f"\n{'='*60}")
        print(f"完整映射关系统计")
        print(f"{'='*60}")
        print(f"映射文件中的可转债数量: {stats['total_bonds_in_mapping']}")
        print(f"映射文件中的正股数量: {stats['total_stocks_in_mapping']}")
        print(f"可转债列表中的数量: {stats['total_bonds_with_info']}")
        print(f"同时存在于两个文件的可转债: {stats['bonds_with_both']}")
        print(f"仅在映射文件中的可转债: {stats['bonds_only_in_mapping']}")
        print(f"仅在可转债列表中的可转债: {stats['bonds_only_in_list']}")
        
        # 计算覆盖率
        if stats['total_bonds_with_info'] > 0:
            coverage = (stats['bonds_with_both'] / stats['total_bonds_with_info']) * 100
            print(f"映射覆盖率: {coverage:.1f}%")


def main():
    """主函数"""
    print("建立完整的可转债-正股映射关系")
    print("=" * 50)
    
    try:
        # 初始化映射器
        mapper = CompleteBondStockMapping()
        
        # 加载数据文件
        mapping_df = mapper.load_mapping_file("iquant_stock_bond.csv")
        bond_df = mapper.load_bond_list("修复价格的可转债列表.csv")
        
        if mapping_df.empty or bond_df.empty:
            print("数据文件加载失败")
            return
        
        # 建立映射关系
        stats = mapper.build_mapping(mapping_df, bond_df)
        
        # 显示统计信息
        mapper.display_statistics(stats)
        
        # 测试查询功能
        print(f"\n{'='*60}")
        print(f"测试查询功能")
        print(f"{'='*60}")
        
        # 测试1: 可转债查正股
        test_bonds = ['128136', '110060', '123001']
        for bond_code in test_bonds:
            result = mapper.query_stock_by_bond(bond_code)
            if result['found']:
                stock_info = result['stock_info']
                print(f"✓ {bond_code}({stock_info['bond_name']}) -> {stock_info['stock_code']}")
            else:
                print(f"✗ {bond_code} 未找到对应正股")
        
        # 测试2: 正股查可转债
        test_stocks = ['002475', '600326', '600036']
        for stock_code in test_stocks:
            result = mapper.query_bonds_by_stock(stock_code)
            if result['found']:
                bonds = result['bonds']
                bond_names = [f"{b['bond_code']}({b['bond_name']})" for b in bonds]
                print(f"✓ {stock_code} -> {', '.join(bond_names)}")
            else:
                print(f"✗ {stock_code} 未找到对应可转债")
        
        # 导出完整映射
        print(f"\n{'='*60}")
        print(f"导出完整映射")
        print(f"{'='*60}")
        output_file = mapper.export_complete_mapping()
        
        if output_file:
            print(f"映射关系建立完成！")
            print(f"导出文件: {output_file}")
        
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
