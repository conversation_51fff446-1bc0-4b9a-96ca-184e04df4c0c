#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
获取所有沪深可转债列表

通过 mootdx 接口获取所有在交易的可转债股票列表
"""

import pandas as pd
from datetime import datetime
from typing import List, Dict

try:
    from mootdx.quotes import Quotes
    MOOTDX_AVAILABLE = True
except ImportError:
    print("错误: 无法导入 mootdx 库")
    print("请安装: pip install mootdx")
    MOOTDX_AVAILABLE = False


class BondListFetcher:
    """可转债列表获取器"""
    
    def __init__(self):
        """初始化"""
        if not MOOTDX_AVAILABLE:
            raise ImportError("mootdx 库未安装")
        
        self.client = None
        self.connected = False
        self._connect()
    
    def _connect(self):
        """连接数据服务器"""
        try:
            print("正在连接数据服务器...")
            self.client = Quotes.factory(market='std', timeout=30)
            self.connected = True
            print("✓ 连接成功")
        except Exception as e:
            print(f"✗ 连接失败: {e}")
            self.connected = False
            raise ConnectionError(f"无法连接到数据服务器: {e}")
    
    def get_all_stocks_list(self, market=0) -> pd.DataFrame:
        """
        获取所有股票列表
        
        Args:
            market: 0=深圳, 1=上海
            
        Returns:
            pd.DataFrame: 股票列表
        """
        if not self.connected:
            raise ConnectionError("未连接到数据服务器")
        
        try:
            print(f"正在获取{'上海' if market == 1 else '深圳'}市场股票列表...")
            
            # 使用 mootdx 的 stocks 方法获取股票列表
            stocks = self.client.stocks(market=market)
            
            if not stocks.empty:
                print(f"✓ 获取到 {len(stocks)} 只股票")
                return stocks
            else:
                print("✗ 未获取到股票列表")
                return pd.DataFrame()
        
        except Exception as e:
            print(f"✗ 获取股票列表失败: {e}")
            return pd.DataFrame()
    
    def filter_convertible_bonds(self, stocks_df: pd.DataFrame) -> pd.DataFrame:
        """
        从股票列表中筛选出可转债
        
        Args:
            stocks_df: 股票列表DataFrame
            
        Returns:
            pd.DataFrame: 可转债列表
        """
        if stocks_df.empty:
            return pd.DataFrame()
        
        print("正在筛选可转债...")
        
        # 检查DataFrame的列名
        print(f"股票列表列名: {list(stocks_df.columns)}")
        
        # 根据代码筛选可转债
        bonds = []
        
        for _, row in stocks_df.iterrows():
            code = str(row.get('code', ''))
            name = str(row.get('name', ''))
            
            # 判断是否为可转债
            if self._is_convertible_bond(code, name):
                bonds.append({
                    'bond_code': code,
                    'bond_name': name,
                    'market': row.get('market', ''),
                    'raw_data': row.to_dict()
                })
        
        if bonds:
            bonds_df = pd.DataFrame(bonds)
            print(f"✓ 筛选出 {len(bonds)} 只可转债")
            return bonds_df
        else:
            print("✗ 未找到可转债")
            return pd.DataFrame()
    
    def _is_convertible_bond(self, code: str, name: str) -> bool:
        """
        判断是否为可转债
        
        Args:
            code: 股票代码
            name: 股票名称
            
        Returns:
            bool: 是否为可转债
        """
        if not code or len(code) != 6:
            return False
        
        # 根据代码前缀判断
        bond_prefixes = ['110', '113', '118', '123', '127', '128']
        if code[:3] in bond_prefixes:
            return True
        
        # 根据名称判断
        if name and ('转债' in name or '转2' in name or 'EB' in name):
            return True
        
        return False
    
    def get_all_convertible_bonds(self) -> pd.DataFrame:
        """
        获取所有沪深可转债列表
        
        Returns:
            pd.DataFrame: 所有可转债列表
        """
        all_bonds = []
        
        # 获取深圳市场可转债
        print("\n=== 获取深圳市场可转债 ===")
        sz_stocks = self.get_all_stocks_list(market=0)
        if not sz_stocks.empty:
            sz_bonds = self.filter_convertible_bonds(sz_stocks)
            if not sz_bonds.empty:
                sz_bonds['exchange'] = '深圳'
                all_bonds.append(sz_bonds)
        
        # 获取上海市场可转债
        print("\n=== 获取上海市场可转债 ===")
        sh_stocks = self.get_all_stocks_list(market=1)
        if not sh_stocks.empty:
            sh_bonds = self.filter_convertible_bonds(sh_stocks)
            if not sh_bonds.empty:
                sh_bonds['exchange'] = '上海'
                all_bonds.append(sh_bonds)
        
        # 合并结果
        if all_bonds:
            result = pd.concat(all_bonds, ignore_index=True)
            print(f"\n✓ 总共找到 {len(result)} 只可转债")
            return result
        else:
            print("\n✗ 未找到任何可转债")
            return pd.DataFrame()
    
    def save_bonds_list(self, bonds_df: pd.DataFrame, filename: str = None) -> str:
        """
        保存可转债列表到文件
        
        Args:
            bonds_df: 可转债DataFrame
            filename: 文件名
            
        Returns:
            str: 保存的文件路径
        """
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"沪深可转债列表_{timestamp}.csv"
        
        try:
            bonds_df.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"✓ 可转债列表已保存到: {filename}")
            return filename
        except Exception as e:
            print(f"✗ 保存失败: {e}")
            return ""
    
    def display_bonds_summary(self, bonds_df: pd.DataFrame):
        """显示可转债汇总信息"""
        if bonds_df.empty:
            print("无可转债数据")
            return
        
        print(f"\n{'='*60}")
        print(f"沪深可转债汇总")
        print(f"{'='*60}")
        print(f"总数量: {len(bonds_df)} 只")
        
        # 按交易所分组
        if 'exchange' in bonds_df.columns:
            exchange_counts = bonds_df['exchange'].value_counts()
            for exchange, count in exchange_counts.items():
                print(f"{exchange}市场: {count} 只")
        
        # 按代码前缀分组
        if 'bond_code' in bonds_df.columns:
            print(f"\n按代码前缀分布:")
            prefixes = bonds_df['bond_code'].str[:3].value_counts()
            for prefix, count in prefixes.items():
                print(f"  {prefix}xxx: {count} 只")
        
        # 显示前10只
        print(f"\n前10只可转债:")
        display_cols = ['bond_code', 'bond_name', 'exchange']
        available_cols = [col for col in display_cols if col in bonds_df.columns]
        
        if available_cols:
            print(bonds_df[available_cols].head(10).to_string(index=False))
    
    def close(self):
        """关闭连接"""
        if self.client:
            try:
                self.client.close()
                print("已关闭连接")
            except:
                pass


def main():
    """主函数"""
    print("获取所有沪深可转债列表")
    print("=" * 50)
    
    try:
        # 初始化获取器
        fetcher = BondListFetcher()
        
        # 获取所有可转债
        bonds_df = fetcher.get_all_convertible_bonds()
        
        if not bonds_df.empty:
            # 显示汇总信息
            fetcher.display_bonds_summary(bonds_df)
            
            # 保存到文件
            filename = fetcher.save_bonds_list(bonds_df)
            
            print(f"\n可转债列表获取完成！")
            print(f"数据已保存到: {filename}")
        else:
            print("未获取到可转债数据")
    
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if 'fetcher' in locals():
            fetcher.close()


if __name__ == "__main__":
    main()
