"""
直接自动买入脚本
无需交互，直接执行自动买入
"""
import sys
import os
from datetime import datetime
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ths_trader import ThsTrader
from config import get_config

def direct_auto_buy(stock_code, stock_name, price, amount_hands):
    """
    直接自动买入股票
    
    Args:
        stock_code: 股票代码
        stock_name: 股票名称
        price: 买入价格
        amount_hands: 买入手数
    """
    print("🤖 直接自动买入系统")
    print("=" * 50)
    
    # 计算参数
    amount_shares = amount_hands * 100
    total_amount = price * amount_shares
    
    print(f"📊 交易参数:")
    print(f"   股票代码: {stock_code}")
    print(f"   股票名称: {stock_name}")
    print(f"   买入价格: {price}元/股")
    print(f"   买入手数: {amount_hands}手")
    print(f"   买入股数: {amount_shares}股")
    print(f"   总金额: {total_amount}元")
    
    # 获取配置
    config = get_config()
    
    # 创建交易实例
    print(f"\n🔗 连接同花顺...")
    trader = ThsTrader(config)
    
    # 登录
    if not trader.login():
        print("❌ 同花顺连接失败")
        return False
    
    print("✅ 同花顺连接成功")
    
    # 执行买入
    print(f"\n🛒 执行自动买入...")
    try:
        success = trader.buy_stock(
            stock_code=stock_code,
            stock_name=stock_name,
            amount=amount_hands,
            price=price
        )
        
        if success:
            print(f"✅ 自动买入成功!")
            print(f"   股票: {stock_name}({stock_code})")
            print(f"   价格: {price}元/股")
            print(f"   数量: {amount_shares}股")
            print(f"   金额: {total_amount}元")
            
            # 记录日志
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            log_entry = f"{timestamp} - 自动买入 {stock_name}({stock_code}) {amount_shares}股 @{price}元 总额{total_amount}元\n"
            
            with open("direct_auto_buy_log.txt", "a", encoding="utf-8") as f:
                f.write(log_entry)
            
            print(f"📝 交易日志已记录")
            return True
        else:
            print(f"❌ 自动买入失败")
            return False
            
    except Exception as e:
        print(f"❌ 自动买入异常: {e}")
        return False

def main():
    """主函数 - 直接买入000528"""
    print("🚀 启动直接自动买入...")
    
    # 直接买入000528
    success = direct_auto_buy(
        stock_code='000528',
        stock_name='柳工',
        price=8.52,
        amount_hands=1
    )
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 自动买入完成！")
        print("✅ 交易已成功执行")
        print("📋 请在同花顺中查看委托状态")
    else:
        print("❌ 自动买入失败")
        print("🔄 可以重新运行脚本重试")
    print("=" * 50)

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
    except KeyboardInterrupt:
        print("\n❌ 程序被中断")
