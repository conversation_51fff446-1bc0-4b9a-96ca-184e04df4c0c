#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
基于现有数据查找高振幅可转债

由于数据库中可转债历史数据有限，基于现有数据计算振幅
"""

import pymysql
import pymysql.cursors
import pandas as pd
from datetime import datetime, <PERSON><PERSON><PERSON>


def find_high_volatility_bonds_from_available_data():
    """基于现有数据查找高振幅可转债"""
    
    print("🔍 基于现有数据查找高振幅可转债")
    print("=" * 60)
    
    # 数据库配置
    db_config = {
        'host': '***********',
        'port': 3306,
        'user': 'iQuant',
        'password': 'NAAnwaRsb8YGN3F5',
        'database': 'iquant',
        'charset': 'utf8mb4'
    }
    
    try:
        # 连接数据库
        connection = pymysql.connect(**db_config)
        print("✓ 数据库连接成功")
        
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 查找所有有数据的可转债（1开头的6位代码）
        query = """
        SELECT symbol, 
               COUNT(*) as data_points,
               MIN(day) as start_date,
               MAX(day) as end_date,
               AVG(close) as avg_price,
               MIN(low) as min_price,
               MAX(high) as max_price,
               AVG((high - low) / close * 100) as avg_daily_range,
               MAX((high - low) / close * 100) as max_daily_range,
               STDDEV(close) as price_std
        FROM iquant_daily_price 
        WHERE symbol REGEXP '^1[0-9]{5}$'
        AND day >= DATE_SUB(CURDATE(), INTERVAL 60 DAY)
        GROUP BY symbol
        HAVING data_points >= 1
        ORDER BY avg_daily_range DESC
        """
        
        cursor.execute(query)
        results = cursor.fetchall()
        
        print(f"✓ 找到 {len(results)} 只有数据的可转债")
        
        if not results:
            print("⚠️  没有找到可转债数据")
            return
        
        # 处理结果
        high_volatility_bonds = []
        
        for row in results:
            symbol = row['symbol']
            avg_daily_range = row['avg_daily_range'] or 0
            max_daily_range = row['max_daily_range'] or 0
            
            # 计算价格区间振幅
            if row['avg_price'] and float(row['avg_price']) > 0:
                price_range_pct = ((float(row['max_price']) - float(row['min_price'])) / float(row['avg_price'])) * 100
            else:
                price_range_pct = 0

            # 计算价格变异系数
            if row['avg_price'] and float(row['avg_price']) > 0 and row['price_std']:
                price_cv = (float(row['price_std']) / float(row['avg_price'])) * 100
            else:
                price_cv = 0
            
            bond_info = {
                'symbol': symbol,
                'data_points': row['data_points'],
                'date_range': f"{row['start_date']} ~ {row['end_date']}",
                'avg_price': float(row['avg_price']) if row['avg_price'] else 0,
                'price_range': f"{float(row['min_price']):.2f}-{float(row['max_price']):.2f}",
                'avg_daily_range': float(avg_daily_range),
                'max_daily_range': float(max_daily_range),
                'price_range_pct': price_range_pct,
                'price_cv': price_cv
            }
            
            high_volatility_bonds.append(bond_info)
        
        # 显示结果
        display_results(high_volatility_bonds)
        
        # 导出高振幅可转债代码
        export_high_volatility_codes(high_volatility_bonds)
        
        cursor.close()
        connection.close()
        print("\n✓ 数据库连接已关闭")
        
    except Exception as e:
        print(f"❌ 查找失败: {e}")
        import traceback
        traceback.print_exc()


def display_results(bonds):
    """显示结果"""
    
    print(f"\n{'='*120}")
    print("可转债振幅分析结果")
    print(f"{'='*120}")
    
    if not bonds:
        print("⚠️  没有数据")
        return
    
    # 按平均日内振幅排序
    bonds_by_daily_range = sorted(bonds, key=lambda x: x['avg_daily_range'], reverse=True)
    
    print(f"📊 按平均日内振幅排序 (前20只):")
    print(f"{'排名':<4} {'代码':<8} {'数据点':<6} {'平均日振幅%':<10} {'最大日振幅%':<10} {'区间振幅%':<10} {'价格CV%':<8} {'平均价格':<8}")
    print("-" * 120)
    
    for i, bond in enumerate(bonds_by_daily_range[:20], 1):
        print(f"{i:<4} {bond['symbol']:<8} {bond['data_points']:<6} "
              f"{bond['avg_daily_range']:<10.2f} {bond['max_daily_range']:<10.2f} "
              f"{bond['price_range_pct']:<10.2f} {bond['price_cv']:<8.2f} {bond['avg_price']:<8.2f}")
    
    # 筛选高振幅可转债
    high_volatility = [b for b in bonds if b['avg_daily_range'] >= 5.0 or b['max_daily_range'] >= 10.0]
    
    if high_volatility:
        print(f"\n🔥 高振幅可转债 (平均日振幅≥5% 或 最大日振幅≥10%):")
        print(f"找到 {len(high_volatility)} 只符合条件的可转债")
        
        for i, bond in enumerate(high_volatility, 1):
            print(f"  {i:2d}. {bond['symbol']} - 平均日振幅:{bond['avg_daily_range']:.2f}% "
                  f"最大日振幅:{bond['max_daily_range']:.2f}% 数据点:{bond['data_points']}")
    
    # 按最大日振幅排序
    bonds_by_max_range = sorted(bonds, key=lambda x: x['max_daily_range'], reverse=True)
    
    print(f"\n📈 按最大日振幅排序 (前10只):")
    for i, bond in enumerate(bonds_by_max_range[:10], 1):
        print(f"  {i:2d}. {bond['symbol']} - 最大日振幅:{bond['max_daily_range']:.2f}% "
              f"平均价格:{bond['avg_price']:.2f} 数据点:{bond['data_points']}")


def export_high_volatility_codes(bonds):
    """导出高振幅可转债代码"""
    
    # 筛选条件：平均日振幅≥3% 或 最大日振幅≥8%
    high_volatility = [b for b in bonds if b['avg_daily_range'] >= 3.0 or b['max_daily_range'] >= 8.0]
    
    if not high_volatility:
        # 如果没有符合条件的，降低标准
        high_volatility = [b for b in bonds if b['avg_daily_range'] >= 2.0 or b['max_daily_range'] >= 5.0]
    
    if not high_volatility:
        # 如果还是没有，取前20只
        high_volatility = sorted(bonds, key=lambda x: x['avg_daily_range'], reverse=True)[:20]
    
    if high_volatility:
        # 按平均日振幅排序
        sorted_bonds = sorted(high_volatility, key=lambda x: x['avg_daily_range'], reverse=True)
        
        # 提取代码
        bond_codes = [bond['symbol'] for bond in sorted_bonds]
        
        # 用空格连接
        codes_string = ' '.join(bond_codes)
        
        # 保存到文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"高振幅可转债代码_{timestamp}.txt"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(codes_string)
        
        print(f"\n📁 高振幅可转债代码已导出: {output_file}")
        print(f"📋 代码列表 ({len(bond_codes)} 只): {codes_string}")
        
        # 显示详细信息
        print(f"\n📊 导出的可转债详细信息:")
        for i, bond in enumerate(sorted_bonds, 1):
            print(f"  {i:2d}. {bond['symbol']} - 平均日振幅:{bond['avg_daily_range']:.2f}% "
                  f"最大日振幅:{bond['max_daily_range']:.2f}% 平均价格:{bond['avg_price']:.2f}")
        
        return codes_string
    else:
        print(f"\n⚠️  没有找到符合条件的高振幅可转债")
        return ""


def main():
    """主函数"""
    find_high_volatility_bonds_from_available_data()


if __name__ == "__main__":
    main()
