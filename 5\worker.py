import time
from datetime import datetime
from typing import Dict

import os
import sys

# 将当前目录与仓库3目录加入路径，便于脚本直接运行
CUR_DIR = os.path.dirname(os.path.abspath(__file__))
REPO_ROOT = os.path.dirname(CUR_DIR)
sys.path.insert(0, CUR_DIR)
sys.path.insert(0, os.path.join(REPO_ROOT, '3'))

from api_config import CFG
from queue_utils import read_queue, update_queue, append_log
from price_feed import get_sell1_price
from captcha_helper import wait_captcha_and_notify, force_close_captcha

# 复用已有交易实现
from config import get_config
from ths_trader import ThsTrader


class Worker:
    def __init__(self):
        self.trader = None
        self.ensure_login()

    def ensure_login(self):
        # 尝试多次连接，必要时回退/等待用户手动打开
        if self.trader is None or not getattr(self.trader, 'is_logged_in', False):
            attempts = 0
            while attempts < 6:
                attempts += 1
                cfg = get_config()
                exe_path = CFG.get('exe_path')
                if not exe_path or not os.path.exists(exe_path):
                    cfg['account_config']['exe_path'] = ''
                    print('ℹ️ 未配置或找不到 xiadan.exe，尝试附着已打开的客户端或默认启动')
                else:
                    cfg['account_config']['exe_path'] = exe_path
                    print(f'ℹ️ 使用配置的 xiadan.exe 路径: {exe_path}')

                self.trader = ThsTrader(cfg)
                ok = self.trader.login()

                if ok:
                    break

                # 第一次失败且配置了路径，则回退一次
                if exe_path:
                    print('⚠️ 带路径连接失败，回退为无路径附着模式再试一次...')
                    cfg['account_config']['exe_path'] = ''
                    self.trader = ThsTrader(cfg)
                    if self.trader.login():
                        break

                print('⏳ 等待 3 秒后重试连接（可手动打开并登录同花顺）...')
                time.sleep(3)

            if self.trader is None or not getattr(self.trader, 'is_logged_in', False):
                print('❌ 暂时无法连接同花顺。服务会继续运行，稍后会再次尝试连接。')

    def handle_order(self, row: Dict):
        """执行队列中的一条指令"""
        code = row['stock_no']
        name = row.get('stock_name') or code
        amount_hands = int(row['amount']) // 100 if int(row['amount']) >= 100 else 1
        operate = row['operate']
        price_str = (row.get('price') or '').strip()
        # 如果没有给定价格，取卖一价
        if price_str:
            try:
                price = float(price_str)
            except Exception:
                price = None
        else:
            price = get_sell1_price(code)  # 未提供则取卖一

        ok = False
        if operate == 'buy':
            ok = self.trader.buy_stock(code, name, amount=amount_hands, price=price)
        elif operate == 'sell':
            ok = self.trader.sell_stock(code, name, amount=amount_hands, price=price)
        else:
            # 未知操作，标记失败
            ok = False

        # 验证码弹窗提示（若出现）
        try:
            wait_captcha_and_notify(60)
        except Exception:
            pass

        # 收尾再次尝试关闭验证码（若仍在）
        try:
            force_close_captcha(2, 0.4)
        except Exception:
            pass

        # 写日志
        append_log({
            'key': row['key'],
            '委托时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            '证券代码': code,
            '证券名称': name,
            '操作': operate,
            '备注': 'OK' if ok else 'FAIL',
            '委托数量': row['amount'],
            '成交数量': '',
            '委托价格': str(price) if price is not None else '',
            '成交均价': '',
            '撤消数量': '',
            '合同编号': '',
            '策略编号': row['strategy_no'],
        })

        return ok

    def run_once(self):
        rows = read_queue()
        changed = False
        for r in rows:
            if str(r.get('status', '0')) == '0':
                ok = self.handle_order(r)
                r['status'] = 1 if ok else -1
                changed = True
                time.sleep(CFG['sleepC'])
        if changed:
            update_queue(rows)


def run_loop():
    w = Worker()
    while True:
        w.run_once()
        time.sleep(CFG['sleepB'])

