#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化可转债爬虫

使用内置库爬取东方财富可转债数据
"""

import urllib.request
import urllib.parse
import json
import csv
import time
from datetime import datetime


def get_convertible_bonds():
    """获取所有可转债数据（支持分页）"""

    # 东方财富可转债API
    url = "https://datacenter-web.eastmoney.com/api/data/v1/get"

    all_bonds = []
    page_number = 1
    page_size = 500

    while True:
        params = {
            'sortColumns': 'SECURITY_CODE',
            'sortTypes': '1',
            'pageSize': str(page_size),
            'pageNumber': str(page_number),
            'reportName': 'RPT_BOND_CB_LIST',
            'columns': 'ALL',
            'quoteColumns': '',
            'js': '',
            'source': 'WEB',
            'client': 'WEB',
            '_': str(int(time.time() * 1000))
        }
    
        # 构建URL
        query_string = urllib.parse.urlencode(params)
        full_url = f"{url}?{query_string}"

        # 设置请求头
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://data.eastmoney.com/',
            'Accept': 'application/json, text/plain, */*',
        }

        try:
            print(f"正在获取第 {page_number} 页可转债数据...")

            # 创建请求
            req = urllib.request.Request(full_url, headers=headers)

            # 发送请求
            with urllib.request.urlopen(req, timeout=30) as response:
                data = json.loads(response.read().decode('utf-8'))

            if data.get('success') and 'result' in data and 'data' in data['result']:
                page_data = data['result']['data']
                total_count = data['result'].get('count', 0)

                print(f"✓ 第 {page_number} 页获取成功: {len(page_data)} 条数据")

                if not page_data:
                    print("当前页无数据，停止获取")
                    break

                all_bonds.extend(page_data)

                # 检查是否还有更多数据
                if len(all_bonds) >= total_count:
                    print(f"已获取全部数据，总计 {len(all_bonds)} 条")
                    break

                # 调试：只在第一页显示数据结构
                if page_number == 1 and page_data:
                    print("调试：前3条数据的字段:")
                    for i, item in enumerate(page_data[:3]):
                        print(f"  第{i+1}条: {list(item.keys())[:10]}...")  # 只显示前10个字段
                        print(f"    NEWEST_PRICE: {item.get('NEWEST_PRICE')}")
                        print(f"    SECURITY_CODE: {item.get('SECURITY_CODE')}")
                        print(f"    SECURITY_NAME_ABBR: {item.get('SECURITY_NAME_ABBR')}")

                page_number += 1
                time.sleep(1)  # 避免请求过快

            else:
                print(f"第 {page_number} 页获取失败")
                break

        except Exception as e:
            print(f"✗ 获取第 {page_number} 页数据失败: {e}")
            break

    print(f"✓ 总共获取到 {len(all_bonds)} 条可转债数据")
    return all_bonds


def process_bond_data(raw_data):
    """处理可转债数据"""
    
    if not raw_data:
        return []
    
    print("正在处理数据...")
    
    # 字段映射
    field_mapping = {
        'SECURITY_CODE': '可转债代码',
        'SECURITY_NAME_ABBR': '可转债简称',
        'TRADE_MARKET_CODE': '市场',
        'NEWEST_PRICE': '最新价',
        'CHANGE_RATE': '涨跌幅',
        'VOLUME': '成交量',
        'AMOUNT': '成交额',
        'TURNOVERRATE': '换手率',
        'OPEN_PRICE': '开盘价',
        'HIGH_PRICE': '最高价',
        'LOW_PRICE': '最低价',
        'PRE_CLOSE_PRICE': '昨收价',
        'AMPLITUDE': '振幅',
        'CONVERT_STOCK_CODE': '正股代码',
        'CONVERT_STOCK_NAME': '正股简称',
        'CONVERT_STOCK_PRICE': '正股价格',
        'CONVERT_PRICE': '转股价',
        'CONVERT_VALUE': '转股价值',
        'CONVERT_PREMIUM_RATIO': '转股溢价率',
        'BOND_VALUE': '纯债价值',
        'BOND_PREMIUM_RATIO': '纯债溢价率',
        'RATING': '评级',
        'MATURITY_DATE': '到期日期',
        'REMAIN_YEAR': '剩余年限',
        'YTMRT': '到期收益率'
    }
    
    processed_data = []
    active_count = 0
    
    for item in raw_data:
        # 处理所有可转债数据
        processed_item = {}

        # 处理每个字段
        for eng_field, cn_field in field_mapping.items():
            value = item.get(eng_field)

            # 数据清洗
            if value is None or value == '-' or value == '':
                processed_item[cn_field] = ''
            elif eng_field == 'MATURITY_DATE' and isinstance(value, str) and len(value) >= 8:
                # 日期格式处理
                try:
                    processed_item[cn_field] = f"{value[:4]}-{value[4:6]}-{value[6:8]}"
                except:
                    processed_item[cn_field] = value
            else:
                processed_item[cn_field] = value

        # 添加额外信息
        processed_item['数据获取时间'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 判断交易市场
        code = processed_item.get('可转债代码', '')
        if code.startswith(('110', '113', '118')):
            processed_item['交易市场'] = '上海'
        elif code.startswith(('123', '127', '128')):
            processed_item['交易市场'] = '深圳'
        else:
            processed_item['交易市场'] = '未知'

        # 判断是否退市
        delist_date = item.get('DELIST_DATE')
        if delist_date and delist_date != '-':
            processed_item['状态'] = '已退市'
        else:
            processed_item['状态'] = '正常'

        processed_data.append(processed_item)
        active_count += 1
    
    print(f"✓ 处理完成，共 {active_count} 只可转债")
    return processed_data


def save_to_csv(data, filename=None):
    """保存数据到CSV文件"""
    
    if not data:
        print("✗ 无数据可保存")
        return ""
    
    if filename is None:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"东方财富可转债数据_{timestamp}.csv"
    
    try:
        # 获取所有字段名
        fieldnames = list(data[0].keys())
        
        # 写入CSV文件
        with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(data)
        
        print(f"✓ 数据已保存到: {filename}")
        return filename
        
    except Exception as e:
        print(f"✗ 保存失败: {e}")
        return ""


def display_summary(data):
    """显示数据汇总"""
    
    if not data:
        print("无数据")
        return
    
    print(f"\n{'='*80}")
    print(f"可转债数据汇总")
    print(f"{'='*80}")
    print(f"活跃可转债数量: {len(data)}")
    
    # 市场分布统计
    market_count = {}
    price_list = []
    change_list = []
    
    for item in data:
        # 市场统计
        market = item.get('交易市场', '未知')
        market_count[market] = market_count.get(market, 0) + 1
        
        # 价格统计
        try:
            price = float(item.get('最新价', 0))
            if price > 0:
                price_list.append(price)
        except:
            pass
        
        # 涨跌幅统计
        try:
            change = float(item.get('涨跌幅', 0))
            change_list.append(change)
        except:
            pass
    
    # 显示市场分布
    print(f"\n市场分布:")
    for market, count in market_count.items():
        print(f"  {market}: {count} 只")
    
    # 显示价格统计
    if price_list:
        print(f"\n价格统计:")
        print(f"  平均价格: {sum(price_list)/len(price_list):.2f} 元")
        print(f"  最高价格: {max(price_list):.2f} 元")
        print(f"  最低价格: {min(price_list):.2f} 元")
        print(f"  价格中位数: {sorted(price_list)[len(price_list)//2]:.2f} 元")
    
    # 显示涨跌幅统计
    if change_list:
        print(f"\n涨跌幅统计:")
        print(f"  平均涨跌幅: {sum(change_list)/len(change_list):.2f}%")
        print(f"  最大涨幅: {max(change_list):.2f}%")
        print(f"  最大跌幅: {min(change_list):.2f}%")
        
        up_count = len([x for x in change_list if x > 0])
        down_count = len([x for x in change_list if x < 0])
        flat_count = len([x for x in change_list if x == 0])
        print(f"  上涨: {up_count} 只, 下跌: {down_count} 只, 平盘: {flat_count} 只")
    
    # 显示前10条数据
    print(f"\n前10只可转债:")
    for i, item in enumerate(data[:10], 1):
        code = item.get('可转债代码', '')
        name = item.get('可转债简称', '')
        price = item.get('最新价', '')
        change = item.get('涨跌幅', '')
        market = item.get('交易市场', '')
        print(f"  {i:2d}. {code} {name} 价格:{price} 涨跌:{change}% 市场:{market}")


def main():
    """主函数"""
    print("🚀 简化可转债数据爬虫")
    print("=" * 50)
    
    try:
        # 1. 获取数据
        raw_data = get_convertible_bonds()
        
        if not raw_data:
            print("❌ 未获取到数据")
            return
        
        # 2. 处理数据
        processed_data = process_bond_data(raw_data)
        
        if not processed_data:
            print("❌ 无活跃可转债数据")
            return
        
        # 3. 显示汇总
        display_summary(processed_data)
        
        # 4. 保存数据
        filename = save_to_csv(processed_data)
        
        if filename:
            print(f"\n✅ 爬取完成！")
            print(f"📁 数据文件: {filename}")
            print(f"📊 活跃可转债数量: {len(processed_data)} 只")
        else:
            print("❌ 数据保存失败")
            
    except Exception as e:
        print(f"❌ 爬虫运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
