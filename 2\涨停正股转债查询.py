#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
涨停正股转债查询工具

查找最近10天有涨停的正股对应的可转债列表
"""

import pandas as pd
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

# 尝试导入数据库驱动
try:
    import mysql.connector
    MYSQL_CONNECTOR_AVAILABLE = True
except ImportError:
    MYSQL_CONNECTOR_AVAILABLE = False

try:
    import pymysql
    import pymysql.cursors
    PYMYSQL_AVAILABLE = True
except ImportError:
    PYMYSQL_AVAILABLE = False

MYSQL_AVAILABLE = MYSQL_CONNECTOR_AVAILABLE or PYMYSQL_AVAILABLE

if not MYSQL_AVAILABLE:
    print("警告: 未安装 mysql-connector-python 或 pymysql")
    print("请安装: pip install mysql-connector-python 或 pip install pymysql")


class LimitUpStockBondQuery:
    """涨停正股转债查询器"""
    
    def __init__(self, db_config: Dict):
        """
        初始化查询器
        
        Args:
            db_config: 数据库配置
        """
        self.db_config = db_config
        self.connection = None
        self.bond_stock_mapping = {}
        
        # 加载映射关系
        self._load_bond_stock_mapping()
    
    def _load_bond_stock_mapping(self):
        """加载可转债正股映射关系（使用东方财富终极完整数据）"""
        try:
            # 从新的东方财富终极完整可转债数据加载映射关系
            bond_df = pd.read_csv("东方财富终极完整可转债_20250807_004218.csv", encoding='utf-8-sig')

            print(f"✓ 读取可转债数据: {len(bond_df)} 条记录")

            # 只处理正常状态且有正股代码的可转债
            valid_bonds = bond_df[
                (bond_df['状态'] == '正常') &
                (bond_df['正股代码'].notna()) &
                (bond_df['正股代码'] != '') &
                (bond_df['债券代码'].notna()) &
                (bond_df['债券简称'].notna())
            ].copy()

            print(f"✓ 筛选有效可转债: {len(valid_bonds)} 条")

            for _, row in valid_bonds.iterrows():
                stock_code = str(row['正股代码']).strip()
                bond_code = str(row['债券代码']).strip()
                bond_name = str(row['债券简称']).strip()
                market = str(row.get('市场', '')).strip()

                # 获取实时价格数据
                current_price = row.get('债现价', '')
                change_rate = row.get('涨跌幅', '')
                volume = row.get('成交量', '')
                amount = row.get('成交额', '')

                # 获取转股相关数据
                convert_price = row.get('转股价', '')
                convert_value = row.get('转股价值', '')
                convert_premium = row.get('转股溢价率', '')

                # 获取其他重要信息
                rating = row.get('信用评级', '')
                maturity_date = row.get('到期日期', '')

                if stock_code not in self.bond_stock_mapping:
                    self.bond_stock_mapping[stock_code] = []

                self.bond_stock_mapping[stock_code].append({
                    'bond_code': bond_code,
                    'bond_name': bond_name,
                    'market': market,
                    'current_price': current_price,
                    'change_rate': change_rate,
                    'volume': volume,
                    'amount': amount,
                    'convert_price': convert_price,
                    'convert_value': convert_value,
                    'convert_premium': convert_premium,
                    'rating': rating,
                    'maturity_date': maturity_date,
                    'has_realtime_data': bool(current_price and current_price not in ['', '-'])
                })

            print(f"✓ 加载映射关系: {len(self.bond_stock_mapping)} 只正股对应 {len(valid_bonds)} 只可转债")

            # 统计有实时数据的可转债
            realtime_count = sum(1 for bonds in self.bond_stock_mapping.values()
                               for bond in bonds if bond['has_realtime_data'])
            print(f"✓ 其中有实时行情数据: {realtime_count} 只可转债")

        except Exception as e:
            print(f"✗ 加载映射关系失败: {e}")
            import traceback
            traceback.print_exc()
            self.bond_stock_mapping = {}
    
    def connect_database(self):
        """连接数据库"""
        if not MYSQL_AVAILABLE:
            print("✗ 数据库连接失败: 未安装MySQL驱动")
            return False

        try:
            # 优先尝试 mysql.connector
            if MYSQL_CONNECTOR_AVAILABLE:
                import mysql.connector
                self.connection = mysql.connector.connect(**self.db_config)
                self.db_type = 'mysql.connector'
            elif PYMYSQL_AVAILABLE:
                import pymysql
                self.connection = pymysql.connect(**self.db_config)
                self.db_type = 'pymysql'
            else:
                print("✗ 未找到可用的MySQL驱动")
                return False

            print(f"✓ 数据库连接成功 (使用 {self.db_type})")
            return True
        except Exception as e:
            print(f"✗ 数据库连接失败: {e}")
            return False
    
    def get_limit_up_stocks_recent_days(self, days: int = 20) -> pd.DataFrame:
        """
        获取最近N天有涨停的正股
        
        Args:
            days: 查询天数
            
        Returns:
            pd.DataFrame: 涨停股票数据
        """
        if not self.connection:
            if not self.connect_database():
                return pd.DataFrame()
        
        try:
            # 计算查询日期范围
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=days)
            
            # SQL查询：最近N天有涨停的股票
            query = """
            SELECT 
                symbol,
                day,
                close,
                pre_close,
                volume,
                limit_up,
                ROUND((close - pre_close) / pre_close * 100, 2) as change_pct
            FROM iquant_daily_price 
            WHERE day >= %s 
                AND day <= %s 
                AND limit_up = 1
            ORDER BY day DESC, symbol ASC
            """
            
            print(f"查询日期范围: {start_date} 到 {end_date}")
            
            # 执行查询
            if self.db_type == 'mysql.connector':
                # mysql.connector
                cursor = self.connection.cursor(dictionary=True)
                cursor.execute(query, (start_date, end_date))
                results = cursor.fetchall()
            else:
                # pymysql
                import pymysql.cursors
                cursor = self.connection.cursor(pymysql.cursors.DictCursor)
                cursor.execute(query, (start_date, end_date))
                results = cursor.fetchall()

            cursor.close()
            
            if results:
                df = pd.DataFrame(results)
                print(f"✓ 找到 {len(df)} 条涨停记录，涉及 {df['symbol'].nunique()} 只股票")
                return df
            else:
                print("✗ 未找到涨停记录")
                return pd.DataFrame()
                
        except Exception as e:
            print(f"✗ 查询涨停股票失败: {e}")
            return pd.DataFrame()
    
    def get_limit_up_stocks_with_bonds(self, days: int = 20) -> Dict:
        """
        获取最近N天有涨停且有对应可转债的正股
        
        Args:
            days: 查询天数
            
        Returns:
            Dict: 涨停股票及对应可转债信息
        """
        # 获取涨停股票
        limit_up_df = self.get_limit_up_stocks_recent_days(days)
        
        if limit_up_df.empty:
            return {
                'summary': {'total_limit_up': 0, 'with_bonds': 0, 'total_bonds': 0},
                'stocks_with_bonds': [],
                'stocks_without_bonds': []
            }
        
        # 分析涨停股票的可转债情况
        stocks_with_bonds = []
        stocks_without_bonds = []
        total_bonds = 0
        
        # 按股票分组
        for symbol, group in limit_up_df.groupby('symbol'):
            # 获取该股票的涨停记录
            limit_up_days = group.sort_values('day', ascending=False)
            latest_record = limit_up_days.iloc[0]
            
            stock_info = {
                'symbol': symbol,
                'latest_limit_up_date': str(latest_record['day']),
                'latest_close': float(latest_record['close']),
                'latest_change_pct': float(latest_record['change_pct']),
                'limit_up_count': len(limit_up_days),
                'limit_up_dates': [str(d) for d in limit_up_days['day'].tolist()],
                'bonds': []
            }
            
            # 查找对应的可转债
            if symbol in self.bond_stock_mapping:
                bonds = self.bond_stock_mapping[symbol]
                stock_info['bonds'] = bonds
                stocks_with_bonds.append(stock_info)
                total_bonds += len(bonds)
            else:
                stocks_without_bonds.append(stock_info)
        
        # 汇总信息
        summary = {
            'total_limit_up': len(limit_up_df['symbol'].unique()),
            'with_bonds': len(stocks_with_bonds),
            'without_bonds': len(stocks_without_bonds),
            'total_bonds': total_bonds,
            'query_days': days,
            'query_date_range': f"{limit_up_df['day'].min()} 到 {limit_up_df['day'].max()}"
        }
        
        return {
            'summary': summary,
            'stocks_with_bonds': stocks_with_bonds,
            'stocks_without_bonds': stocks_without_bonds
        }
    
    def display_limit_up_bonds_report(self, result: Dict):
        """显示涨停股票可转债报告"""
        summary = result['summary']
        stocks_with_bonds = result['stocks_with_bonds']
        stocks_without_bonds = result['stocks_without_bonds']
        
        print(f"\n{'='*80}")
        print(f"最近{summary['query_days']}天涨停正股转债分析报告")
        print(f"{'='*80}")
        print(f"查询时间范围: {summary['query_date_range']}")
        print(f"涨停股票总数: {summary['total_limit_up']} 只")
        print(f"有对应可转债: {summary['with_bonds']} 只")
        print(f"无对应可转债: {summary['without_bonds']} 只")
        print(f"对应可转债总数: {summary['total_bonds']} 只")
        
        if stocks_with_bonds:
            print(f"\n{'='*80}")
            print(f"有对应可转债的涨停股票 ({len(stocks_with_bonds)} 只)")
            print(f"{'='*80}")
            
            for i, stock in enumerate(stocks_with_bonds, 1):
                print(f"\n{i}. {stock['symbol']} - 最新涨停: {stock['latest_limit_up_date']}")
                print(f"   收盘价: {stock['latest_close']:.2f} 元")
                print(f"   涨幅: {stock['latest_change_pct']:.2f}%")
                print(f"   涨停次数: {stock['limit_up_count']} 次")
                if stock['limit_up_count'] > 1:
                    print(f"   涨停日期: {', '.join(stock['limit_up_dates'])}")
                
                print(f"   对应可转债 ({len(stock['bonds'])} 只):")
                for bond in stock['bonds']:
                    # 显示基本信息
                    market_str = f"({bond.get('market', 'N/A')})"

                    # 显示实时价格信息
                    if bond.get('has_realtime_data'):
                        current_price = bond.get('current_price', '')
                        change_rate = bond.get('change_rate', '')
                        volume = bond.get('volume', '')

                        # 处理价格显示
                        if current_price and current_price not in ['', '-'] and str(current_price) != 'nan':
                            try:
                                price_val = float(current_price)
                                price_info = f"价格:{price_val:.2f}"
                            except:
                                price_info = "价格:--"
                        else:
                            price_info = "价格:--"

                        # 处理涨跌幅显示
                        if change_rate and change_rate not in ['', '-'] and str(change_rate) != 'nan':
                            try:
                                change_val = float(change_rate)
                                price_info += f" 涨幅:{change_val:.2f}%"
                            except:
                                pass

                        # 处理成交量显示
                        if volume and volume not in ['', '-'] and str(volume) != 'nan':
                            try:
                                vol_num = float(volume)
                                if vol_num > 10000:
                                    vol_str = f"{vol_num/10000:.1f}万手"
                                else:
                                    vol_str = f"{vol_num:.0f}手"
                                price_info += f" 量:{vol_str}"
                            except:
                                pass

                        print(f"     • {bond['bond_code']} {bond['bond_name']} {market_str} [{price_info}] 🔥")

                        # 显示转股信息
                        convert_info = []

                        # 转股价值
                        if bond.get('convert_value') and bond.get('convert_value') not in ['', '-'] and str(bond.get('convert_value')) != 'nan':
                            try:
                                convert_val = float(bond['convert_value'])
                                convert_info.append(f"转股价值:{convert_val:.2f}")
                            except:
                                pass

                        # 转股溢价率
                        if bond.get('convert_premium') and bond.get('convert_premium') not in ['', '-'] and str(bond.get('convert_premium')) != 'nan':
                            try:
                                premium_val = float(bond['convert_premium'])
                                convert_info.append(f"溢价率:{premium_val:.2f}%")
                            except:
                                pass

                        # 评级
                        if bond.get('rating') and bond.get('rating') not in ['', '-']:
                            convert_info.append(f"评级:{bond['rating']}")

                        if convert_info:
                            print(f"       {' | '.join(convert_info)}")
                    else:
                        # 没有实时数据的可转债
                        rating_str = f" 评级:{bond.get('rating')}" if bond.get('rating') and bond.get('rating') not in ['', '-'] else ""
                        print(f"     • {bond['bond_code']} {bond['bond_name']} {market_str}{rating_str}")
        
        if stocks_without_bonds:
            print(f"\n{'='*80}")
            print(f"无对应可转债的涨停股票 ({len(stocks_without_bonds)} 只)")
            print(f"{'='*80}")
            
            for i, stock in enumerate(stocks_without_bonds, 1):
                print(f"{i:2d}. {stock['symbol']} - {stock['latest_limit_up_date']} "
                      f"(收盘: {stock['latest_close']:.2f}, 涨幅: {stock['latest_change_pct']:.2f}%)")
    
    def export_limit_up_bonds(self, result: Dict, filename: str = None) -> str:
        """
        导出涨停股票可转债数据
        
        Args:
            result: 查询结果
            filename: 文件名
            
        Returns:
            str: 导出文件路径
        """
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"涨停正股转债列表_{timestamp}.csv"
        
        try:
            export_data = []
            
            for stock in result['stocks_with_bonds']:
                for bond in stock['bonds']:
                    export_data.append({
                        # 正股信息
                        'stock_symbol': stock['symbol'],
                        'latest_limit_up_date': stock['latest_limit_up_date'],
                        'latest_close': stock['latest_close'],
                        'latest_change_pct': stock['latest_change_pct'],
                        'limit_up_count': stock['limit_up_count'],

                        # 可转债基本信息
                        'bond_code': bond['bond_code'],
                        'bond_name': bond['bond_name'],
                        'bond_market': bond.get('market', ''),

                        # 可转债实时行情
                        'bond_current_price': bond.get('current_price', ''),
                        'bond_change_rate': bond.get('change_rate', ''),
                        'bond_volume': bond.get('volume', ''),
                        'bond_amount': bond.get('amount', ''),

                        # 转股信息
                        'convert_price': bond.get('convert_price', ''),
                        'convert_value': bond.get('convert_value', ''),
                        'convert_premium': bond.get('convert_premium', ''),

                        # 其他信息
                        'bond_rating': bond.get('rating', ''),
                        'maturity_date': bond.get('maturity_date', ''),
                        'has_realtime_data': bond.get('has_realtime_data', False)
                    })
            
            if export_data:
                df = pd.DataFrame(export_data)
                df.to_csv(filename, index=False, encoding='utf-8-sig')
                print(f"✓ 数据已导出到: {filename}")
                return filename
            else:
                print("✗ 无数据可导出")
                return ""
                
        except Exception as e:
            print(f"✗ 导出失败: {e}")
            return ""
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            try:
                self.connection.close()
                print("✓ 数据库连接已关闭")
            except:
                pass


def main():
    """主函数"""
    print("涨停正股转债查询工具")
    print("=" * 50)
    
    # 数据库配置
    db_config = {
        'host': '***********',
        'port': 3306,
        'user': 'iQuant',
        'password': 'NAAnwaRsb8YGN3F5',
        'database': 'iquant',
        'charset': 'utf8mb4'
    }
    
    try:
        # 初始化查询器
        query_tool = LimitUpStockBondQuery(db_config)
        
        # 查询最近10天涨停股票的可转债
        print("\n查询最近10天涨停股票对应的可转债...")
        result = query_tool.get_limit_up_stocks_with_bonds(days=10)
        
        # 显示报告
        query_tool.display_limit_up_bonds_report(result)
        
        # 导出数据
        if result['stocks_with_bonds']:
            print(f"\n{'='*80}")
            print("导出数据")
            print(f"{'='*80}")
            output_file = query_tool.export_limit_up_bonds(result)
            if output_file:
                print(f"涨停股票可转债数据已导出: {output_file}")
        
        # 关闭连接
        query_tool.close()
        
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
