#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库功能测试脚本
"""

import os
import sqlite3
import json
from plate_data_crawler import PlateDataCrawler
from database_manager import DatabaseManager


def test_database_creation():
    """测试数据库创建"""
    print("🧪 测试数据库创建...")
    
    # 删除已存在的测试数据库
    test_db = "test_plate_stocks.db"
    if os.path.exists(test_db):
        os.remove(test_db)
    
    # 创建爬虫实例，应该自动创建数据库
    crawler = PlateDataCrawler(db_path=test_db)
    
    # 检查数据库文件是否创建
    if os.path.exists(test_db):
        print("✅ 数据库文件创建成功")
        
        # 检查表结构
        conn = sqlite3.connect(test_db)
        cursor = conn.cursor()
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        expected_tables = ['stocks', 'plates', 'crawl_logs']
        if all(table in tables for table in expected_tables):
            print("✅ 数据库表结构创建成功")
            print(f"📋 创建的表: {', '.join(tables)}")
        else:
            print("❌ 数据库表结构不完整")
        
        conn.close()
        
        # 清理测试数据库
        os.remove(test_db)
        return True
    else:
        print("❌ 数据库文件创建失败")
        return False


def test_data_insertion():
    """测试数据插入"""
    print("\n🧪 测试数据插入...")
    
    test_db = "test_plate_stocks.db"
    if os.path.exists(test_db):
        os.remove(test_db)
    
    crawler = PlateDataCrawler(db_path=test_db)
    
    # 模拟股票数据
    mock_stocks = [
        {
            '_id': '65467ec71a0fa8e533b7b30c',
            'ts_code': '000001.SZ',
            'act_ent_type': '地方国企',
            'act_name': '深圳市人民政府国有资产监督管理委员会',
            'area': '深圳',
            'cnspell': 'payh',
            'curr_type': 'CNY',
            'delist_date': None,
            'enname': 'Ping An Bank Co., Ltd.',
            'exchange': 'SZSE',
            'fullname': '平安银行股份有限公司',
            'industry': '银行',
            'is_hs': 'S',
            'list_date': '********',
            'list_status': 'L',
            'market': '主板',
            'name': '平安银行',
            'symbol': '000001',
            'plates': '银行/金融科技/数字货币',
            'plates_info': {
                '银行': '四大股份制银行之一',
                '金融科技': '金融科技创新领先',
                '数字货币': '数字人民币试点银行'
            },
            'T3': 1000
        },
        {
            '_id': '65467ec71a0fa8e533b7b30d',
            'ts_code': '000002.SZ',
            'act_ent_type': '地方国企',
            'act_name': '深圳市人民政府国有资产监督管理委员会',
            'area': '深圳',
            'cnspell': 'wka',
            'curr_type': 'CNY',
            'delist_date': None,
            'enname': 'China Vanke Co., Ltd.',
            'exchange': 'SZSE',
            'fullname': '万科企业股份有限公司',
            'industry': '房地产',
            'is_hs': 'S',
            'list_date': '19910129',
            'list_status': 'L',
            'market': '主板',
            'name': '万科A',
            'symbol': '000002',
            'plates': '房地产/物业管理/租赁住房',
            'plates_info': {
                '房地产': '房地产开发龙头',
                '物业管理': '物业服务领先企业',
                '租赁住房': '长租公寓运营商'
            },
            'T3': 2000
        }
    ]
    
    # 测试保存到数据库
    result = crawler.save_to_database(mock_stocks, "测试题材")
    
    if result:
        print("✅ 数据插入成功")
        
        # 验证数据是否正确保存
        saved_stocks = crawler.get_stocks_from_db("测试题材")
        
        if len(saved_stocks) == 2:
            print(f"✅ 数据验证成功，保存了 {len(saved_stocks)} 条记录")
            
            # 检查数据完整性
            for stock in saved_stocks:
                if stock['name'] in ['平安银行', '万科A']:
                    print(f"📊 {stock['name']} ({stock['symbol']}) - {stock['industry']}")
                else:
                    print("❌ 数据内容不匹配")
                    break
            else:
                print("✅ 数据内容验证成功")
        else:
            print(f"❌ 数据数量不匹配，期望2条，实际{len(saved_stocks)}条")
    else:
        print("❌ 数据插入失败")
    
    # 清理测试数据库
    if os.path.exists(test_db):
        os.remove(test_db)
    
    return result


def test_database_queries():
    """测试数据库查询功能"""
    print("\n🧪 测试数据库查询功能...")
    
    test_db = "test_plate_stocks.db"
    if os.path.exists(test_db):
        os.remove(test_db)
    
    crawler = PlateDataCrawler(db_path=test_db)
    db_manager = DatabaseManager(db_path=test_db)
    
    # 插入测试数据
    mock_stocks = [
        {
            '_id': '1', 'ts_code': '000001.SZ', 'name': '平安银行', 'symbol': '000001',
            'industry': '银行', 'area': '深圳', 'plates_info': {}
        },
        {
            '_id': '2', 'ts_code': '000002.SZ', 'name': '万科A', 'symbol': '000002',
            'industry': '房地产', 'area': '深圳', 'plates_info': {}
        },
        {
            '_id': '3', 'ts_code': '600000.SH', 'name': '浦发银行', 'symbol': '600000',
            'industry': '银行', 'area': '上海', 'plates_info': {}
        }
    ]
    
    crawler.save_to_database(mock_stocks[:2], "金融题材")
    crawler.save_to_database([mock_stocks[2]], "银行题材")
    
    # 测试各种查询
    tests_passed = 0
    total_tests = 0
    
    # 测试1: 按题材查询
    total_tests += 1
    finance_stocks = crawler.get_stocks_from_db("金融题材")
    if len(finance_stocks) == 2:
        print("✅ 按题材查询测试通过")
        tests_passed += 1
    else:
        print(f"❌ 按题材查询测试失败，期望2条，实际{len(finance_stocks)}条")
    
    # 测试2: 搜索功能
    total_tests += 1
    bank_stocks = crawler.search_stocks("银行")
    if len(bank_stocks) >= 2:  # 应该找到平安银行和浦发银行
        print("✅ 搜索功能测试通过")
        tests_passed += 1
    else:
        print(f"❌ 搜索功能测试失败，期望至少2条，实际{len(bank_stocks)}条")
    
    # 测试3: 题材汇总
    total_tests += 1
    plates_summary = crawler.get_plates_summary()
    if len(plates_summary) == 2:  # 应该有2个题材
        print("✅ 题材汇总测试通过")
        tests_passed += 1
    else:
        print(f"❌ 题材汇总测试失败，期望2个题材，实际{len(plates_summary)}个")
    
    # 测试4: 统计功能
    total_tests += 1
    plate_stats = db_manager.get_plate_statistics()
    if len(plate_stats) == 2:
        print("✅ 统计功能测试通过")
        tests_passed += 1
    else:
        print(f"❌ 统计功能测试失败")
    
    # 测试5: 行业分布
    total_tests += 1
    industry_dist = db_manager.get_industry_distribution()
    if len(industry_dist) >= 2:  # 应该有银行和房地产
        print("✅ 行业分布测试通过")
        tests_passed += 1
    else:
        print(f"❌ 行业分布测试失败")
    
    print(f"📊 查询测试结果: {tests_passed}/{total_tests} 通过")
    
    # 清理测试数据库
    if os.path.exists(test_db):
        os.remove(test_db)
    
    return tests_passed == total_tests


def main():
    """主测试函数"""
    print("🚀 数据库功能测试")
    print("=" * 50)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("数据库创建", test_database_creation()))
    test_results.append(("数据插入", test_data_insertion()))
    test_results.append(("数据库查询", test_database_queries()))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📋 测试结果汇总:")
    print("=" * 50)
    
    passed = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    total = len(test_results)
    print(f"\n🎯 总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有数据库功能测试通过!")
    else:
        print("⚠️  部分测试失败，请检查代码")


if __name__ == "__main__":
    main()
