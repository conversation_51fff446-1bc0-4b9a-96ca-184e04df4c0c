"""
HP_formula.py - 小白量化公式模块替代实现
提供常用的技术指标计算函数
"""
import pandas as pd
import numpy as np

def initmydf(df):
    """
    初始化数据框，标准化列名
    
    Args:
        df: 原始数据框
        
    Returns:
        DataFrame: 标准化后的数据框
    """
    # 复制数据框
    mydf = df.copy()
    
    # 标准化列名
    column_mapping = {
        'datetime': 'datetime',
        'open': 'open',
        'close': 'close', 
        'high': 'high',
        'low': 'low',
        'volume': 'volume',
        'amount': 'amount'
    }
    
    # 重命名列
    for old_name, new_name in column_mapping.items():
        if old_name in mydf.columns:
            mydf = mydf.rename(columns={old_name: new_name})
    
    # 确保数据类型正确
    numeric_columns = ['open', 'close', 'high', 'low', 'volume', 'amount']
    for col in numeric_columns:
        if col in mydf.columns:
            mydf[col] = pd.to_numeric(mydf[col], errors='coerce')
    
    # 按时间排序
    if 'datetime' in mydf.columns:
        mydf = mydf.sort_values('datetime').reset_index(drop=True)
    
    return mydf

def MA(series, period):
    """
    简单移动平均线
    
    Args:
        series: 价格序列
        period: 周期
        
    Returns:
        Series: 移动平均值
    """
    return series.rolling(window=period, min_periods=1).mean()

def EMA(series, period):
    """
    指数移动平均线
    
    Args:
        series: 价格序列
        period: 周期
        
    Returns:
        Series: 指数移动平均值
    """
    return series.ewm(span=period, adjust=False).mean()

def SMA(series, period, weight=1):
    """
    平滑移动平均线
    
    Args:
        series: 价格序列
        period: 周期
        weight: 权重
        
    Returns:
        Series: 平滑移动平均值
    """
    alpha = weight / period
    return series.ewm(alpha=alpha, adjust=False).mean()

def HHV(series, period):
    """
    最高值
    
    Args:
        series: 价格序列
        period: 周期
        
    Returns:
        Series: 周期内最高值
    """
    return series.rolling(window=period, min_periods=1).max()

def LLV(series, period):
    """
    最低值
    
    Args:
        series: 价格序列
        period: 周期
        
    Returns:
        Series: 周期内最低值
    """
    return series.rolling(window=period, min_periods=1).min()

def CROSS(series1, series2):
    """
    交叉函数，判断series1是否向上穿越series2
    
    Args:
        series1: 序列1
        series2: 序列2
        
    Returns:
        Series: 交叉信号 (1表示向上穿越，0表示无穿越)
    """
    # 当前值：series1 > series2
    current = series1 > series2
    # 前一个值：series1 <= series2
    previous = (series1 <= series2).shift(1)
    
    # 向上穿越：前一个值series1<=series2，当前值series1>series2
    cross_up = current & previous
    
    return cross_up.astype(int)

def IF(condition, true_value, false_value):
    """
    条件函数
    
    Args:
        condition: 条件序列
        true_value: 条件为真时的值
        false_value: 条件为假时的值
        
    Returns:
        Series: 结果序列
    """
    if isinstance(condition, pd.Series):
        return pd.Series(np.where(condition, true_value, false_value), index=condition.index)
    else:
        return true_value if condition else false_value

def RSI(series, period=14):
    """
    相对强弱指标
    
    Args:
        series: 价格序列
        period: 周期
        
    Returns:
        Series: RSI值
    """
    delta = series.diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    
    avg_gain = gain.rolling(window=period, min_periods=1).mean()
    avg_loss = loss.rolling(window=period, min_periods=1).mean()
    
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    
    return rsi

def MACD(series, fast=12, slow=26, signal=9):
    """
    MACD指标
    
    Args:
        series: 价格序列
        fast: 快线周期
        slow: 慢线周期
        signal: 信号线周期
        
    Returns:
        tuple: (DIF, DEA, MACD)
    """
    ema_fast = EMA(series, fast)
    ema_slow = EMA(series, slow)
    
    dif = ema_fast - ema_slow
    dea = EMA(dif, signal)
    macd = 2 * (dif - dea)
    
    return dif, dea, macd

def KDJ(high, low, close, period=9, k_period=3, d_period=3):
    """
    KDJ指标
    
    Args:
        high: 最高价序列
        low: 最低价序列
        close: 收盘价序列
        period: RSV周期
        k_period: K值周期
        d_period: D值周期
        
    Returns:
        tuple: (K, D, J)
    """
    # 计算RSV
    lowest_low = low.rolling(window=period, min_periods=1).min()
    highest_high = high.rolling(window=period, min_periods=1).max()
    
    rsv = (close - lowest_low) / (highest_high - lowest_low) * 100
    rsv = rsv.fillna(50)  # 填充NaN值
    
    # 计算K值
    k = SMA(rsv, k_period, 1)
    
    # 计算D值
    d = SMA(k, d_period, 1)
    
    # 计算J值
    j = 3 * k - 2 * d
    
    return k, d, j

def BOLL(series, period=20, std_dev=2):
    """
    布林带指标
    
    Args:
        series: 价格序列
        period: 周期
        std_dev: 标准差倍数
        
    Returns:
        tuple: (上轨, 中轨, 下轨)
    """
    middle = MA(series, period)
    std = series.rolling(window=period, min_periods=1).std()
    
    upper = middle + std_dev * std
    lower = middle - std_dev * std
    
    return upper, middle, lower

def ATR(high, low, close, period=14):
    """
    真实波动范围
    
    Args:
        high: 最高价序列
        low: 最低价序列
        close: 收盘价序列
        period: 周期
        
    Returns:
        Series: ATR值
    """
    prev_close = close.shift(1)
    
    tr1 = high - low
    tr2 = abs(high - prev_close)
    tr3 = abs(low - prev_close)
    
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    atr = tr.rolling(window=period, min_periods=1).mean()
    
    return atr

if __name__ == "__main__":
    # 测试代码
    print("HP_formula 模块测试")
    
    # 创建测试数据
    dates = pd.date_range('2024-01-01', periods=100, freq='D')
    np.random.seed(42)
    
    # 模拟股价数据
    close_prices = 100 + np.cumsum(np.random.randn(100) * 0.5)
    high_prices = close_prices + np.random.rand(100) * 2
    low_prices = close_prices - np.random.rand(100) * 2
    open_prices = close_prices + np.random.randn(100) * 0.3
    
    df = pd.DataFrame({
        'datetime': dates,
        'open': open_prices,
        'high': high_prices,
        'low': low_prices,
        'close': close_prices,
        'volume': np.random.randint(1000, 10000, 100),
        'amount': np.random.randint(100000, 1000000, 100)
    })
    
    # 初始化数据
    mydf = initmydf(df)
    close = mydf['close']
    high = mydf['high']
    low = mydf['low']
    
    # 测试各种指标
    print("测试MA:", MA(close, 5).tail(3).values)
    print("测试EMA:", EMA(close, 5).tail(3).values)
    print("测试HHV:", HHV(close, 10).tail(3).values)
    print("测试LLV:", LLV(close, 10).tail(3).values)
    
    # 测试MACD
    dif, dea, macd = MACD(close)
    print("测试MACD DIF:", dif.tail(3).values)
    
    # 测试KDJ
    k, d, j = KDJ(high, low, close)
    print("测试KDJ K:", k.tail(3).values)
    
    print("所有指标测试完成")
