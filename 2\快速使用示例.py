#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
可转债数据获取快速使用示例

展示如何快速使用可转债数据工具
"""

from 获取可转债数据 import ConvertibleBondData
from 可转债分析工具 import BondAnalyzer


def example_1_basic_usage():
    """示例1: 基础使用"""
    print("=" * 50)
    print("示例1: 基础数据获取")
    print("=" * 50)
    
    # 初始化工具
    bond_tool = ConvertibleBondData()
    
    # 定义一些热门可转债代码
    bond_codes = ['110001', '113050', '128136', '123001']
    
    print(f"获取可转债 {bond_codes} 的数据...")
    
    # 1. 获取实时行情
    print("\n1. 实时行情:")
    quotes = bond_tool.get_bond_quotes(bond_codes)
    if not quotes.empty:
        # 只显示关键字段
        key_columns = ['bond_code', 'market', 'price', 'volume']
        display_quotes = quotes[key_columns] if all(col in quotes.columns for col in key_columns) else quotes
        print(display_quotes.to_string(index=False))
    else:
        print("未获取到行情数据")
    
    # 2. 获取K线数据
    print(f"\n2. K线数据 (以 {bond_codes[0]} 为例):")
    kline = bond_tool.get_bond_kline(bond_codes[0], frequency='1d', count=5)
    if not kline.empty:
        key_columns = ['bond_code', 'datetime', 'open', 'high', 'low', 'close', 'volume']
        display_kline = kline[key_columns] if all(col in kline.columns for col in key_columns) else kline
        print(display_kline.to_string(index=False))
    else:
        print("未获取到K线数据")
    
    # 3. 获取基本信息
    print(f"\n3. 基本信息:")
    info = bond_tool.get_bond_info(bond_codes)
    if not info.empty:
        key_columns = ['bond_code', 'bond_name', 'market', 'current_price']
        display_info = info[key_columns] if all(col in info.columns for col in key_columns) else info
        print(display_info.to_string(index=False))
    else:
        print("未获取到基本信息")
    
    bond_tool.close()


def example_2_analysis_tool():
    """示例2: 使用分析工具"""
    print("\n" + "=" * 50)
    print("示例2: 数据分析工具")
    print("=" * 50)
    
    # 初始化分析器
    analyzer = BondAnalyzer()
    
    # 热门可转债
    hot_bonds = ['110001', '128136', '123001']
    
    print(f"分析可转债 {hot_bonds}...")
    
    # 1. 监控行情
    print("\n1. 行情监控:")
    quotes = analyzer.monitor_bonds(hot_bonds, save_to_file=False)
    if not quotes.empty:
        # 筛选有价格的数据
        valid_quotes = quotes[quotes['price'] > 0] if 'price' in quotes.columns else quotes
        if not valid_quotes.empty:
            key_columns = ['bond_code', 'market', 'price']
            if 'change_pct' in valid_quotes.columns:
                key_columns.append('change_pct')
            display_quotes = valid_quotes[key_columns] if all(col in valid_quotes.columns for col in key_columns) else valid_quotes
            print(display_quotes.to_string(index=False))
        else:
            print("当前无有效行情数据（可能是非交易时间）")
    
    # 2. 价值分析
    print(f"\n2. 价值分析 (以 {hot_bonds[0]} 为例):")
    analysis = analyzer.analyze_bond_value(hot_bonds[0])
    print(f"分析时间: {analysis['analysis_time']}")
    print(f"投资建议: {analysis['recommendation'] or '暂无建议'}")
    
    if analysis['price_analysis']:
        price_info = analysis['price_analysis']
        print(f"当前价格: {price_info.get('current_price', 'N/A')}")
        print(f"30日均价: {price_info.get('avg_30d', 'N/A')}")
        print(f"价格位置: {price_info.get('price_position', 'N/A')}%")
    
    # 3. 筛选可转债
    print(f"\n3. 筛选可转债:")
    criteria = {
        'min_price': 90,
        'max_price': 130,
        'min_volume': 100
    }
    print(f"筛选条件: {criteria}")
    
    filtered = analyzer.screen_bonds(criteria)
    if not filtered.empty:
        valid_filtered = filtered[filtered['price'] > 0] if 'price' in filtered.columns else filtered
        if not valid_filtered.empty:
            key_columns = ['bond_code', 'market', 'price']
            display_filtered = valid_filtered[key_columns] if all(col in valid_filtered.columns for col in key_columns) else valid_filtered
            print(f"筛选结果 ({len(valid_filtered)} 只):")
            print(display_filtered.to_string(index=False))
        else:
            print("筛选结果为空（可能是非交易时间）")
    else:
        print("未找到符合条件的可转债")
    
    analyzer.close()


def example_3_code_validation():
    """示例3: 代码验证功能"""
    print("\n" + "=" * 50)
    print("示例3: 可转债代码验证")
    print("=" * 50)
    
    bond_tool = ConvertibleBondData()
    
    # 测试各种代码
    test_codes = [
        '110001',  # 上海可转债
        '113050',  # 上海可转债
        '123001',  # 深圳可转债
        '128136',  # 深圳可转债
        '600036',  # 普通股票
        '000001',  # 普通股票
        '123456',  # 无效代码
    ]
    
    print("代码验证结果:")
    print("-" * 40)
    
    for code in test_codes:
        is_bond = bond_tool.is_convertible_bond(code)
        if is_bond:
            market = bond_tool.get_bond_market(code)
            market_name = '上海' if market == 1 else '深圳' if market == 0 else '未知'
            print(f"{code}: ✓ 可转债 ({market_name})")
        else:
            print(f"{code}: ✗ 非可转债")
    
    bond_tool.close()


def example_4_data_export():
    """示例4: 数据导出功能"""
    print("\n" + "=" * 50)
    print("示例4: 数据导出")
    print("=" * 50)
    
    analyzer = BondAnalyzer()
    
    # 获取一些数据
    test_bonds = ['110001', '128136']
    quotes = analyzer.monitor_bonds(test_bonds, save_to_file=False)
    
    if not quotes.empty:
        # 导出到文件
        filename = analyzer.export_data(quotes, "示例导出数据.csv")
        print(f"数据已导出到: {filename}")
        
        # 显示导出的数据概要
        print(f"导出数据概要:")
        print(f"- 记录数: {len(quotes)}")
        print(f"- 字段数: {len(quotes.columns)}")
        print(f"- 主要字段: {list(quotes.columns[:5])}")
    else:
        print("没有数据可导出")
    
    analyzer.close()


def main():
    """主函数"""
    print("可转债数据获取工具 - 快速使用示例")
    print("=" * 60)
    
    try:
        # 运行所有示例
        example_1_basic_usage()
        example_2_analysis_tool()
        example_3_code_validation()
        example_4_data_export()
        
    except KeyboardInterrupt:
        print("\n\n用户中断操作")
    except Exception as e:
        print(f"\n\n发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("示例演示完成！")
    print("=" * 60)
    
    print("\n使用提示:")
    print("1. 在交易时间内运行可获得实时数据")
    print("2. 非交易时间可能显示为0或空数据")
    print("3. 可以修改代码中的可转债列表进行测试")
    print("4. 数据会自动保存到CSV文件")


if __name__ == "__main__":
    main()
