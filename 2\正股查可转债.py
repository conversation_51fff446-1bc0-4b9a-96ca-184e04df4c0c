#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
正股查可转债工具

用户输入正股6位代码，获取对应的可转债信息
"""

import pandas as pd
from datetime import datetime
from typing import Dict, List, Optional

try:
    from mootdx.quotes import Quotes
    MOOTDX_AVAILABLE = True
except ImportError:
    print("警告: 无法导入 mootdx 库")
    print("请安装: pip install mootdx")
    MOOTDX_AVAILABLE = False


class StockToBondQuery:
    """正股查可转债工具"""
    
    def __init__(self):
        """初始化工具"""
        self.stock_bond_mapping = self._init_stock_bond_mapping()
        self.client = None
        self.connected = False
        
        if MOOTDX_AVAILABLE:
            self._connect()
    
    def _connect(self):
        """连接数据服务器"""
        try:
            print("正在连接数据服务器...")
            self.client = Quotes.factory(market='std', timeout=30)
            self.connected = True
            print("✓ 连接成功")
        except Exception as e:
            print(f"✗ 连接失败: {e}")
            self.connected = False
    
    def _init_stock_bond_mapping(self) -> Dict[str, List[Dict]]:
        """
        初始化正股到可转债的映射关系
        
        Returns:
            Dict: 正股代码 -> 可转债列表的映射
        """
        mapping = {}
        
        # 银行股
        mapping['601988'] = [
            {'bond_code': '110001', 'bond_name': '中行转债', 'market': '上海'},
            {'bond_code': '113001', 'bond_name': '中行转债2', 'market': '上海'}
        ]
        mapping['600036'] = [
            {'bond_code': '110011', 'bond_name': '招银转债', 'market': '上海'},
            {'bond_code': '113011', 'bond_name': '招银转债2', 'market': '上海'}
        ]
        mapping['600000'] = [
            {'bond_code': '110030', 'bond_name': '浦发转债', 'market': '上海'},
            {'bond_code': '110059', 'bond_name': '浦发转债2', 'market': '上海'}
        ]
        mapping['600016'] = [
            {'bond_code': '110031', 'bond_name': '民生转债', 'market': '上海'}
        ]
        mapping['601318'] = [
            {'bond_code': '110032', 'bond_name': '平安转债', 'market': '上海'}
        ]
        mapping['601166'] = [
            {'bond_code': '110033', 'bond_name': '兴业转债', 'market': '上海'}
        ]
        mapping['601328'] = [
            {'bond_code': '110034', 'bond_name': '交行转债', 'market': '上海'}
        ]
        mapping['601009'] = [
            {'bond_code': '113050', 'bond_name': '南银转债', 'market': '上海'}
        ]
        mapping['000001'] = [
            {'bond_code': '123008', 'bond_name': '平安银行转债', 'market': '深圳'}
        ]
        mapping['002142'] = [
            {'bond_code': '113048', 'bond_name': '宁银转债', 'market': '上海'},
            {'bond_code': '123013', 'bond_name': '宁银转债2', 'market': '深圳'}
        ]
        
        # 科技股
        mapping['002475'] = [
            {'bond_code': '128136', 'bond_name': '立讯转债', 'market': '深圳'},
            {'bond_code': '128049', 'bond_name': '立讯转债2', 'market': '深圳'},
            {'bond_code': '123005', 'bond_name': '立讯转债3', 'market': '深圳'}
        ]
        mapping['002415'] = [
            {'bond_code': '123002', 'bond_name': '海康转债', 'market': '深圳'},
            {'bond_code': '128040', 'bond_name': '海康转债2', 'market': '深圳'}
        ]
        mapping['300058'] = [
            {'bond_code': '123001', 'bond_name': '蓝标转债', 'market': '深圳'}
        ]
        mapping['300433'] = [
            {'bond_code': '123003', 'bond_name': '蓝思转债', 'market': '深圳'}
        ]
        mapping['002241'] = [
            {'bond_code': '123004', 'bond_name': '歌尔转债', 'market': '深圳'},
            {'bond_code': '128035', 'bond_name': '歌尔转债2', 'market': '深圳'}
        ]
        mapping['000063'] = [
            {'bond_code': '127002', 'bond_name': '中兴转债', 'market': '深圳'}
        ]
        mapping['002230'] = [
            {'bond_code': '123012', 'bond_name': '科大转债', 'market': '深圳'}
        ]
        
        # 消费股
        mapping['600519'] = [
            {'bond_code': '110020', 'bond_name': '茅台转债', 'market': '上海'},
            {'bond_code': '113008', 'bond_name': '茅台转债2', 'market': '上海'}
        ]
        mapping['000858'] = [
            {'bond_code': '123006', 'bond_name': '五粮液转债', 'market': '深圳'}
        ]
        mapping['002304'] = [
            {'bond_code': '123009', 'bond_name': '洋河转债', 'market': '深圳'}
        ]
        mapping['600887'] = [
            {'bond_code': '110036', 'bond_name': '伊利转债', 'market': '上海'}
        ]
        
        # 制造业
        mapping['600585'] = [
            {'bond_code': '110037', 'bond_name': '海螺转债', 'market': '上海'}
        ]
        mapping['600104'] = [
            {'bond_code': '110039', 'bond_name': '上汽转债', 'market': '上海'}
        ]
        mapping['600031'] = [
            {'bond_code': '113027', 'bond_name': '三一转债', 'market': '上海'}
        ]
        mapping['000887'] = [
            {'bond_code': '127001', 'bond_name': '中鼎转债', 'market': '深圳'},
            {'bond_code': '127011', 'bond_name': '中鼎转债2', 'market': '深圳'}
        ]
        mapping['002325'] = [
            {'bond_code': '128013', 'bond_name': '洪涛转债', 'market': '深圳'}
        ]
        
        # 医药股
        mapping['600276'] = [
            {'bond_code': '110038', 'bond_name': '恒瑞转债', 'market': '上海'}
        ]
        mapping['600196'] = [
            {'bond_code': '110044', 'bond_name': '复星转债', 'market': '上海'}
        ]
        mapping['002422'] = [
            {'bond_code': '128041', 'bond_name': '科伦转债', 'market': '深圳'}
        ]
        
        return mapping
    
    def query_bonds_by_stock(self, stock_code: str) -> Dict:
        """
        根据正股代码查询可转债
        
        Args:
            stock_code: 正股6位代码
            
        Returns:
            Dict: 查询结果
        """
        result = {
            'stock_code': stock_code,
            'stock_name': '',
            'bonds': [],
            'stock_quotes': None,
            'bonds_quotes': [],
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'success': False,
            'message': ''
        }
        
        # 验证股票代码格式
        if not stock_code or len(stock_code) != 6 or not stock_code.isdigit():
            result['message'] = '请输入6位数字的股票代码'
            return result
        
        # 查找对应的可转债
        if stock_code in self.stock_bond_mapping:
            result['bonds'] = self.stock_bond_mapping[stock_code]
            result['success'] = True
            result['message'] = f'找到 {len(result["bonds"])} 只对应的可转债'
            
            # 获取正股行情
            if self.connected:
                try:
                    stock_quotes = self._get_stock_quotes(stock_code)
                    if stock_quotes:
                        result['stock_quotes'] = stock_quotes
                        result['stock_name'] = stock_quotes.get('name', f'股票{stock_code}')
                except Exception as e:
                    print(f"获取正股行情失败: {e}")
                
                # 获取可转债行情
                for bond in result['bonds']:
                    try:
                        bond_quotes = self._get_bond_quotes(bond['bond_code'])
                        if bond_quotes:
                            bond_quotes['bond_name'] = bond['bond_name']
                            result['bonds_quotes'].append(bond_quotes)
                    except Exception as e:
                        print(f"获取可转债 {bond['bond_code']} 行情失败: {e}")
        else:
            result['message'] = f'未找到股票 {stock_code} 对应的可转债'
        
        return result
    
    def _get_stock_quotes(self, stock_code: str) -> Optional[Dict]:
        """获取正股行情"""
        if not self.connected:
            return None
        
        try:
            # 判断市场
            market = 1 if stock_code.startswith(('60', '68', '11', '12', '13')) else 0
            
            quotes = self.client.quotes(symbol=stock_code, market=market)
            if not quotes.empty:
                data = quotes.iloc[0].to_dict()
                data['market'] = '上海' if market == 1 else '深圳'
                return data
        except Exception as e:
            print(f"获取正股 {stock_code} 行情失败: {e}")
        
        return None
    
    def _get_bond_quotes(self, bond_code: str) -> Optional[Dict]:
        """获取可转债行情"""
        if not self.connected:
            return None
        
        try:
            # 判断市场
            if bond_code.startswith(('110', '113', '118')):
                market = 1  # 上海
            elif bond_code.startswith(('123', '127', '128')):
                market = 0  # 深圳
            else:
                return None
            
            quotes = self.client.quotes(symbol=bond_code, market=market)
            if not quotes.empty:
                data = quotes.iloc[0].to_dict()
                data['bond_code'] = bond_code
                data['market'] = '上海' if market == 1 else '深圳'
                return data
        except Exception as e:
            print(f"获取可转债 {bond_code} 行情失败: {e}")
        
        return None
    
    def display_result(self, result: Dict):
        """显示查询结果"""
        print(f"\n{'='*60}")
        print(f"正股查可转债结果")
        print(f"{'='*60}")
        print(f"查询时间: {result['timestamp']}")
        print(f"正股代码: {result['stock_code']}")
        
        if result['stock_name']:
            print(f"正股名称: {result['stock_name']}")
        
        if result['stock_quotes']:
            sq = result['stock_quotes']
            print(f"正股行情: 价格 {sq.get('price', 'N/A')}, 涨跌 {sq.get('change', 'N/A')}")
        
        if result['success']:
            print(f"\n找到 {len(result['bonds'])} 只对应的可转债:")
            print("-" * 60)
            
            for i, bond in enumerate(result['bonds'], 1):
                print(f"{i}. {bond['bond_code']} - {bond['bond_name']} ({bond['market']})")
                
                # 显示可转债行情
                bond_quotes = None
                for bq in result['bonds_quotes']:
                    if bq.get('bond_code') == bond['bond_code']:
                        bond_quotes = bq
                        break
                
                if bond_quotes:
                    print(f"   行情: 价格 {bond_quotes.get('price', 'N/A')}, 成交量 {bond_quotes.get('vol', 'N/A')}")
                else:
                    print(f"   行情: 暂无数据")
                print()
        else:
            print(f"\n{result['message']}")
    
    def close(self):
        """关闭连接"""
        if self.client:
            try:
                self.client.close()
                print("已关闭连接")
            except:
                pass


def main():
    """主函数"""
    print("正股查可转债工具")
    print("=" * 50)
    print("输入正股6位代码，查询对应的可转债信息")
    print("输入 'quit' 或 'exit' 退出程序")
    print("=" * 50)
    
    # 初始化工具
    query_tool = StockToBondQuery()
    
    try:
        while True:
            # 获取用户输入
            stock_code = input("\n请输入正股代码: ").strip()
            
            # 检查退出命令
            if stock_code.lower() in ['quit', 'exit', 'q']:
                print("退出程序")
                break
            
            # 查询可转债
            result = query_tool.query_bonds_by_stock(stock_code)
            
            # 显示结果
            query_tool.display_result(result)
    
    except KeyboardInterrupt:
        print("\n\n用户中断程序")
    except Exception as e:
        print(f"\n发生错误: {e}")
    finally:
        query_tool.close()


if __name__ == "__main__":
    main()
