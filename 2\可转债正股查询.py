#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
可转债对应正股查询工具

功能：
1. 查询可转债对应的正股代码和信息
2. 获取正股实时行情
3. 计算转股价值和溢价率
4. 可转债与正股联动分析

作者: Bond-Stock Mapping Tool
日期: 2025-08-06
"""

import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from 获取可转债数据 import ConvertibleBondData


class BondStockMapper:
    """可转债正股映射工具"""
    
    def __init__(self):
        """初始化映射工具"""
        self.bond_tool = ConvertibleBondData()
    
    def get_underlying_stock(self, bond_code: str) -> Optional[Dict]:
        """
        获取可转债对应的正股信息
        
        Args:
            bond_code: 可转债代码
            
        Returns:
            Dict: 正股信息，如果未找到返回None
        """
        if bond_code in self.bond_stock_mapping:
            return self.bond_stock_mapping[bond_code].copy()
        else:
            # 如果映射表中没有，尝试推断
            return self._infer_underlying_stock(bond_code)
    
    def _infer_underlying_stock(self, bond_code: str) -> Optional[Dict]:
        """
        推断可转债对应的正股（基于命名规律）
        
        Args:
            bond_code: 可转债代码
            
        Returns:
            Dict: 推断的正股信息
        """
        # 这里可以实现基于规律的推断逻辑
        # 目前返回基础信息
        if self.bond_tool.is_convertible_bond(bond_code):
            market = self.bond_tool.get_bond_market(bond_code)
            market_name = '上海' if market == 1 else '深圳' if market == 0 else '未知'
            
            return {
                'stock_code': 'UNKNOWN',
                'stock_name': f'未知正股({bond_code})',
                'bond_name': f'转债{bond_code}',
                'conversion_price': None,
                'conversion_ratio': None,
                'market': market_name
            }
        
        return None
    
    def get_bond_stock_quotes(self, bond_code: str) -> Dict:
        """
        获取可转债和对应正股的实时行情
        
        Args:
            bond_code: 可转债代码
            
        Returns:
            Dict: 包含可转债和正股行情的字典
        """
        result = {
            'bond_code': bond_code,
            'bond_quotes': None,
            'stock_quotes': None,
            'underlying_info': None,
            'conversion_analysis': None
        }
        
        # 获取正股信息
        underlying_info = self.get_underlying_stock(bond_code)
        if not underlying_info:
            result['error'] = f'未找到可转债 {bond_code} 对应的正股信息'
            return result
        
        result['underlying_info'] = underlying_info
        
        # 获取可转债行情
        bond_quotes = self.bond_tool.get_bond_quotes(bond_code)
        if not bond_quotes.empty:
            result['bond_quotes'] = bond_quotes.iloc[0].to_dict()
        
        # 获取正股行情
        stock_code = underlying_info['stock_code']
        if stock_code != 'UNKNOWN':
            try:
                # 获取正股市场
                stock_market = 1 if stock_code.startswith(('60', '68', '11', '12', '13')) else 0
                stock_quotes = self.bond_tool.client.quotes(symbol=stock_code, market=stock_market)
                if not stock_quotes.empty:
                    result['stock_quotes'] = stock_quotes.iloc[0].to_dict()
            except Exception as e:
                print(f"获取正股 {stock_code} 行情失败: {e}")
        
        # 计算转股价值分析
        result['conversion_analysis'] = self._calculate_conversion_value(
            result['bond_quotes'], 
            result['stock_quotes'], 
            underlying_info
        )
        
        return result
    
    def _calculate_conversion_value(self, bond_quotes: Dict, stock_quotes: Dict, underlying_info: Dict) -> Dict:
        """
        计算转股价值分析
        
        Args:
            bond_quotes: 可转债行情
            stock_quotes: 正股行情
            underlying_info: 正股信息
            
        Returns:
            Dict: 转股价值分析结果
        """
        analysis = {
            'conversion_value': None,  # 转股价值
            'premium_rate': None,      # 溢价率
            'break_even_price': None,  # 盈亏平衡价
            'recommendation': ''
        }
        
        if not bond_quotes or not stock_quotes or not underlying_info:
            return analysis
        
        try:
            bond_price = bond_quotes.get('price', 0)
            stock_price = stock_quotes.get('price', 0)
            conversion_price = underlying_info.get('conversion_price')
            conversion_ratio = underlying_info.get('conversion_ratio')
            
            if bond_price > 0 and stock_price > 0 and conversion_price and conversion_ratio:
                # 转股价值 = 正股价格 × 转股比例
                conversion_value = stock_price * conversion_ratio
                analysis['conversion_value'] = round(conversion_value, 2)
                
                # 溢价率 = (可转债价格 - 转股价值) / 转股价值 × 100%
                if conversion_value > 0:
                    premium_rate = (bond_price - conversion_value) / conversion_value * 100
                    analysis['premium_rate'] = round(premium_rate, 2)
                
                # 盈亏平衡价 = 转股价格 × (可转债价格 / 100)
                break_even_price = conversion_price * (bond_price / 100)
                analysis['break_even_price'] = round(break_even_price, 2)
                
                # 投资建议
                if premium_rate < 5:
                    analysis['recommendation'] = '溢价率较低，转股价值较高'
                elif premium_rate < 15:
                    analysis['recommendation'] = '溢价率适中，可关注'
                else:
                    analysis['recommendation'] = '溢价率较高，谨慎投资'
        
        except Exception as e:
            print(f"计算转股价值时发生错误: {e}")
        
        return analysis
    
    def batch_analyze_bonds(self, bond_codes: List[str]) -> pd.DataFrame:
        """
        批量分析可转债与正股
        
        Args:
            bond_codes: 可转债代码列表
            
        Returns:
            pd.DataFrame: 分析结果
        """
        results = []
        
        print(f"开始批量分析 {len(bond_codes)} 只可转债...")
        
        for i, bond_code in enumerate(bond_codes, 1):
            print(f"分析进度: {i}/{len(bond_codes)} - {bond_code}")
            
            analysis = self.get_bond_stock_quotes(bond_code)
            
            # 整理数据
            row = {
                'bond_code': bond_code,
                'bond_name': analysis['underlying_info']['bond_name'] if analysis['underlying_info'] else '',
                'stock_code': analysis['underlying_info']['stock_code'] if analysis['underlying_info'] else '',
                'stock_name': analysis['underlying_info']['stock_name'] if analysis['underlying_info'] else '',
                'market': analysis['underlying_info']['market'] if analysis['underlying_info'] else '',
                'bond_price': analysis['bond_quotes']['price'] if analysis['bond_quotes'] else 0,
                'stock_price': analysis['stock_quotes']['price'] if analysis['stock_quotes'] else 0,
                'conversion_price': analysis['underlying_info']['conversion_price'] if analysis['underlying_info'] else None,
                'conversion_value': analysis['conversion_analysis']['conversion_value'] if analysis['conversion_analysis'] else None,
                'premium_rate': analysis['conversion_analysis']['premium_rate'] if analysis['conversion_analysis'] else None,
                'recommendation': analysis['conversion_analysis']['recommendation'] if analysis['conversion_analysis'] else ''
            }
            
            results.append(row)
        
        return pd.DataFrame(results)
    
    def search_by_stock(self, stock_code: str) -> List[Dict]:
        """
        根据正股代码查找对应的可转债
        
        Args:
            stock_code: 正股代码
            
        Returns:
            List[Dict]: 对应的可转债列表
        """
        bonds = []
        
        for bond_code, info in self.bond_stock_mapping.items():
            if info['stock_code'] == stock_code:
                bonds.append({
                    'bond_code': bond_code,
                    'bond_name': info['bond_name'],
                    'conversion_price': info['conversion_price'],
                    'conversion_ratio': info['conversion_ratio'],
                    'market': info['market']
                })
        
        return bonds
    
    def export_mapping_table(self, filename: str = None) -> str:
        """
        导出可转债正股映射表
        
        Args:
            filename: 文件名
            
        Returns:
            str: 导出的文件路径
        """
        if filename is None:
            filename = f"可转债正股映射表_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        # 整理映射数据
        mapping_data = []
        for bond_code, info in self.bond_stock_mapping.items():
            mapping_data.append({
                'bond_code': bond_code,
                'bond_name': info['bond_name'],
                'stock_code': info['stock_code'],
                'stock_name': info['stock_name'],
                'market': info['market'],
                'conversion_price': info['conversion_price'],
                'conversion_ratio': info['conversion_ratio']
            })
        
        df = pd.DataFrame(mapping_data)
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        
        print(f"映射表已导出到: {filename}")
        return filename
    
    def close(self):
        """关闭连接"""
        self.bond_tool.close()


def demo_bond_stock_mapping():
    """演示可转债正股映射功能"""
    print("=" * 60)
    print("可转债正股映射演示")
    print("=" * 60)
    
    mapper = BondStockMapper()
    
    # 测试可转债代码
    test_bonds = ['110001', '128136', '123001', '113050']
    
    print("\n1. 单只可转债分析")
    print("-" * 40)
    
    for bond_code in test_bonds[:2]:  # 只演示前2只
        print(f"\n分析可转债: {bond_code}")
        analysis = mapper.get_bond_stock_quotes(bond_code)
        
        if analysis['underlying_info']:
            info = analysis['underlying_info']
            print(f"  可转债名称: {info['bond_name']}")
            print(f"  对应正股: {info['stock_code']} - {info['stock_name']}")
            print(f"  转股价格: {info['conversion_price']}")
            print(f"  转股比例: {info['conversion_ratio']}")
            
            if analysis['conversion_analysis']:
                conv = analysis['conversion_analysis']
                print(f"  转股价值: {conv['conversion_value']}")
                print(f"  溢价率: {conv['premium_rate']}%")
                print(f"  投资建议: {conv['recommendation']}")
        else:
            print(f"  未找到对应正股信息")
    
    print(f"\n2. 批量分析")
    print("-" * 40)
    
    # 批量分析
    batch_result = mapper.batch_analyze_bonds(test_bonds)
    if not batch_result.empty:
        # 显示关键字段
        key_columns = ['bond_code', 'bond_name', 'stock_code', 'stock_name', 'premium_rate']
        display_columns = [col for col in key_columns if col in batch_result.columns]
        print(batch_result[display_columns].to_string(index=False))
    
    print(f"\n3. 根据正股查找可转债")
    print("-" * 40)
    
    # 根据正股查找可转债
    stock_code = '002475'  # 立讯精密
    bonds = mapper.search_by_stock(stock_code)
    if bonds:
        print(f"正股 {stock_code} 对应的可转债:")
        for bond in bonds:
            print(f"  {bond['bond_code']} - {bond['bond_name']}")
    else:
        print(f"未找到正股 {stock_code} 对应的可转债")
    
    print(f"\n4. 导出映射表")
    print("-" * 40)
    
    # 导出映射表
    filename = mapper.export_mapping_table()
    
    mapper.close()
    print(f"\n演示完成！")


if __name__ == "__main__":
    demo_bond_stock_mapping()
