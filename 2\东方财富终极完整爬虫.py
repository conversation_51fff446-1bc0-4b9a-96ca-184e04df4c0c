#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
东方财富终极完整可转债爬虫

结合多个数据源，获取最完整的可转债数据
"""

import urllib.request
import urllib.parse
import json
import csv
import time
from datetime import datetime


class UltimateBondSpider:
    """终极完整可转债爬虫"""
    
    def __init__(self):
        """初始化"""
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://data.eastmoney.com/',
            'Accept': 'application/json, text/plain, */*',
        }
    
    def get_active_bonds_quotes(self):
        """获取活跃可转债的实时行情"""
        
        # 东方财富可转债行情API
        url = "https://push2.eastmoney.com/api/qt/clist/get"
        
        params = {
            'cb': 'jQuery112409748013994908473_1691234567890',
            'pn': '1',
            'pz': '1000',
            'po': '1',
            'np': '1',
            'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
            'fltt': '2',
            'invt': '2',
            'fid': 'f3',
            'fs': 'b:MK0354',  # 可转债板块
            'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152',
            '_': str(int(time.time() * 1000))
        }
        
        try:
            # 构建URL
            query_string = urllib.parse.urlencode(params)
            full_url = f"{url}?{query_string}"
            
            print("正在获取活跃可转债实时行情...")
            
            # 创建请求
            req = urllib.request.Request(full_url, headers=self.headers)
            
            # 发送请求
            with urllib.request.urlopen(req, timeout=30) as response:
                text = response.read().decode('utf-8')
            
            # 提取JSON数据
            start = text.find('(') + 1
            end = text.rfind(')')
            json_str = text[start:end]
            
            data = json.loads(json_str)
            
            if data.get('rc') == 0 and 'data' in data and 'diff' in data['data']:
                bonds_data = data['data']['diff']
                print(f"✓ 获取到 {len(bonds_data)} 只活跃可转债行情")
                return bonds_data
            else:
                print("✗ 未获取到行情数据")
                return []
                
        except Exception as e:
            print(f"✗ 获取行情数据失败: {e}")
            return []
    
    def get_all_bonds_info(self):
        """获取所有可转债的基础信息"""
        
        url = "https://datacenter-web.eastmoney.com/api/data/v1/get"
        
        all_bonds = []
        page_number = 1
        page_size = 500
        
        while True:
            params = {
                'sortColumns': 'SECURITY_CODE',
                'sortTypes': '1',
                'pageSize': str(page_size),
                'pageNumber': str(page_number),
                'reportName': 'RPT_BOND_CB_LIST',
                'columns': 'ALL',
                'js': '',
                'source': 'WEB',
                'client': 'WEB',
                '_': str(int(time.time() * 1000))
            }
            
            try:
                # 构建URL
                query_string = urllib.parse.urlencode(params)
                full_url = f"{url}?{query_string}"
                
                print(f"正在获取第 {page_number} 页可转债基础信息...")
                
                # 创建请求
                req = urllib.request.Request(full_url, headers=self.headers)
                
                # 发送请求
                with urllib.request.urlopen(req, timeout=30) as response:
                    data = json.loads(response.read().decode('utf-8'))
                
                if data.get('success') and 'result' in data and 'data' in data['result']:
                    page_data = data['result']['data']
                    total_count = data['result'].get('count', 0)
                    
                    print(f"✓ 第 {page_number} 页获取成功: {len(page_data)} 条数据")
                    
                    if not page_data:
                        break
                    
                    all_bonds.extend(page_data)
                    
                    # 检查是否还有更多数据
                    if len(all_bonds) >= total_count:
                        print(f"已获取全部基础信息，总计 {len(all_bonds)} 条")
                        break
                    
                    page_number += 1
                    time.sleep(0.5)  # 避免请求过快
                    
                else:
                    print(f"第 {page_number} 页获取失败")
                    break
                    
            except Exception as e:
                print(f"✗ 获取第 {page_number} 页数据失败: {e}")
                break
        
        return all_bonds
    
    def merge_data(self, quotes_data, info_data):
        """合并行情数据和基础信息"""
        
        print("正在合并数据...")
        
        # 创建行情数据映射
        quotes_map = {}
        for item in quotes_data:
            code = item.get('f12')
            if code:
                quotes_map[code] = item
        
        # 创建基础信息映射
        info_map = {}
        for item in info_data:
            code = item.get('SECURITY_CODE')
            if code:
                info_map[code] = item
        
        # 获取所有可转债代码
        all_codes = set(quotes_map.keys()) | set(info_map.keys())
        
        merged_data = []
        
        for code in all_codes:
            quote = quotes_map.get(code, {})
            info = info_map.get(code, {})
            
            # 判断是否有退市日期
            delist_date = info.get('DELIST_DATE')
            status = '已退市' if delist_date and delist_date != '-' else '正常'
            
            merged_item = {
                # 基本信息
                '债券代码': code,
                '债券简称': quote.get('f14') or info.get('SECURITY_NAME_ABBR', ''),
                
                # 实时行情（优先使用行情数据）
                '债现价': quote.get('f2', ''),
                '涨跌幅': quote.get('f3', ''),
                '涨跌额': quote.get('f4', ''),
                '成交量': quote.get('f5', ''),
                '成交额': quote.get('f6', ''),
                '换手率': quote.get('f8', ''),
                '振幅': quote.get('f7', ''),
                '最高价': quote.get('f15', ''),
                '最低价': quote.get('f16', ''),
                '开盘价': quote.get('f17', ''),
                '昨收价': quote.get('f18', ''),
                '量比': quote.get('f10', ''),
                '市盈率': quote.get('f9', ''),
                
                # 正股信息
                '正股代码': info.get('CONVERT_STOCK_CODE', ''),
                '正股简称': info.get('CONVERT_STOCK_NAME', ''),
                '正股价': info.get('CONVERT_STOCK_PRICE', ''),
                
                # 转股信息
                '转股价': info.get('CONVERT_PRICE', ''),
                '转股价值': info.get('CONVERT_VALUE', ''),
                '转股溢价率': info.get('CONVERT_PREMIUM_RATIO', ''),
                
                # 债券价值
                '纯债价值': info.get('BOND_VALUE', ''),
                '纯债溢价率': info.get('BOND_PREMIUM_RATIO', ''),
                '到期收益率': info.get('YTMRT', ''),
                
                # 发行信息
                '发行规模': info.get('ISSUE_AMOUNT', ''),
                '申购日期': info.get('APPLY_DATE', ''),
                '申购代码': info.get('APPLY_CODE', ''),
                '申购上限': info.get('APPLY_UPPER_LIMIT', ''),
                '上市时间': info.get('LISTING_DATE', ''),
                '到期日期': info.get('MATURITY_DATE', ''),
                '剩余年限': info.get('REMAIN_YEAR', ''),
                
                # 评级和其他
                '信用评级': info.get('RATING', ''),
                '股权登记日': info.get('EQUITY_RECORD_DATE', ''),
                '每股配售额': info.get('ALLOT_PRICE', ''),
                '原股东配售': info.get('ORIG_SHARE_RATIO', ''),
                
                # 回售和赎回
                '回售触发价': info.get('RESALE_TRIG_PRICE', ''),
                '强赎触发价': info.get('REDEEM_PRICE', ''),
                
                # 状态和市场
                '状态': status,
                '市场': '上海' if code.startswith(('110', '113', '118')) else '深圳' if code.startswith(('123', '127', '128')) else '未知',
                '交易市场': info.get('TRADE_MARKET_CODE', ''),
                
                # 元数据
                '数据获取时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # 数据清洗：处理日期格式
            for date_field in ['申购日期', '上市时间', '到期日期', '股权登记日']:
                value = merged_item.get(date_field)
                if value and isinstance(value, str) and len(value) >= 8:
                    try:
                        merged_item[date_field] = f"{value[:4]}-{value[4:6]}-{value[6:8]}"
                    except:
                        pass
            
            merged_data.append(merged_item)
        
        print(f"✓ 数据合并完成，共 {len(merged_data)} 条记录")
        return merged_data
    
    def save_to_csv(self, data, filename=None):
        """保存数据到CSV文件"""
        
        if not data:
            print("✗ 无数据可保存")
            return ""
        
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"东方财富终极完整可转债_{timestamp}.csv"
        
        try:
            # 获取所有字段名
            fieldnames = list(data[0].keys())
            
            # 写入CSV文件
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(data)
            
            print(f"✓ 数据已保存到: {filename}")
            return filename
            
        except Exception as e:
            print(f"✗ 保存失败: {e}")
            return ""
    
    def display_summary(self, data):
        """显示数据汇总"""
        
        if not data:
            print("无数据")
            return
        
        print(f"\n{'='*80}")
        print(f"终极完整可转债数据汇总")
        print(f"{'='*80}")
        print(f"可转债总数: {len(data)}")
        
        # 状态统计
        status_count = {}
        for item in data:
            status = item.get('状态', '未知')
            status_count[status] = status_count.get(status, 0) + 1
        
        print(f"\n状态分布:")
        for status, count in status_count.items():
            percentage = count / len(data) * 100
            print(f"  {status}: {count} 只 ({percentage:.1f}%)")
        
        # 数据完整性统计
        with_price = len([item for item in data if item.get('债现价') and item.get('债现价') not in ['', '-']])
        with_convert = len([item for item in data if item.get('转股价值') and item.get('转股价值') not in ['', '-']])
        with_stock_price = len([item for item in data if item.get('正股价') and item.get('正股价') not in ['', '-']])
        with_premium = len([item for item in data if item.get('转股溢价率') and item.get('转股溢价率') not in ['', '-']])
        
        print(f"\n数据完整性:")
        print(f"  有债券价格: {with_price} 只 ({with_price/len(data)*100:.1f}%)")
        print(f"  有转股价值: {with_convert} 只 ({with_convert/len(data)*100:.1f}%)")
        print(f"  有正股价格: {with_stock_price} 只 ({with_stock_price/len(data)*100:.1f}%)")
        print(f"  有转股溢价率: {with_premium} 只 ({with_premium/len(data)*100:.1f}%)")
        
        # 显示有完整数据的活跃可转债
        active_complete = []
        for item in data:
            if (item.get('状态') == '正常' and item.get('债现价') and item.get('债现价') not in ['', '-']):
                active_complete.append(item)
        
        print(f"\n活跃可转债 (有实时价格): {len(active_complete)} 只")
        
        if active_complete:
            print(f"\n前10只活跃可转债:")
            for i, item in enumerate(active_complete[:10], 1):
                code = item.get('债券代码', '')
                name = item.get('债券简称', '')
                price = item.get('债现价', '')
                change = item.get('涨跌幅', '')
                stock_name = item.get('正股简称', '')
                rating = item.get('信用评级', '')
                
                print(f"  {i:2d}. {code} {name} 价格:{price} 涨幅:{change}% 正股:{stock_name} 评级:{rating}")
    
    def run(self):
        """运行爬虫"""
        print("🚀 东方财富终极完整可转债爬虫")
        print("=" * 60)
        
        try:
            # 1. 获取活跃可转债行情
            quotes_data = self.get_active_bonds_quotes()
            
            # 2. 获取所有可转债基础信息
            info_data = self.get_all_bonds_info()
            
            # 3. 合并数据
            merged_data = self.merge_data(quotes_data, info_data)
            
            if not merged_data:
                print("❌ 数据合并失败")
                return ""
            
            # 4. 显示汇总
            self.display_summary(merged_data)
            
            # 5. 保存数据
            filename = self.save_to_csv(merged_data)
            
            if filename:
                print(f"\n✅ 爬取完成！")
                print(f"📁 数据文件: {filename}")
                print(f"📊 可转债总数: {len(merged_data)} 只")
                print(f"💡 包含完整的基础信息 + 活跃可转债的实时行情")
                return filename
            else:
                print("❌ 数据保存失败")
                return ""
                
        except Exception as e:
            print(f"❌ 爬虫运行失败: {e}")
            import traceback
            traceback.print_exc()
            return ""


def main():
    """主函数"""
    spider = UltimateBondSpider()
    result_file = spider.run()
    
    if result_file:
        print(f"\n🎉 终极完整爬虫执行成功！")
        print(f"📁 数据文件: {result_file}")
        print(f"🔥 现在拥有最完整的可转债数据：基础信息 + 实时行情！")
    else:
        print(f"\n❌ 爬虫执行失败！")


if __name__ == "__main__":
    main()
