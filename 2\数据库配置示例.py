#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据库配置示例和测试数据生成

用于演示涨停正股转债查询功能
"""

import pandas as pd
from datetime import datetime, timedelta
import random


# 数据库配置示例
DATABASE_CONFIG = {
    'host': 'localhost',        # 数据库主机
    'port': 3306,              # 数据库端口
    'user': 'your_username',   # 用户名
    'password': 'your_password', # 密码
    'database': 'your_database', # 数据库名
    'charset': 'utf8mb4'
}

# MySQL连接字符串示例
MYSQL_CONNECTION_STRING = "mysql+pymysql://username:password@localhost:3306/database_name"


def generate_test_limit_up_data():
    """生成测试涨停数据"""
    
    # 一些有可转债的股票代码
    stocks_with_bonds = [
        '600326',  # 西藏天路 -> 110060 天路转债
        '600036',  # 招商银行 -> 110036 招行转债  
        '300058',  # 蓝色光标 -> 123001 蓝标转债
        '002475',  # 立讯精密 -> 128136 立讯转债
        '600519',  # 贵州茅台 -> 110020 茅台转债
        '000858',  # 五粮液 -> 123006 五粮液转债
        '600000',  # 浦发银行 -> 110059 浦发转债
        '601988',  # 中国银行 -> 113001 中行转债
    ]
    
    # 一些没有可转债的股票代码
    stocks_without_bonds = [
        '000001',  # 平安银行
        '000002',  # 万科A
        '600001',  # 邯郸钢铁
        '600004',  # 白云机场
    ]
    
    all_stocks = stocks_with_bonds + stocks_without_bonds
    
    # 生成最近10天的测试数据
    test_data = []
    end_date = datetime.now().date()
    
    for i in range(10):
        current_date = end_date - timedelta(days=i)
        
        # 随机选择一些股票涨停
        limit_up_stocks = random.sample(all_stocks, random.randint(2, 6))
        
        for stock in limit_up_stocks:
            # 生成涨停数据
            pre_close = round(random.uniform(10, 100), 2)
            close = round(pre_close * 1.1, 2)  # 涨停10%
            
            test_data.append({
                'symbol': stock,
                'day': current_date,
                'open': round(pre_close * random.uniform(1.05, 1.08), 2),
                'high': close,
                'low': round(pre_close * random.uniform(1.02, 1.05), 2),
                'close': close,
                'pre_close': pre_close,
                'volume': random.randint(1000000, 50000000),
                'limit_up': 1,
                'change_pct': 10.0
            })
    
    return pd.DataFrame(test_data)


def create_test_sql_file():
    """创建测试SQL文件"""
    
    test_data = generate_test_limit_up_data()
    
    sql_content = """-- 测试涨停数据
-- 用于演示涨停正股转债查询功能

"""
    
    for _, row in test_data.iterrows():
        sql = f"""INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('{row['symbol']}', '{row['day']}', {row['open']}, {row['high']}, {row['low']}, {row['close']}, {row['pre_close']}, {row['volume']}, {row['limit_up']});
"""
        sql_content += sql
    
    # 保存SQL文件
    with open('测试涨停数据.sql', 'w', encoding='utf-8') as f:
        f.write(sql_content)
    
    print(f"✓ 测试SQL文件已生成: 测试涨停数据.sql")
    print(f"✓ 包含 {len(test_data)} 条涨停记录")
    
    return test_data


def display_test_data_summary(test_data):
    """显示测试数据汇总"""
    
    print(f"\n{'='*60}")
    print(f"测试数据汇总")
    print(f"{'='*60}")
    print(f"总记录数: {len(test_data)}")
    print(f"涉及股票: {test_data['symbol'].nunique()} 只")
    print(f"日期范围: {test_data['day'].min()} 到 {test_data['day'].max()}")
    
    print(f"\n按股票统计涨停次数:")
    stock_counts = test_data['symbol'].value_counts()
    for stock, count in stock_counts.items():
        print(f"  {stock}: {count} 次")
    
    print(f"\n按日期统计涨停数量:")
    date_counts = test_data['day'].value_counts().sort_index(ascending=False)
    for date, count in date_counts.items():
        print(f"  {date}: {count} 只股票涨停")


def main():
    """主函数"""
    print("数据库配置示例和测试数据生成")
    print("=" * 50)
    
    print("\n1. 数据库配置示例:")
    print("-" * 30)
    for key, value in DATABASE_CONFIG.items():
        print(f"  {key}: {value}")
    
    print(f"\n2. 生成测试涨停数据:")
    print("-" * 30)
    test_data = create_test_sql_file()
    
    print(f"\n3. 测试数据预览:")
    print("-" * 30)
    print(test_data.head(10).to_string(index=False))
    
    display_test_data_summary(test_data)
    
    print(f"\n4. 使用说明:")
    print("-" * 30)
    print("1. 修改 DATABASE_CONFIG 中的数据库连接信息")
    print("2. 执行 测试涨停数据.sql 导入测试数据")
    print("3. 运行 涨停正股转债查询.py 进行查询")
    print("4. 查看涨停股票对应的可转债列表")


if __name__ == "__main__":
    main()
