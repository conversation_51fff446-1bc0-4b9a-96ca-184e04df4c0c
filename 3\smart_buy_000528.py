"""
智能买入000528脚本
结合GUI自动化和手动指导
"""
import sys
import os
import time
import pyautogui
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ths_trader import ThsTrader
from config import get_config

def smart_buy_000528():
    """智能买入000528"""
    print("=" * 60)
    print("🤖 智能买入 柳工(000528)")
    print("=" * 60)
    
    # 获取配置
    config = get_config()
    
    # 交易参数
    stock_code = '000528'
    stock_name = '柳工'
    buy_price = 8.52
    buy_amount = 100
    total_amount = buy_price * buy_amount
    
    print(f"📊 交易参数:")
    print(f"   股票代码: {stock_code}")
    print(f"   股票名称: {stock_name}")
    print(f"   买入价格: {buy_price}元")
    print(f"   买入数量: {buy_amount}股")
    print(f"   总金额: {total_amount}元")
    
    # 连接同花顺
    print(f"\n🔗 连接同花顺...")
    trader = ThsTrader(config)
    
    if not trader.login():
        print("❌ 同花顺连接失败")
        return False
    
    print("✅ 同花顺连接成功")
    
    # 尝试自动买入
    print(f"\n🤖 尝试自动买入...")
    try:
        success = trader.buy_stock(stock_code, stock_name, 1, buy_price)
        if success:
            print("✅ 自动买入成功！")
            return True
    except Exception as e:
        print(f"⚠️ 自动买入失败: {e}")
    
    # 自动买入失败，使用GUI自动化
    print(f"\n🖱️ 尝试GUI自动化...")
    
    try:
        # 给用户时间切换到同花顺窗口
        print("请确保同花顺买入界面已打开...")
        for i in range(5, 0, -1):
            print(f"   {i}秒后开始自动输入...")
            time.sleep(1)
        
        # 自动输入股票代码
        print("🔤 自动输入股票代码...")
        
        # 点击证券代码输入框（根据您的截图位置）
        # 这个坐标需要根据实际界面调整
        pyautogui.click(x=320, y=60)  # 证券代码输入框位置
        time.sleep(0.5)
        
        # 清空输入框
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(0.2)
        
        # 输入股票代码
        pyautogui.write(stock_code)
        time.sleep(0.5)
        
        # 按回车确认
        pyautogui.press('enter')
        time.sleep(1)
        
        print(f"✅ 已输入股票代码: {stock_code}")
        
        # 输入买入价格
        print("💰 自动输入买入价格...")
        pyautogui.click(x=320, y=98)  # 买入价格输入框位置
        time.sleep(0.5)
        pyautogui.hotkey('ctrl', 'a')
        pyautogui.write(str(buy_price))
        time.sleep(0.5)
        
        print(f"✅ 已输入买入价格: {buy_price}")
        
        # 输入买入数量
        print("📦 自动输入买入数量...")
        pyautogui.click(x=320, y=118)  # 买入数量输入框位置
        time.sleep(0.5)
        pyautogui.hotkey('ctrl', 'a')
        pyautogui.write(str(buy_amount))
        time.sleep(0.5)
        
        print(f"✅ 已输入买入数量: {buy_amount}")
        
        # 提示用户确认
        print(f"\n🔍 请检查同花顺界面中的信息:")
        print(f"   - 证券代码: {stock_code}")
        print(f"   - 证券名称: {stock_name}")
        print(f"   - 买入价格: {buy_price}")
        print(f"   - 买入数量: {buy_amount}")
        
        confirm = input("\n信息是否正确？确认买入请输入 'YES': ").strip()
        
        if confirm == 'YES':
            # 点击买入按钮
            print("🛒 点击买入按钮...")
            pyautogui.click(x=340, y=178)  # 买入按钮位置
            time.sleep(1)
            
            print("✅ 已点击买入按钮，请在同花顺中确认委托")
            return True
        else:
            print("❌ 用户取消买入")
            return False
            
    except Exception as e:
        print(f"❌ GUI自动化失败: {e}")
        
        # 最后的手动指导
        print(f"\n📋 请手动完成以下操作:")
        print(f"   1. 在证券代码框输入: {stock_code}")
        print(f"   2. 在买入价格框输入: {buy_price}")
        print(f"   3. 在买入数量框输入: {buy_amount}")
        print(f"   4. 点击买入按钮")
        
        manual_confirm = input("\n手动操作完成后按回车确认: ")
        return True

def main():
    """主函数"""
    try:
        print("智能买入程序 - 柳工(000528)")
        print("结合自动化和手动操作的混合模式")
        print()
        
        success = smart_buy_000528()
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 买入程序执行完成！")
            print("请在同花顺中查看委托状态和成交情况")
        else:
            print("❌ 买入程序执行失败")
            print("请手动在同花顺中完成买入操作")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n❌ 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")

if __name__ == "__main__":
    main()
