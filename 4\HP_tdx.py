"""
HP_tdx.py - 小白量化通达信模块替代实现
基于pytdx实现通达信数据接口
"""
import pandas as pd
from pytdx.hq import TdxHq_API
import os

class TdxConnection:
    """通达信连接类"""
    
    def __init__(self, ip='**************', port=7709):
        self.ip = ip
        self.port = port
        self.api = None
        self.connected = False
    
    def connect(self):
        """连接到通达信服务器"""
        try:
            self.api = TdxHq_API()
            self.api.connect(self.ip, self.port)
            self.connected = True
            print(f"连接通达信服务器成功: {self.ip}:{self.port}")
            return True
        except Exception as e:
            print(f"连接通达信服务器失败: {e}")
            self.connected = False
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.api and self.connected:
            self.api.disconnect()
            self.connected = False
            print("已断开通达信连接")

def TdxInit(ip='**************', port=7709):
    """
    初始化通达信连接
    
    Args:
        ip: 服务器IP
        port: 服务器端口
        
    Returns:
        TdxConnection: 连接对象
    """
    conn = TdxConnection(ip, port)
    conn.connect()
    return conn

def get_market(code):
    """
    根据股票代码获取市场代码
    
    Args:
        code: 股票代码
        
    Returns:
        int: 市场代码 (0=深圳, 1=上海)
    """
    if code.startswith('6'):
        return 1  # 上海
    elif code.startswith(('0', '3')):
        return 0  # 深圳
    elif code.startswith('8'):
        return 0  # 北交所
    else:
        return 0  # 默认深圳

def get_security_bars(nCategory=9, nMarket=1, code='000001', nStart=0, nCount=100):
    """
    获取K线数据
    
    Args:
        nCategory: K线类型 (4=1分钟, 5=5分钟, 6=15分钟, 7=30分钟, 8=小时, 9=日线)
        nMarket: 市场代码 (0=深圳, 1=上海)
        code: 股票代码
        nStart: 开始位置
        nCount: 获取数量
        
    Returns:
        DataFrame: K线数据
    """
    try:
        # 创建临时连接
        api = TdxHq_API()
        api.connect('**************', 7709)
        
        # 获取数据
        data = api.get_security_bars(nCategory, nMarket, code, nStart, nCount)
        
        # 断开连接
        api.disconnect()
        
        if data:
            # 转换为DataFrame
            df = pd.DataFrame(data)
            # 检查实际列数并重命名
            if len(df.columns) >= 7:
                df.columns = ['datetime', 'open', 'close', 'high', 'low', 'volume', 'amount']
            else:
                # 根据实际列数调整
                column_names = ['datetime', 'open', 'close', 'high', 'low', 'volume', 'amount']
                df.columns = column_names[:len(df.columns)]

            # 转换数据类型
            if 'datetime' in df.columns:
                df['datetime'] = pd.to_datetime(df['datetime'])

            # 转换数值列
            numeric_cols = [col for col in ['open', 'close', 'high', 'low', 'volume', 'amount'] if col in df.columns]
            if numeric_cols:
                df[numeric_cols] = df[numeric_cols].astype(float)
            
            return df
        else:
            print(f"获取K线数据失败: {code}")
            return pd.DataFrame()
            
    except Exception as e:
        print(f"获取K线数据异常: {e}")
        return pd.DataFrame()

def getzxgfile(filename):
    """
    读取自选股文件
    
    Args:
        filename: 自选股文件路径
        
    Returns:
        list: [(market, code), ...] 格式的股票列表
    """
    codes = []
    
    try:
        if not os.path.exists(filename):
            print(f"自选股文件不存在: {filename}")
            # 返回一些默认股票
            return [
                (1, '600519'),  # 贵州茅台
                (0, '000001'),  # 平安银行
                (0, '000002'),  # 万科A
                (1, '600036'),  # 招商银行
                (1, '600000'),  # 浦发银行
            ]
        
        # 尝试读取.blk文件（通达信自选股格式）
        with open(filename, 'rb') as f:
            data = f.read()
            
        # 简单解析（实际.blk格式比较复杂）
        # 这里提供一个基础实现
        if len(data) > 0:
            # 假设每个股票代码占用固定字节
            # 实际需要根据具体格式解析
            print(f"读取自选股文件: {filename}, 大小: {len(data)} 字节")
            
            # 返回一些示例股票
            codes = [
                (1, '600519'),  # 贵州茅台
                (0, '000001'),  # 平安银行
                (0, '000002'),  # 万科A
                (1, '600036'),  # 招商银行
                (1, '600000'),  # 浦发银行
                (0, '000858'),  # 五粮液
                (1, '600276'),  # 恒瑞医药
                (0, '002415'),  # 海康威视
            ]
        
    except Exception as e:
        print(f"读取自选股文件失败: {e}")
        # 返回默认股票池
        codes = [
            (1, '600519'),  # 贵州茅台
            (0, '000001'),  # 平安银行
            (0, '000002'),  # 万科A
        ]
    
    print(f"加载股票池: {len(codes)} 只股票")
    return codes

def get_stock_info(market, code):
    """
    获取股票基本信息
    
    Args:
        market: 市场代码
        code: 股票代码
        
    Returns:
        dict: 股票信息
    """
    try:
        api = TdxHq_API()
        api.connect('**************', 7709)
        
        # 获取股票信息
        quotes = api.get_security_quotes([(market, code)])
        
        api.disconnect()
        
        if quotes:
            quote = quotes[0]
            return {
                'code': quote['code'],
                'name': quote.get('name', ''),
                'price': quote.get('price', 0),
                'last_close': quote.get('last_close', 0),
                'open': quote.get('open', 0),
                'high': quote.get('high', 0),
                'low': quote.get('low', 0),
                'volume': quote.get('vol', 0),
                'amount': quote.get('amount', 0),
            }
        else:
            return None
            
    except Exception as e:
        print(f"获取股票信息失败: {e}")
        return None

if __name__ == "__main__":
    # 测试代码
    print("HP_tdx 模块测试")
    
    # 测试连接
    hq = TdxInit()
    if hq.connected:
        print("通达信连接测试成功")
        
        # 测试获取K线数据
        df = get_security_bars(nCategory=9, nMarket=1, code='600519', nCount=10)
        if not df.empty:
            print("K线数据获取成功:")
            print(df.head())
        
        # 测试获取股票信息
        info = get_stock_info(1, '600519')
        if info:
            print("股票信息获取成功:")
            print(info)
        
        hq.disconnect()
    
    # 测试自选股文件读取
    codes = getzxgfile('test.blk')  # 测试不存在的文件
    print(f"自选股测试: {codes}")
