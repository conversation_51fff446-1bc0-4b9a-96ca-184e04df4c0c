#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
纯真实数据可转债工具

完全基于真实接口数据，不包含任何模拟数据
"""

import pandas as pd
from datetime import datetime
from typing import Dict, List, Optional, Union

try:
    from mootdx.quotes import Quotes
    MOOTDX_AVAILABLE = True
except ImportError:
    print("错误: 无法导入 mootdx 库")
    print("请安装: pip install mootdx")
    MOOTDX_AVAILABLE = False


class PureRealDataTool:
    """纯真实数据工具"""
    
    def __init__(self, market='std', timeout=30):
        """
        初始化工具
        
        Args:
            market: 市场类型
            timeout: 超时时间
        """
        if not MOOTDX_AVAILABLE:
            raise ImportError("mootdx 库未安装，请运行: pip install mootdx")
        
        self.market = market
        self.timeout = timeout
        self.client = None
        self.connected = False
        
        self._connect()
    
    def _connect(self):
        """连接到数据服务器"""
        try:
            print("正在连接到真实数据服务器...")
            self.client = Quotes.factory(
                market=self.market,
                timeout=self.timeout
            )
            self.connected = True
            print("✓ 成功连接到真实数据服务器")
        except Exception as e:
            print(f"✗ 连接失败: {e}")
            self.connected = False
            raise ConnectionError(f"无法连接到真实数据服务器: {e}")
    
    def is_convertible_bond(self, code: str) -> bool:
        """
        判断是否为可转债代码
        
        Args:
            code: 股票代码
            
        Returns:
            bool: 是否为可转债
        """
        if not code or len(code) != 6:
            return False
        
        # 上海可转债：110xxx, 113xxx, 118xxx
        # 深圳可转债：123xxx, 127xxx, 128xxx
        bond_prefixes = ['110', '113', '118', '123', '127', '128']
        return code[:3] in bond_prefixes
    
    def get_bond_market(self, code: str) -> int:
        """
        获取可转债所属市场
        
        Args:
            code: 可转债代码
            
        Returns:
            int: 市场代码，1=上海，0=深圳
        """
        if code.startswith(('110', '113', '118')):
            return 1  # 上海
        elif code.startswith(('123', '127', '128')):
            return 0  # 深圳
        else:
            return -1  # 未知
    
    def get_real_bond_quotes(self, bond_codes: Union[str, List[str]]) -> pd.DataFrame:
        """
        获取可转债真实行情数据
        
        Args:
            bond_codes: 可转债代码或代码列表
            
        Returns:
            pd.DataFrame: 真实行情数据
        """
        if not self.connected:
            raise ConnectionError("未连接到数据服务器")
        
        if isinstance(bond_codes, str):
            bond_codes = [bond_codes]
        
        # 过滤有效的可转债代码
        valid_codes = [code for code in bond_codes if self.is_convertible_bond(code)]
        
        if not valid_codes:
            print("没有有效的可转债代码")
            return pd.DataFrame()
        
        all_data = []
        
        for code in valid_codes:
            try:
                market = self.get_bond_market(code)
                if market == -1:
                    print(f"无法识别可转债市场: {code}")
                    continue
                
                quotes = self.client.quotes(symbol=code, market=market)
                if not quotes.empty:
                    data = quotes.iloc[0].to_dict()
                    data['bond_code'] = code
                    data['market'] = '上海' if market == 1 else '深圳'
                    data['timestamp'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    all_data.append(data)
                    print(f"✓ 获取 {code} 行情成功")
                else:
                    print(f"✗ 未获取到 {code} 行情数据")
            except Exception as e:
                print(f"✗ 获取 {code} 行情失败: {e}")
        
        if all_data:
            return pd.DataFrame(all_data)
        else:
            print("未获取到任何真实行情数据")
            return pd.DataFrame()
    
    def get_real_stock_quotes(self, stock_codes: Union[str, List[str]]) -> pd.DataFrame:
        """
        获取正股真实行情数据
        
        Args:
            stock_codes: 正股代码或代码列表
            
        Returns:
            pd.DataFrame: 真实行情数据
        """
        if not self.connected:
            raise ConnectionError("未连接到数据服务器")
        
        if isinstance(stock_codes, str):
            stock_codes = [stock_codes]
        
        all_data = []
        
        for code in stock_codes:
            try:
                # 判断市场
                if code.startswith(('60', '68', '11', '12', '13')):
                    market = 1  # 上海
                elif code.startswith(('00', '30', '20')):
                    market = 0  # 深圳
                else:
                    print(f"无法识别股票市场: {code}")
                    continue
                
                quotes = self.client.quotes(symbol=code, market=market)
                if not quotes.empty:
                    data = quotes.iloc[0].to_dict()
                    data['stock_code'] = code
                    data['market'] = '上海' if market == 1 else '深圳'
                    data['timestamp'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    all_data.append(data)
                    print(f"✓ 获取 {code} 行情成功")
                else:
                    print(f"✗ 未获取到 {code} 行情数据")
            except Exception as e:
                print(f"✗ 获取 {code} 行情失败: {e}")
        
        if all_data:
            return pd.DataFrame(all_data)
        else:
            print("未获取到任何真实行情数据")
            return pd.DataFrame()
    
    def get_real_kline(self, code: str, frequency='1d', count=100, is_bond=True) -> pd.DataFrame:
        """
        获取真实K线数据
        
        Args:
            code: 证券代码
            frequency: 数据频率
            count: 数据条数
            is_bond: 是否为可转债
            
        Returns:
            pd.DataFrame: 真实K线数据
        """
        if not self.connected:
            raise ConnectionError("未连接到数据服务器")
        
        try:
            # 判断市场
            if is_bond:
                market = self.get_bond_market(code)
            else:
                if code.startswith(('60', '68', '11', '12', '13')):
                    market = 1  # 上海
                elif code.startswith(('00', '30', '20')):
                    market = 0  # 深圳
                else:
                    raise ValueError(f"无法识别证券市场: {code}")
            
            if market == -1:
                raise ValueError(f"无法识别证券市场: {code}")
            
            # 转换频率参数
            freq_map = {
                '1m': 8, '5m': 0, '15m': 1, '30m': 2,
                '1h': 3, '1d': 9, 'daily': 9
            }
            freq_code = freq_map.get(frequency, 9)
            
            kline = self.client.bars(
                symbol=code,
                market=market,
                frequency=freq_code,
                offset=count
            )
            
            if not kline.empty:
                kline['code'] = code
                kline['market'] = '上海' if market == 1 else '深圳'
                kline['type'] = '可转债' if is_bond else '股票'
                print(f"✓ 获取 {code} K线数据成功，共 {len(kline)} 条")
                return kline
            else:
                print(f"✗ 未获取到 {code} K线数据")
                return pd.DataFrame()
        except Exception as e:
            print(f"✗ 获取 {code} K线数据失败: {e}")
            return pd.DataFrame()
    
    def batch_get_quotes(self, codes: List[str], code_type='bond') -> pd.DataFrame:
        """
        批量获取真实行情数据
        
        Args:
            codes: 代码列表
            code_type: 代码类型，'bond' 或 'stock'
            
        Returns:
            pd.DataFrame: 批量真实行情数据
        """
        print(f"开始批量获取 {len(codes)} 个{code_type}的真实行情数据...")
        
        if code_type == 'bond':
            return self.get_real_bond_quotes(codes)
        else:
            return self.get_real_stock_quotes(codes)
    
    def export_real_data(self, data: pd.DataFrame, filename: str = None) -> str:
        """
        导出真实数据
        
        Args:
            data: 要导出的数据
            filename: 文件名
            
        Returns:
            str: 导出的文件路径
        """
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"真实数据_{timestamp}.csv"
        
        try:
            data.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"✓ 真实数据已导出到: {filename}")
            return filename
        except Exception as e:
            print(f"✗ 导出数据失败: {e}")
            return ""
    
    def close(self):
        """关闭连接"""
        if self.client:
            try:
                self.client.close()
                print("✓ 已关闭真实数据连接")
            except:
                pass
            self.connected = False


def demo_pure_real_data():
    """演示纯真实数据功能"""
    print("=" * 60)
    print("纯真实数据可转债工具演示")
    print("=" * 60)
    
    try:
        # 初始化工具
        tool = PureRealDataTool()
        
        # 测试数据
        test_bonds = ['128136', '110001']
        test_stocks = ['002475', '601988']
        
        print("\n1. 获取可转债真实行情")
        print("-" * 40)
        bond_quotes = tool.get_real_bond_quotes(test_bonds)
        if not bond_quotes.empty:
            print(f"成功获取 {len(bond_quotes)} 条可转债行情数据")
            if 'price' in bond_quotes.columns:
                for _, row in bond_quotes.iterrows():
                    print(f"  {row['bond_code']}: 价格 {row['price']}, 市场 {row['market']}")
        
        print("\n2. 获取正股真实行情")
        print("-" * 40)
        stock_quotes = tool.get_real_stock_quotes(test_stocks)
        if not stock_quotes.empty:
            print(f"成功获取 {len(stock_quotes)} 条正股行情数据")
            if 'price' in stock_quotes.columns:
                for _, row in stock_quotes.iterrows():
                    print(f"  {row['stock_code']}: 价格 {row['price']}, 市场 {row['market']}")
        
        print("\n3. 获取真实K线数据")
        print("-" * 40)
        if test_bonds:
            kline = tool.get_real_kline(test_bonds[0], frequency='1d', count=5, is_bond=True)
            if not kline.empty and 'close' in kline.columns:
                print(f"最近5日收盘价: {list(kline['close'])}")
        
        print("\n4. 导出真实数据")
        print("-" * 40)
        if not bond_quotes.empty:
            filename = tool.export_real_data(bond_quotes, "可转债真实行情.csv")
            if filename:
                print(f"数据已导出: {filename}")
        
        # 关闭连接
        tool.close()
        
    except ConnectionError as e:
        print(f"连接错误: {e}")
        print("请检查网络连接和 mootdx 服务器状态")
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n演示完成！")


if __name__ == "__main__":
    demo_pure_real_data()
