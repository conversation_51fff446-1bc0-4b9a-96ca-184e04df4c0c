# 沪深可转债数据获取工具

这是一套完整的可转债数据获取和分析工具，基于 mootdx 库实现，支持获取沪深两市可转债的实时行情、历史数据和基本信息。

## 功能特性

### 📊 数据获取功能
- ✅ **实时行情**: 获取可转债实时价格、成交量等行情数据
- ✅ **历史K线**: 支持分钟级和日级K线数据获取
- ✅ **基本信息**: 获取可转债基本信息（转股价、到期日等）
- ✅ **市场识别**: 自动识别上海/深圳市场

### 🔍 分析功能
- ✅ **价值分析**: 分析可转债投资价值和风险
- ✅ **行情监控**: 实时监控多只可转债行情变化
- ✅ **筛选工具**: 根据价格、成交量等条件筛选可转债
- ✅ **数据导出**: 支持导出到 CSV/Excel 文件

### 🎯 支持的可转债类型
- **上海可转债**: 110xxx, 113xxx, 118xxx
- **深圳可转债**: 123xxx, 127xxx, 128xxx

## 文件结构

```
├── 获取可转债数据.py        # 核心数据获取工具
├── 可转债分析工具.py        # 数据分析和监控工具
└── README.md               # 说明文档
```

## 安装依赖

```bash
pip install mootdx pandas numpy openpyxl
```

## 快速开始

### 1. 基础数据获取

```python
from 获取可转债数据 import ConvertibleBondData

# 初始化工具
bond_tool = ConvertibleBondData()

# 获取可转债实时行情
quotes = bond_tool.get_bond_quotes(['110001', '128136'])
print(quotes)

# 获取K线数据
kline = bond_tool.get_bond_kline('110001', frequency='1d', count=30)
print(kline)

# 获取基本信息
info = bond_tool.get_bond_info(['110001', '128136'])
print(info)
```

### 2. 数据分析工具

```python
from 可转债分析工具 import BondAnalyzer

# 初始化分析器
analyzer = BondAnalyzer()

# 监控热门可转债
hot_bonds = ['110001', '128136', '123001']
quotes = analyzer.monitor_bonds(hot_bonds)

# 分析单只可转债价值
analysis = analyzer.analyze_bond_value('110001')
print(analysis['recommendation'])

# 筛选可转债
criteria = {'min_price': 95, 'max_price': 115}
filtered = analyzer.screen_bonds(criteria)
```

## 运行示例

### 1. 运行基础演示
```bash
python 获取可转债数据.py
```

### 2. 运行分析工具
```bash
python 可转债分析工具.py
```

### 3. 交互式模式
```bash
python 获取可转债数据.py --interactive
```

## API 参考

### ConvertibleBondData 类

#### 主要方法

- `get_bond_quotes(codes)`: 获取可转债实时行情
- `get_bond_kline(code, frequency, count)`: 获取K线数据
- `get_bond_info(codes)`: 获取基本信息
- `is_convertible_bond(code)`: 判断是否为可转债代码
- `get_bond_market(code)`: 获取所属市场
- `search_bonds(keyword)`: 搜索可转债

#### 参数说明

**frequency 参数**:
- `'1m'`: 1分钟K线
- `'5m'`: 5分钟K线
- `'15m'`: 15分钟K线
- `'30m'`: 30分钟K线
- `'1h'`: 1小时K线
- `'1d'`: 日K线

### BondAnalyzer 类

#### 主要方法

- `monitor_bonds(codes, save_to_file)`: 监控可转债行情
- `analyze_bond_value(code)`: 分析投资价值
- `screen_bonds(criteria)`: 筛选可转债
- `export_data(data, filename)`: 导出数据

#### 筛选条件示例

```python
criteria = {
    'min_price': 90,      # 最低价格
    'max_price': 120,     # 最高价格
    'min_volume': 1000    # 最小成交量
}
```

## 数据字段说明

### 行情数据字段
- `bond_code`: 可转债代码
- `market`: 所属市场（上海/深圳）
- `price`: 当前价格
- `open`: 开盘价
- `high`: 最高价
- `low`: 最低价
- `volume`: 成交量
- `amount`: 成交额

### 基本信息字段
- `bond_name`: 可转债名称
- `current_price`: 当前价格
- `conversion_price`: 转股价格
- `conversion_ratio`: 转股比例
- `maturity_date`: 到期日
- `issue_date`: 发行日
- `bond_rating`: 债券评级
- `interest_rate`: 票面利率

## 使用示例

### 示例1: 监控热门可转债

```python
from 可转债分析工具 import BondAnalyzer

analyzer = BondAnalyzer()

# 定义热门可转债
hot_bonds = [
    '110001',  # 中行转债
    '113050',  # 南银转债
    '128136',  # 立讯转债
    '123001',  # 蓝标转债
]

# 监控行情
quotes = analyzer.monitor_bonds(hot_bonds, save_to_file=True)
print("热门可转债行情:")
print(quotes[['bond_code', 'market', 'price', 'change_pct']])
```

### 示例2: 筛选低价可转债

```python
# 筛选价格在90-110之间的可转债
criteria = {
    'min_price': 90,
    'max_price': 110,
    'min_volume': 500
}

filtered_bonds = analyzer.screen_bonds(criteria)
print(f"筛选出 {len(filtered_bonds)} 只低价可转债")
```

### 示例3: 分析投资价值

```python
# 分析特定可转债的投资价值
code = '128136'
analysis = analyzer.analyze_bond_value(code)

print(f"可转债 {code} 分析结果:")
print(f"当前价格: {analysis['price_analysis']['current_price']}")
print(f"30日均价: {analysis['price_analysis']['avg_30d']}")
print(f"投资建议: {analysis['recommendation']}")
```

## 注意事项

1. **数据时效性**: 行情数据实时性取决于 mootdx 服务器状态
2. **请求频率**: 避免过于频繁的数据请求，建议间隔1秒以上
3. **代码有效性**: 部分可转债代码可能已退市或暂停交易
4. **网络连接**: 需要稳定的网络连接访问行情服务器

## 错误处理

工具内置了完善的错误处理机制：

- **连接失败**: 自动切换到模拟模式
- **数据异常**: 返回空DataFrame并提示错误信息
- **代码无效**: 自动过滤无效的可转债代码
- **文件导出**: 支持多种格式的数据导出

## 扩展功能

### 自定义分析指标

可以基于现有框架扩展更多分析指标：

```python
def custom_analysis(self, code):
    """自定义分析函数"""
    kline = self.bond_tool.get_bond_kline(code, count=60)
    
    # 计算技术指标
    ma5 = kline['close'].rolling(5).mean()
    ma20 = kline['close'].rolling(20).mean()
    
    # 自定义分析逻辑
    signal = "买入" if ma5.iloc[-1] > ma20.iloc[-1] else "观望"
    
    return {
        'signal': signal,
        'ma5': ma5.iloc[-1],
        'ma20': ma20.iloc[-1]
    }
```

## 许可证

MIT License

## 更新日志

### v1.0.0 (2025-08-06)
- 初始版本发布
- 支持沪深可转债数据获取
- 实现基础分析功能
- 提供完整的使用示例
