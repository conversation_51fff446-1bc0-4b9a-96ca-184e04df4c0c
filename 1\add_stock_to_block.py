#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
向指定板块添加股票的脚本

功能：向"测试板块"添加股票 600789
"""

from stock_crud_tool import StockCRUDTool


def add_stock_600789_to_test_block():
    """向测试板块添加股票 600789"""
    print("=" * 50)
    print("向测试板块添加股票 600789")
    print("=" * 50)
    
    # 使用你的通达信目录
    tdx_dir = "D:/zyb(6.3.5 7.66)M"
    
    # 初始化 CRUD 工具
    crud_tool = StockCRUDTool(tdxdir=tdx_dir)
    
    block_name = "测试板块"
    new_stock = "600789"
    
    print(f"通达信目录: {tdx_dir}")
    print(f"目标板块: {block_name}")
    print(f"要添加的股票: {new_stock}")
    
    # 1. 先查询当前板块内容
    print(f"\n1. 查询板块 '{block_name}' 当前内容")
    result = crud_tool.get_block_stocks(block_name)
    
    if result['success']:
        print(f"✓ 当前板块包含 {result['stock_count']} 只股票")
        print(f"股票列表: {result['stocks']}")
        
        # 检查股票是否已存在
        if new_stock in result['stocks']:
            print(f"⚠️  股票 {new_stock} 已存在于板块中，无需添加")
            return
    else:
        print(f"✗ 查询失败: {result['message']}")
        
        # 如果板块不存在，询问是否创建
        create_new = input(f"板块 '{block_name}' 不存在，是否创建新板块？(y/n): ").lower().strip()
        if create_new == 'y':
            print(f"\n创建新板块 '{block_name}' 并添加股票 {new_stock}")
            result = crud_tool.create_block_with_stocks(block_name, [new_stock])
            print(f"结果: {result['message']}")
            return
        else:
            print("操作取消")
            return
    
    # 2. 添加新股票
    print(f"\n2. 向板块 '{block_name}' 添加股票 {new_stock}")
    result = crud_tool.add_stocks_to_block(block_name, [new_stock])
    
    if result['success']:
        print(f"✓ {result['message']}")
        print(f"新增股票: {result['added_codes']}")
        if result['duplicate_codes']:
            print(f"重复股票: {result['duplicate_codes']}")
        if result['invalid_codes']:
            print(f"无效股票: {result['invalid_codes']}")
    else:
        print(f"✗ 添加失败: {result['message']}")
        return
    
    # 3. 验证添加结果
    print(f"\n3. 验证添加结果")
    result = crud_tool.get_block_stocks(block_name)
    
    if result['success']:
        print(f"✓ 板块 '{block_name}' 现在包含 {result['stock_count']} 只股票")
        print(f"最终股票列表: {result['stocks']}")
        
        # 确认目标股票已添加
        if new_stock in result['stocks']:
            print(f"✓ 确认股票 {new_stock} 已成功添加到板块中")
        else:
            print(f"✗ 股票 {new_stock} 未在板块中找到")
    else:
        print(f"✗ 验证失败: {result['message']}")
    
    print(f"\n操作完成！")


def interactive_add_stock():
    """交互式添加股票"""
    print("=" * 50)
    print("交互式股票添加工具")
    print("=" * 50)
    
    # 获取用户输入
    block_name = input("请输入板块名称 (默认: 测试板块): ").strip()
    if not block_name:
        block_name = "测试板块"
    
    stock_code = input("请输入股票代码 (默认: 600789): ").strip()
    if not stock_code:
        stock_code = "600789"
    
    # 验证股票代码格式
    import re
    if not re.match(r'^[0-9]{6}$', stock_code):
        print(f"✗ 无效的股票代码格式: {stock_code}")
        print("股票代码应为6位数字")
        return
    
    tdx_dir = "D:/zyb(6.3.5 7.66)M"
    
    print(f"\n配置信息:")
    print(f"通达信目录: {tdx_dir}")
    print(f"目标板块: {block_name}")
    print(f"要添加的股票: {stock_code}")
    
    confirm = input("\n确认执行操作？(y/n): ").lower().strip()
    if confirm != 'y':
        print("操作取消")
        return
    
    # 执行添加操作
    crud_tool = StockCRUDTool(tdxdir=tdx_dir)
    
    # 查询板块
    result = crud_tool.get_block_stocks(block_name)
    
    if result['success']:
        print(f"\n当前板块包含股票: {result['stocks']}")
        
        # 添加股票
        result = crud_tool.add_stocks_to_block(block_name, [stock_code])
        print(f"添加结果: {result['message']}")
        
        # 验证结果
        result = crud_tool.get_block_stocks(block_name)
        if result['success']:
            print(f"最终股票列表: {result['stocks']}")
    else:
        print(f"板块不存在: {result['message']}")
        create_new = input("是否创建新板块？(y/n): ").lower().strip()
        if create_new == 'y':
            result = crud_tool.create_block_with_stocks(block_name, [stock_code])
            print(f"创建结果: {result['message']}")


def batch_add_stocks():
    """批量添加股票"""
    print("=" * 50)
    print("批量添加股票到测试板块")
    print("=" * 50)
    
    tdx_dir = "D:/zyb(6.3.5 7.66)M"
    block_name = "测试板块"
    
    # 要添加的股票列表
    stocks_to_add = [
        "600789",  # 鲁抗医药
        "600036",  # 招商银行
        "000001",  # 平安银行
        "600519",  # 贵州茅台
    ]
    
    print(f"目标板块: {block_name}")
    print(f"要添加的股票: {stocks_to_add}")
    
    crud_tool = StockCRUDTool(tdxdir=tdx_dir)
    
    # 查询当前板块
    result = crud_tool.get_block_stocks(block_name)
    
    if result['success']:
        print(f"\n当前板块包含: {result['stocks']}")
        
        # 批量添加
        result = crud_tool.add_stocks_to_block(block_name, stocks_to_add)
        print(f"\n批量添加结果: {result['message']}")
        
        if result['added_codes']:
            print(f"成功添加: {result['added_codes']}")
        if result['duplicate_codes']:
            print(f"重复跳过: {result['duplicate_codes']}")
        if result['invalid_codes']:
            print(f"无效代码: {result['invalid_codes']}")
        
        # 最终验证
        result = crud_tool.get_block_stocks(block_name)
        if result['success']:
            print(f"\n最终板块内容: {result['stocks']}")
            print(f"总股票数: {result['stock_count']}")
    else:
        print(f"板块不存在，创建新板块...")
        result = crud_tool.create_block_with_stocks(block_name, stocks_to_add)
        print(f"创建结果: {result['message']}")


def main():
    """主函数"""
    print("股票板块管理工具")
    print("=" * 50)
    print("1. 向测试板块添加股票 600789")
    print("2. 交互式添加股票")
    print("3. 批量添加股票")
    print("=" * 50)
    
    choice = input("请选择操作 (1-3): ").strip()
    
    if choice == "1":
        add_stock_600789_to_test_block()
    elif choice == "2":
        interactive_add_stock()
    elif choice == "3":
        batch_add_stocks()
    else:
        print("无效选择，默认执行添加 600789 到测试板块")
        add_stock_600789_to_test_block()


if __name__ == "__main__":
    main()
