import csv
import os
from typing import List, Dict
from uuid import uuid4

from api_config import CFG

QUEUE_PATH = CFG['activework_path']
QUEUE_FIELDS = CFG['activework_field']

LOG_PATH = CFG['workdatalog_path']
LOG_FIELDS = CFG['workdata_field']

os.makedirs(os.path.dirname(QUEUE_PATH), exist_ok=True)

# 初始化CSV文件
if not os.path.exists(QUEUE_PATH):
    with open(QUEUE_PATH, 'w', newline='', encoding='utf-8-sig') as f:
        writer = csv.DictWriter(f, fieldnames=QUEUE_FIELDS)
        writer.writeheader()

if not os.path.exists(LOG_PATH):
    with open(LOG_PATH, 'w', newline='', encoding='utf-8-sig') as f:
        writer = csv.DictWriter(f, fieldnames=LOG_FIELDS)
        writer.writeheader()


def add_to_queue(items: List[Dict]):
    """添加到工作队列，status=0 表示未执行"""
    with open(QUEUE_PATH, 'a', newline='', encoding='utf-8-sig') as f:
        writer = csv.DictWriter(f, fieldnames=QUEUE_FIELDS)
        for it in items:
            row = {
                'key': it.get('key') or str(uuid4()),
                'strategy_no': it['strategy_no'],
                'stock_no': it['code'],
                'stock_name': it.get('name', ''),
                'amount': int(it['ct_amount']),
                'operate': it['operate'],
                'price': it.get('price', ''),
                'status': 0,
            }
            writer.writerow(row)


def read_queue() -> List[Dict]:
    rows = []
    with open(QUEUE_PATH, 'r', newline='', encoding='utf-8-sig') as f:
        reader = csv.DictReader(f)
        for r in reader:
            rows.append(r)
    return rows


def update_queue(rows: List[Dict]):
    with open(QUEUE_PATH, 'w', newline='', encoding='utf-8-sig') as f:
        writer = csv.DictWriter(f, fieldnames=QUEUE_FIELDS)
        writer.writeheader()
        for r in rows:
            writer.writerow(r)


def append_log(row: Dict):
    with open(LOG_PATH, 'a', newline='', encoding='utf-8-sig') as f:
        writer = csv.DictWriter(f, fieldnames=LOG_FIELDS)
        writer.writerow(row)

