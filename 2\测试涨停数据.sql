-- 测试涨停数据
-- 用于演示涨停正股转债查询功能

INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('600036', '2025-08-07', 11.59, 11.81, 11.13, 11.81, 10.74, 8821536, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('000858', '2025-08-07', 43.36, 44.88, 42.13, 44.88, 40.8, 29415931, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('600001', '2025-08-07', 48.51, 49.47, 46.64, 49.47, 44.97, 25012783, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('600000', '2025-08-07', 23.83, 24.86, 23.23, 24.86, 22.6, 8822183, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('300058', '2025-08-07', 93.41, 95.73, 89.01, 95.73, 87.03, 10143447, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('600519', '2025-08-07', 104.83, 107.06, 100.42, 107.06, 97.33, 11928054, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('000002', '2025-08-06', 101.07, 104.75, 99.38, 104.75, 95.23, 12812347, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('600036', '2025-08-06', 14.29, 14.77, 14.09, 14.77, 13.43, 16223534, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('600001', '2025-08-06', 78.79, 81.51, 76.19, 81.51, 74.1, 47213860, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('600519', '2025-08-05', 36.96, 37.95, 36.11, 37.95, 34.5, 39532131, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('000858', '2025-08-05', 20.78, 21.46, 20.23, 21.46, 19.51, 42952998, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('300058', '2025-08-05', 26.54, 27.63, 26.35, 27.63, 25.12, 17130353, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('600001', '2025-08-05', 62.14, 63.54, 59.11, 63.54, 57.76, 39611333, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('600036', '2025-08-05', 67.17, 69.76, 65.38, 69.76, 63.42, 11988839, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('600004', '2025-08-04', 64.89, 66.43, 63.07, 66.43, 60.39, 24159439, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('000001', '2025-08-04', 32.13, 33.4, 31.86, 33.4, 30.36, 2251142, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('000002', '2025-08-03', 13.15, 13.56, 12.84, 13.56, 12.33, 21435699, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('600036', '2025-08-03', 106.81, 109.33, 102.66, 109.33, 99.39, 22459113, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('000001', '2025-08-03', 27.0, 27.64, 25.66, 27.64, 25.13, 6089609, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('600326', '2025-08-03', 76.89, 79.17, 75.42, 79.17, 71.97, 34589217, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('000858', '2025-08-03', 14.65, 15.03, 14.18, 15.03, 13.66, 12676167, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('601988', '2025-08-03', 99.71, 103.38, 97.4, 103.38, 93.98, 5843115, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('000858', '2025-08-02', 22.87, 23.9, 22.43, 23.9, 21.73, 25940638, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('000001', '2025-08-02', 28.51, 29.77, 28.29, 29.77, 27.06, 47496414, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('600036', '2025-08-02', 91.74, 94.27, 88.4, 94.27, 85.7, 31993421, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('600001', '2025-08-02', 61.71, 62.91, 59.36, 62.91, 57.19, 34142220, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('601988', '2025-08-02', 46.54, 47.55, 44.69, 47.55, 43.23, 13160822, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('600000', '2025-08-01', 93.3, 96.13, 89.2, 96.13, 87.39, 26318571, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('600001', '2025-08-01', 71.3, 73.69, 68.81, 73.69, 66.99, 2541044, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('600004', '2025-08-01', 92.11, 94.22, 88.68, 94.22, 85.65, 29468843, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('601988', '2025-08-01', 51.28, 52.97, 49.57, 52.97, 48.15, 44912940, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('600326', '2025-07-31', 82.86, 85.81, 80.88, 85.81, 78.01, 24655397, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('000002', '2025-07-31', 92.1, 94.37, 88.17, 94.37, 85.79, 43602279, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('600004', '2025-07-30', 95.29, 99.73, 94.05, 99.73, 90.66, 3545143, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('002475', '2025-07-30', 88.73, 90.59, 85.82, 90.59, 82.35, 15681524, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('600036', '2025-07-30', 45.23, 47.33, 44.06, 47.33, 43.03, 16880815, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('601988', '2025-07-30', 102.52, 106.45, 100.89, 106.45, 96.77, 48237525, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('600326', '2025-07-30', 42.27, 43.79, 41.3, 43.79, 39.81, 10246944, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('000002', '2025-07-30', 57.0, 58.17, 54.59, 58.17, 52.88, 19656835, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('600326', '2025-07-29', 76.86, 78.83, 74.31, 78.83, 71.66, 39547098, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('600000', '2025-07-29', 94.85, 97.49, 90.45, 97.49, 88.63, 37612783, 1);
INSERT INTO iquant_daily_price (symbol, day, open, high, low, close, pre_close, volume, limit_up) 
VALUES ('600036', '2025-07-29', 104.31, 109.03, 101.95, 109.03, 99.12, 31253688, 1);
