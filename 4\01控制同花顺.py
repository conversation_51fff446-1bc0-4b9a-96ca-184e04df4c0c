import pyautogui
import time
import tkinter as tk
import subprocess

path=r'd:\同花顺软件\同花顺\hexin.exe'

root=tk.Tk()
##获取窗口信息
screenwidth = root.winfo_screenwidth()  #获取屏幕宽度（单位：像素）
screenheight = root.winfo_screenheight()  #获取屏幕高度（单位：像素）
winw= root.winfo_width()   #获取窗口宽度（单位：像素）
winh = root.winfo_height()  #获取窗口高度（单位：像素）
winx= root.winfo_x()
winy= root.winfo_y()
root.update()
root.destroy()
print(screenwidth,screenheight)

subprocess.Popen(path)

time.sleep(1)
pyautogui.press('enter')
time.sleep(5)

pyautogui.click(x=80, y=80) 
time.sleep(3)
pyautogui.press('enter')

time.sleep(3)
pyautogui.write('600519')
pyautogui.press('enter')
time.sleep(3)
#pyautogui.write('600519')
#pyautogui.press('enter')
time.sleep(3)
pyautogui.press('f12')

