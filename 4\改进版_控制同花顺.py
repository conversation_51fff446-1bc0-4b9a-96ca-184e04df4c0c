import pyautogui
import time
import tkinter as tk
import subprocess

# 配置参数
软件途径 = r'D:\同花顺软件\同花顺\hexin.exe'
股票代码 = '600519'  # 贵州茅台
等待时间 = 2  # 默认等待时间

def get_screen_info():
    """获取屏幕信息"""
    root = tk.Tk()
    screenwidth = root.winfo_screenwidth()
    screenheight = root.winfo_screenheight()
    root.destroy()
    print(f"屏幕分辨率: {screenwidth} x {screenheight}")
    return screenwidth, screenheight

def start_tonghuashun():
    """启动同花顺软件"""
    print("正在启动同花顺...")
    try:
        subprocess.Popen(软件途径)
        print("同花顺启动成功")
        return True
    except Exception as e:
        print(f"启动同花顺失败: {e}")
        return False

def wait_and_click(x, y, wait_time=等待时间):
    """等待并点击指定位置"""
    time.sleep(wait_time)
    pyautogui.click(x=x, y=y)
    print(f"点击坐标: ({x}, {y})")

def input_stock_code(code):
    """输入股票代码"""
    print(f"输入股票代码: {code}")
    pyautogui.write(code)
    time.sleep(0.5)
    pyautogui.press('enter')

def main():
    """主函数"""
    print("=== 同花顺自动控制脚本 ===")
    
    # 获取屏幕信息
    screenwidth, screenheight = get_screen_info()
    
    # 启动同花顺
    if not start_tonghuashun():
        return
    
    # 等待软件启动
    print("等待软件启动...")
    time.sleep(3)
    
    # 按回车键（可能是确认启动）
    print("按回车键...")
    pyautogui.press('enter')
    time.sleep(等待时间)
    
    # 点击指定位置（可能是菜单或按钮）
    print("点击界面元素...")
    wait_and_click(x=80, y=80, wait_time=3)
    
    # 再次按回车
    print("确认操作...")
    pyautogui.press('enter')
    time.sleep(等待时间)
    
    # 输入股票代码
    print("输入股票代码...")
    input_stock_code(股票代码)
    time.sleep(等待时间)
    
    # 可选：再次输入股票代码（如果需要确认）
    print("确认股票代码...")
    input_stock_code(股票代码)
    time.sleep(等待时间)
    
    # 按F12键（可能是打开交易界面）
    print("按F12键...")
    pyautogui.press('f12')
    
    print("脚本执行完成！")

if __name__ == "__main__":
    # 安全提示
    print("注意：此脚本将控制鼠标和键盘")
    print("请确保同花顺软件已关闭")
    print("按Ctrl+C可随时停止脚本")
    
    try:
        main()
    except KeyboardInterrupt:
        print("\n脚本已被用户中断")
    except Exception as e:
        print(f"脚本执行出错: {e}")
