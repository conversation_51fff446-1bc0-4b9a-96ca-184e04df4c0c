import time
import json
import csv
import os
import requests

API = 'http://127.0.0.1:6003'
QUEUE = os.path.join('5', 'work_queue', 'ActiveWork.csv')
LOG = os.path.join('5', 'Work_Data_Log.csv')

order = [{
    'strategy_no': '1001',
    'code': '000528',
    'name': '柳工',
    'ct_amount': 100,
    'operate': 'buy'
}]

print('POST /api/queue ...')
r = requests.post(f'{API}/api/queue', json=order, timeout=10)
print('status:', r.status_code)
print('resp:', r.text)

print('waiting worker to execute...')
for i in range(10):
    time.sleep(1)
    if os.path.exists(LOG) and os.path.getsize(LOG) > 0:
        break

if os.path.exists(QUEUE):
    print('\nActiveWork.csv (tail):')
    with open(QUEUE, 'r', encoding='utf-8-sig') as f:
        lines = f.readlines()[-5:]
        for line in lines:
            print(line.strip())

if os.path.exists(LOG):
    print('\nWork_Data_Log.csv (tail):')
    with open(LOG, 'r', encoding='utf-8-sig') as f:
        lines = f.readlines()[-5:]
        for line in lines:
            print(line.strip())

print('\nDONE')

