import time, os, json
import requests
from pathlib import Path

API = 'http://127.0.0.1:6003'
QUEUE = Path('5/work_queue/ActiveWork.csv')
LOG = Path('5/Work_Data_Log.csv')

def tail(path: Path, n=5):
    if not path.exists():
        print(f'{path} not found')
        return
    lines = path.read_text(encoding='utf-8-sig').splitlines()
    for ln in lines[-n:]:
        print(ln)

order = [{
    'strategy_no': '1001',
    'code': '000528',
    'name': 'LG',
    'ct_amount': 100,
    'operate': 'buy'
    # 不提供 price -> worker 自动取卖一价
}]

print('POST /api/queue ...')
r = requests.post(f'{API}/api/queue', json=order, timeout=20)
print('status:', r.status_code)
print('resp:', r.text)

print('\nwaiting worker to execute (10s)...')
for i in range(10):
    time.sleep(1)

print('\nActiveWork.csv (tail):')
tail(QUEUE, 10)
print('\nWork_Data_Log.csv (tail):')
tail(LOG, 10)
print('\nDONE')

