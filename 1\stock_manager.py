#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
通用股票板块管理工具

支持对任意板块进行股票的增删改查操作
"""

import sys
import argparse
from stock_crud_tool import StockCRUDTool


class StockManager:
    """股票板块管理器"""
    
    def __init__(self, tdx_dir="D:/zyb(6.3.5 7.66)M"):
        """初始化管理器"""
        self.tdx_dir = tdx_dir
        self.crud_tool = StockCRUDTool(tdxdir=tdx_dir)
        print(f"使用通达信目录: {tdx_dir}")
    
    def add_stock(self, block_name, stock_code):
        """向板块添加单只股票"""
        print(f"\n向板块 '{block_name}' 添加股票 {stock_code}")
        print("-" * 40)
        
        # 查询当前板块
        result = self.crud_tool.get_block_stocks(block_name)
        
        if result['success']:
            print(f"当前股票: {result['stocks']}")
            
            if stock_code in result['stocks']:
                print(f"⚠️  股票 {stock_code} 已存在")
                return False
            
            # 添加股票
            result = self.crud_tool.add_stocks_to_block(block_name, [stock_code])
            if result['success']:
                print(f"✓ 添加成功！现有 {result['final_count']} 只股票")
                return True
            else:
                print(f"✗ 添加失败: {result['message']}")
                return False
        else:
            print(f"板块不存在，创建新板块...")
            result = self.crud_tool.create_block_with_stocks(block_name, [stock_code])
            if result['success']:
                print(f"✓ 创建成功！添加了 {result['added_count']} 只股票")
                return True
            else:
                print(f"✗ 创建失败: {result['message']}")
                return False
    
    def remove_stock(self, block_name, stock_code):
        """从板块移除单只股票"""
        print(f"\n从板块 '{block_name}' 移除股票 {stock_code}")
        print("-" * 40)
        
        result = self.crud_tool.remove_stocks_from_block(block_name, [stock_code])
        
        if result['success']:
            print(f"✓ 移除成功！现有 {result['final_count']} 只股票")
            if result['removed_codes']:
                print(f"已移除: {result['removed_codes']}")
            return True
        else:
            print(f"✗ 移除失败: {result['message']}")
            if result['not_found_codes']:
                print(f"未找到: {result['not_found_codes']}")
            return False
    
    def list_stocks(self, block_name):
        """列出板块中的所有股票"""
        print(f"\n板块 '{block_name}' 股票列表")
        print("-" * 40)
        
        result = self.crud_tool.get_block_stocks(block_name)
        
        if result['success']:
            print(f"股票数量: {result['stock_count']}")
            print(f"股票列表: {result['stocks']}")
            
            # 格式化显示
            if result['stocks']:
                print("\n详细列表:")
                for i, stock in enumerate(result['stocks'], 1):
                    print(f"  {i:2d}. {stock}")
            return True
        else:
            print(f"✗ 查询失败: {result['message']}")
            return False
    
    def replace_stocks(self, block_name, stock_codes):
        """替换板块中的所有股票"""
        print(f"\n替换板块 '{block_name}' 中的所有股票")
        print(f"新股票: {stock_codes}")
        print("-" * 40)
        
        result = self.crud_tool.replace_block_stocks(block_name, stock_codes)
        
        if result['success']:
            print(f"✓ 替换成功！现有 {result['final_count']} 只股票")
            if result['invalid_codes']:
                print(f"无效代码: {result['invalid_codes']}")
            return True
        else:
            print(f"✗ 替换失败: {result['message']}")
            return False
    
    def delete_block(self, block_name):
        """删除整个板块"""
        print(f"\n删除板块 '{block_name}'")
        print("-" * 40)
        
        # 先确认
        confirm = input(f"确认删除板块 '{block_name}'？(y/n): ").lower().strip()
        if confirm != 'y':
            print("操作取消")
            return False
        
        result = self.crud_tool.delete_block(block_name)
        
        if result['success']:
            print(f"✓ 删除成功！")
            return True
        else:
            print(f"✗ 删除失败: {result['message']}")
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='股票板块管理工具')
    parser.add_argument('action', choices=['add', 'remove', 'list', 'replace', 'delete'], 
                       help='操作类型')
    parser.add_argument('block_name', help='板块名称')
    parser.add_argument('--stock', '-s', help='股票代码')
    parser.add_argument('--stocks', '-S', nargs='+', help='多个股票代码')
    parser.add_argument('--tdxdir', default="D:/zyb(6.3.5 7.66)M", help='通达信目录')
    
    args = parser.parse_args()
    
    # 初始化管理器
    manager = StockManager(tdx_dir=args.tdxdir)
    
    # 执行操作
    success = False
    
    if args.action == 'add':
        if not args.stock:
            print("错误: 添加股票需要指定 --stock 参数")
            sys.exit(1)
        success = manager.add_stock(args.block_name, args.stock)
    
    elif args.action == 'remove':
        if not args.stock:
            print("错误: 移除股票需要指定 --stock 参数")
            sys.exit(1)
        success = manager.remove_stock(args.block_name, args.stock)
    
    elif args.action == 'list':
        success = manager.list_stocks(args.block_name)
    
    elif args.action == 'replace':
        if not args.stocks:
            print("错误: 替换股票需要指定 --stocks 参数")
            sys.exit(1)
        success = manager.replace_stocks(args.block_name, args.stocks)
    
    elif args.action == 'delete':
        success = manager.delete_block(args.block_name)
    
    # 显示最终结果
    if success:
        print(f"\n✓ 操作完成")
        # 如果不是删除操作，显示最终状态
        if args.action != 'delete':
            manager.list_stocks(args.block_name)
    else:
        print(f"\n✗ 操作失败")
        sys.exit(1)


def interactive_mode():
    """交互模式"""
    print("=" * 50)
    print("股票板块管理工具 - 交互模式")
    print("=" * 50)
    
    manager = StockManager()
    
    while True:
        print("\n可用操作:")
        print("1. 添加股票到板块")
        print("2. 从板块移除股票")
        print("3. 查看板块股票")
        print("4. 替换板块所有股票")
        print("5. 删除板块")
        print("6. 退出")
        
        choice = input("\n请选择操作 (1-6): ").strip()
        
        if choice == '1':
            block_name = input("板块名称: ").strip()
            stock_code = input("股票代码: ").strip()
            if block_name and stock_code:
                manager.add_stock(block_name, stock_code)
        
        elif choice == '2':
            block_name = input("板块名称: ").strip()
            stock_code = input("股票代码: ").strip()
            if block_name and stock_code:
                manager.remove_stock(block_name, stock_code)
        
        elif choice == '3':
            block_name = input("板块名称: ").strip()
            if block_name:
                manager.list_stocks(block_name)
        
        elif choice == '4':
            block_name = input("板块名称: ").strip()
            stocks_input = input("股票代码 (空格分隔): ").strip()
            if block_name and stocks_input:
                stock_codes = stocks_input.split()
                manager.replace_stocks(block_name, stock_codes)
        
        elif choice == '5':
            block_name = input("板块名称: ").strip()
            if block_name:
                manager.delete_block(block_name)
        
        elif choice == '6':
            print("退出程序")
            break
        
        else:
            print("无效选择")


if __name__ == "__main__":
    if len(sys.argv) == 1:
        # 无参数时进入交互模式
        interactive_mode()
    else:
        # 有参数时使用命令行模式
        main()
