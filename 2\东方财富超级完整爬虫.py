#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
东方财富超级完整可转债爬虫

获取包含实时价格、转股数据、正股价格等所有字段的完整可转债数据
"""

import urllib.request
import urllib.parse
import json
import csv
import time
from datetime import datetime


class SuperCompleteBondSpider:
    """超级完整可转债爬虫"""
    
    def __init__(self):
        """初始化"""
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://data.eastmoney.com/',
            'Accept': 'application/json, text/plain, */*',
        }
    
    def get_convertible_bonds_with_quotes(self):
        """获取包含行情的可转债数据"""
        
        # 东方财富可转债行情API（包含转股数据）
        url = "https://datacenter-web.eastmoney.com/api/data/v1/get"
        
        params = {
            'sortColumns': 'SECURITY_CODE',
            'sortTypes': '1',
            'pageSize': '500',
            'pageNumber': '1',
            'reportName': 'RPT_BOND_CB_LIST',
            'columns': 'ALL',
            'quoteColumns': 'f2,f3,f4,f5,f6,f7,f8,f9,f10,f11,f12,f13,f14,f15,f16,f17,f18,f19,f20,f21,f22,f23,f24,f25,f26,f27,f28,f29,f30',
            'js': '',
            'source': 'WEB',
            'client': 'WEB',
            '_': str(int(time.time() * 1000))
        }
        
        try:
            # 构建URL
            query_string = urllib.parse.urlencode(params)
            full_url = f"{url}?{query_string}"
            
            print("正在获取可转债完整数据（包含行情和转股信息）...")
            
            # 创建请求
            req = urllib.request.Request(full_url, headers=self.headers)
            
            # 发送请求
            with urllib.request.urlopen(req, timeout=30) as response:
                data = json.loads(response.read().decode('utf-8'))
            
            if data.get('success') and 'result' in data and 'data' in data['result']:
                bonds_data = data['result']['data']
                print(f"✓ 获取到 {len(bonds_data)} 条完整可转债数据")
                return bonds_data
            else:
                print("✗ 未获取到可转债数据")
                return []
                
        except Exception as e:
            print(f"✗ 获取数据失败: {e}")
            return []
    
    def process_bond_data(self, raw_data):
        """处理可转债数据"""
        
        if not raw_data:
            return []
        
        print("正在处理可转债数据...")
        
        processed_data = []
        
        for item in raw_data:
            # 只处理有基本数据的可转债
            bond_code = item.get('SECURITY_CODE')
            if not bond_code:
                continue
            
            processed_item = {
                # 基本信息
                '债券代码': bond_code,
                '债券简称': item.get('SECURITY_NAME_ABBR', ''),
                
                # 实时行情数据
                '债现价': item.get('NEWEST_PRICE', ''),
                '涨跌幅': item.get('CHANGE_RATE', ''),
                '涨跌额': item.get('CHANGE_AMOUNT', ''),
                '成交量': item.get('VOLUME', ''),
                '成交额': item.get('AMOUNT', ''),
                '换手率': item.get('TURNOVERRATE', ''),
                '振幅': item.get('AMPLITUDE', ''),
                '最高价': item.get('HIGH_PRICE', ''),
                '最低价': item.get('LOW_PRICE', ''),
                '开盘价': item.get('OPEN_PRICE', ''),
                '昨收价': item.get('PRE_CLOSE_PRICE', ''),
                '量比': item.get('VOLUME_RATIO', ''),
                
                # 正股信息
                '正股代码': item.get('CONVERT_STOCK_CODE', ''),
                '正股简称': item.get('CONVERT_STOCK_NAME', ''),
                '正股价': item.get('CONVERT_STOCK_PRICE', ''),
                
                # 转股信息
                '转股价': item.get('CONVERT_PRICE', ''),
                '转股价值': item.get('CONVERT_VALUE', ''),
                '转股溢价率': item.get('CONVERT_PREMIUM_RATIO', ''),
                
                # 债券价值
                '纯债价值': item.get('BOND_VALUE', ''),
                '纯债溢价率': item.get('BOND_PREMIUM_RATIO', ''),
                '到期收益率': item.get('YTMRT', ''),
                
                # 发行信息
                '发行规模': item.get('ISSUE_AMOUNT', ''),
                '申购日期': item.get('APPLY_DATE', ''),
                '申购代码': item.get('APPLY_CODE', ''),
                '申购上限': item.get('APPLY_UPPER_LIMIT', ''),
                '上市时间': item.get('LISTING_DATE', ''),
                '到期日期': item.get('MATURITY_DATE', ''),
                '剩余年限': item.get('REMAIN_YEAR', ''),
                
                # 评级和其他
                '信用评级': item.get('RATING', ''),
                '股权登记日': item.get('EQUITY_RECORD_DATE', ''),
                '每股配售额': item.get('ALLOT_PRICE', ''),
                '原股东配售': item.get('ORIG_SHARE_RATIO', ''),
                
                # 回售和赎回
                '回售触发价': item.get('RESALE_TRIG_PRICE', ''),
                '强赎触发价': item.get('REDEEM_PRICE', ''),
                
                # 市场信息
                '市场': '上海' if bond_code.startswith(('110', '113', '118')) else '深圳' if bond_code.startswith(('123', '127', '128')) else '未知',
                '交易市场': item.get('TRADE_MARKET_CODE', ''),
                
                # 元数据
                '数据获取时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # 数据清洗：处理日期格式
            for date_field in ['申购日期', '上市时间', '到期日期', '股权登记日']:
                value = processed_item.get(date_field)
                if value and isinstance(value, str) and len(value) >= 8:
                    try:
                        processed_item[date_field] = f"{value[:4]}-{value[4:6]}-{value[6:8]}"
                    except:
                        pass
            
            processed_data.append(processed_item)
        
        print(f"✓ 处理完成，共 {len(processed_data)} 条可转债数据")
        return processed_data
    
    def save_to_csv(self, data, filename=None):
        """保存数据到CSV文件"""
        
        if not data:
            print("✗ 无数据可保存")
            return ""
        
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"东方财富超级完整可转债_{timestamp}.csv"
        
        try:
            # 获取所有字段名
            fieldnames = list(data[0].keys())
            
            # 写入CSV文件
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(data)
            
            print(f"✓ 数据已保存到: {filename}")
            return filename
            
        except Exception as e:
            print(f"✗ 保存失败: {e}")
            return ""
    
    def display_summary(self, data):
        """显示数据汇总"""
        
        if not data:
            print("无数据")
            return
        
        print(f"\n{'='*80}")
        print(f"超级完整可转债数据汇总")
        print(f"{'='*80}")
        print(f"可转债总数: {len(data)}")
        
        # 统计有各种数据的可转债数量
        with_price = len([item for item in data if item.get('债现价') and item.get('债现价') not in ['', '-']])
        with_convert = len([item for item in data if item.get('转股价值') and item.get('转股价值') not in ['', '-']])
        with_stock_price = len([item for item in data if item.get('正股价') and item.get('正股价') not in ['', '-']])
        with_premium = len([item for item in data if item.get('转股溢价率') and item.get('转股溢价率') not in ['', '-']])
        
        print(f"\n数据完整性:")
        print(f"  有债券价格: {with_price} 只 ({with_price/len(data)*100:.1f}%)")
        print(f"  有转股价值: {with_convert} 只 ({with_convert/len(data)*100:.1f}%)")
        print(f"  有正股价格: {with_stock_price} 只 ({with_stock_price/len(data)*100:.1f}%)")
        print(f"  有转股溢价率: {with_premium} 只 ({with_premium/len(data)*100:.1f}%)")
        
        # 市场分布
        market_count = {}
        for item in data:
            market = item.get('市场', '未知')
            market_count[market] = market_count.get(market, 0) + 1
        
        print(f"\n市场分布:")
        for market, count in market_count.items():
            percentage = count / len(data) * 100
            print(f"  {market}: {count} 只 ({percentage:.1f}%)")
        
        # 显示前10只有完整数据的可转债
        complete_data = []
        for item in data:
            if (item.get('债现价') and item.get('转股价值') and item.get('正股价') and 
                item.get('债现价') not in ['', '-'] and item.get('转股价值') not in ['', '-'] and 
                item.get('正股价') not in ['', '-']):
                complete_data.append(item)
        
        print(f"\n前10只数据最完整的可转债:")
        for i, item in enumerate(complete_data[:10], 1):
            code = item.get('债券代码', '')
            name = item.get('债券简称', '')
            price = item.get('债现价', '')
            change = item.get('涨跌幅', '')
            convert_value = item.get('转股价值', '')
            premium = item.get('转股溢价率', '')
            stock_name = item.get('正股简称', '')
            stock_price = item.get('正股价', '')
            
            print(f"  {i:2d}. {code} {name}")
            print(f"      债价:{price} 涨幅:{change}% 转股价值:{convert_value} 溢价率:{premium}%")
            print(f"      正股:{stock_name} 正股价:{stock_price}")
        
        # 显示涨幅前5的可转债
        price_data = [item for item in data if item.get('涨跌幅') and item.get('涨跌幅') not in ['', '-']]
        if price_data:
            try:
                price_data.sort(key=lambda x: float(x.get('涨跌幅', 0)), reverse=True)
                print(f"\n涨幅前5的可转债:")
                for i, item in enumerate(price_data[:5], 1):
                    code = item.get('债券代码', '')
                    name = item.get('债券简称', '')
                    price = item.get('债现价', '')
                    change = item.get('涨跌幅', '')
                    stock_name = item.get('正股简称', '')
                    
                    print(f"  {i}. {code} {name} 价格:{price} 涨幅:{change}% 正股:{stock_name}")
            except:
                pass
    
    def run(self):
        """运行爬虫"""
        print("🚀 东方财富超级完整可转债爬虫")
        print("=" * 60)
        
        try:
            # 获取完整数据
            raw_data = self.get_convertible_bonds_with_quotes()
            
            if not raw_data:
                print("❌ 未获取到数据")
                return ""
            
            # 处理数据
            processed_data = self.process_bond_data(raw_data)
            
            if not processed_data:
                print("❌ 数据处理失败")
                return ""
            
            # 显示汇总
            self.display_summary(processed_data)
            
            # 保存数据
            filename = self.save_to_csv(processed_data)
            
            if filename:
                print(f"\n✅ 爬取完成！")
                print(f"📁 数据文件: {filename}")
                print(f"📊 可转债数量: {len(processed_data)} 只")
                print(f"💡 包含完整的实时行情、转股数据、正股价格等所有字段")
                return filename
            else:
                print("❌ 数据保存失败")
                return ""
                
        except Exception as e:
            print(f"❌ 爬虫运行失败: {e}")
            import traceback
            traceback.print_exc()
            return ""


def main():
    """主函数"""
    spider = SuperCompleteBondSpider()
    result_file = spider.run()
    
    if result_file:
        print(f"\n🎉 超级完整爬虫执行成功！")
        print(f"📁 数据文件: {result_file}")
        print(f"🔥 现在拥有最完整的可转债数据，包含所有东方财富网站字段！")
    else:
        print(f"\n❌ 爬虫执行失败！")


if __name__ == "__main__":
    main()
