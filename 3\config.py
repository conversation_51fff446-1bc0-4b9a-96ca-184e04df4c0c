"""
同花顺交易接口配置文件
"""

# 数据源配置
DATA_SOURCE_CONFIG = {
    'url': 'http://101.201.73.219:8082/XSQT/GetBuyCodeInfoII',
    'token': 'ffffffff-e91e-5efd-ffff-ffffa460846b',
    'timeout': 10,
    'retry_times': 3,
    'retry_interval': 5
}

# 同花顺账户配置
ACCOUNT_CONFIG = {
    # 自动登录配置（可选）
    'user': '111',  # 用户名
    'password': '3333',  # 密码
    'exe_path': 'D:\\同花顺软件\\同花顺\\xiadan.exe',  # 同花顺客户端路径，如: C:\\同花顺\\xiadan.exe
    # 'exe_path': 'D:\\同花顺远航版\\transaction\\xiadan.exe',
    
    # 手动登录模式（推荐）
    'manual_login': True  # 设为False时使用自动登录
}

# 交易配置
TRADE_CONFIG = {
    'default_amount': 100,  # 默认买入数量（手）
    'max_position': 10,     # 最大持仓数量
    'price_type': 'market', # 价格类型: 'market'(市价) 或 'limit'(限价)
    'price_offset': 0.01,   # 限价时的价格偏移
    'enable_trading': True, # 是否启用实际交易
    'dry_run': False        # 是否为模拟运行
}

# 监控配置
MONITOR_CONFIG = {
    'interval': 5,          # 监控间隔（秒）
    'enable_monitoring': True,  # 是否启用监控
    'trading_hours_only': True, # 是否仅在交易时间监控
    'max_records_per_check': 50 # 每次检查的最大记录数
}

# 风控配置
RISK_CONFIG = {
    'max_daily_trades': 20,     # 每日最大交易次数
    'max_single_amount': 500,   # 单次最大买入数量（手）
    'min_interval_between_trades': 10,  # 交易间最小间隔（秒）
    'blacklist_codes': [],      # 黑名单股票代码
    'whitelist_sectors': [],    # 白名单板块
    'enable_risk_control': True # 是否启用风控
}

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',
    'file_path': '3/trading.log',
    'max_file_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5,
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
}

# 通知配置
NOTIFICATION_CONFIG = {
    'enable_email': False,
    'email_config': {
        'smtp_server': '',
        'smtp_port': 587,
        'username': '',
        'password': '',
        'to_emails': []
    },
    'enable_webhook': False,
    'webhook_url': '',
    'enable_console': True
}

# 完整配置字典
FULL_CONFIG = {
    'data_source_url': DATA_SOURCE_CONFIG['url'],
    'token': DATA_SOURCE_CONFIG['token'],
    'account_config': ACCOUNT_CONFIG,
    'trade_config': TRADE_CONFIG,
    'monitor_config': MONITOR_CONFIG,
    'risk_config': RISK_CONFIG,
    'log_config': LOG_CONFIG,
    'notification_config': NOTIFICATION_CONFIG
}


def get_config():
    """获取完整配置"""
    return FULL_CONFIG.copy()


def get_test_config():
    """获取测试配置"""
    test_config = get_config()
    test_config['trade_config']['dry_run'] = True
    test_config['trade_config']['enable_trading'] = False
    return test_config


def validate_config(config):
    """验证配置有效性"""
    errors = []
    
    # 检查必需字段
    required_fields = ['data_source_url', 'token']
    for field in required_fields:
        if not config.get(field):
            errors.append(f"缺少必需字段: {field}")
    
    # 检查交易配置
    trade_config = config.get('trade_config', {})
    if trade_config.get('default_amount', 0) <= 0:
        errors.append("默认买入数量必须大于0")
    
    if trade_config.get('max_position', 0) <= 0:
        errors.append("最大持仓数量必须大于0")
    
    # 检查风控配置
    risk_config = config.get('risk_config', {})
    if risk_config.get('max_daily_trades', 0) <= 0:
        errors.append("每日最大交易次数必须大于0")
    
    return errors


if __name__ == '__main__':
    # 配置验证示例
    config = get_config()
    errors = validate_config(config)
    
    if errors:
        print("配置验证失败:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("配置验证通过")
        print(f"数据源URL: {config['data_source_url']}")
        print(f"默认买入数量: {config['trade_config']['default_amount']}手")
        print(f"监控间隔: {config['monitor_config']['interval']}秒")
