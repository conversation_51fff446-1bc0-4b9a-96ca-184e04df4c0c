"""
同花顺交易接口测试类
用于测试交易功能和数据监控
"""
import unittest
import time
import json
from unittest.mock import Mock, patch, MagicMock
from ths_trader import ThsTrader


class TestThsTrader(unittest.TestCase):
    """同花顺交易接口测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.config = {
            'data_source_url': 'http://101.201.73.219:8082/XSQT/GetBuyCodeInfoII',
            'token': 'ffffffff-e91e-5efd-ffff-ffffa460846b',
            'account_config': {
                'user': 'test_user',
                'password': 'test_password',
                'exe_path': 'C:\\同花顺\\xiadan.exe'
            },
            'trade_config': {
                'default_amount': 100,
                'max_position': 10
            }
        }
        self.trader = ThsTrader(self.config)
    
    def tearDown(self):
        """测试后清理"""
        if self.trader.monitoring:
            self.trader.stop_monitoring()
    
    def test_init(self):
        """测试初始化"""
        self.assertEqual(self.trader.data_source_url, self.config['data_source_url'])
        self.assertEqual(self.trader.token, self.config['token'])
        self.assertFalse(self.trader.is_logged_in)
        self.assertFalse(self.trader.monitoring)
    
    @patch('easytrader.use')
    def test_login_success(self, mock_easytrader):
        """测试登录成功"""
        mock_trader = Mock()
        mock_easytrader.return_value = mock_trader
        
        result = self.trader.login()
        
        self.assertTrue(result)
        self.assertTrue(self.trader.is_logged_in)
        mock_easytrader.assert_called_once_with('ths')
        mock_trader.prepare.assert_called_once()
    
    @patch('easytrader.use')
    def test_login_failure(self, mock_easytrader):
        """测试登录失败"""
        mock_easytrader.side_effect = Exception("登录失败")
        
        result = self.trader.login()
        
        self.assertFalse(result)
        self.assertFalse(self.trader.is_logged_in)
    
    @patch('requests.get')
    def test_get_data_source_success(self, mock_get):
        """测试获取数据源成功"""
        mock_response = Mock()
        mock_response.json.return_value = {
            'Code': '200',
            'Msg': 'success',
            'Data': [
                {
                    'id': 443,
                    'code': '603101',
                    'name': '汇嘉时代',
                    'bkname': '新疆',
                    'addtime': '2025-08-11 09:36:04.000'
                }
            ]
        }
        mock_get.return_value = mock_response
        
        result = self.trader.get_data_source()
        
        self.assertIsNotNone(result)
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]['code'], '603101')
    
    @patch('requests.get')
    def test_get_data_source_failure(self, mock_get):
        """测试获取数据源失败"""
        mock_get.side_effect = Exception("网络错误")
        
        result = self.trader.get_data_source()
        
        self.assertIsNone(result)
    
    def test_check_new_records(self):
        """测试检查新记录"""
        # 模拟初始数据
        self.trader.last_data_ids = {443, 444}
        
        with patch.object(self.trader, 'get_data_source') as mock_get_data:
            mock_get_data.return_value = [
                {'id': 443, 'code': '603101', 'name': '汇嘉时代'},
                {'id': 444, 'code': '002471', 'name': '中超控股'},
                {'id': 445, 'code': '002851', 'name': '麦格米特'}  # 新记录
            ]
            
            new_records = self.trader.check_new_records()
            
            self.assertEqual(len(new_records), 1)
            self.assertEqual(new_records[0]['id'], 445)
            self.assertEqual(new_records[0]['name'], '麦格米特')
    
    def test_is_valid_stock_code(self):
        """测试股票代码验证"""
        # 有效代码
        self.assertTrue(self.trader.is_valid_stock_code('603101'))
        self.assertTrue(self.trader.is_valid_stock_code('000001'))
        
        # 无效代码
        self.assertFalse(self.trader.is_valid_stock_code('60310'))  # 长度不对
        self.assertFalse(self.trader.is_valid_stock_code('60310a'))  # 包含字母
        self.assertFalse(self.trader.is_valid_stock_code(''))  # 空字符串
        self.assertFalse(self.trader.is_valid_stock_code(None))  # None
    
    def test_is_trading_time(self):
        """测试交易时间判断"""
        with patch('ths_trader.datetime') as mock_datetime:
            # 模拟工作日上午交易时间
            mock_now = Mock()
            mock_now.time.return_value = time.strptime("10:30", "%H:%M").tm_hour * 3600 + time.strptime("10:30", "%H:%M").tm_min * 60
            mock_now.weekday.return_value = 1  # 周二
            mock_datetime.now.return_value = mock_now
            
            # 这里需要更详细的时间测试实现
            # 由于时间处理比较复杂，这里简化测试
            pass
    
    @patch('easytrader.use')
    def test_buy_stock_success(self, mock_easytrader):
        """测试买入股票成功"""
        mock_trader = Mock()
        mock_trader.market_buy.return_value = {'order_id': '12345'}
        mock_easytrader.return_value = mock_trader
        
        self.trader.login()
        result = self.trader.buy_stock('603101', '汇嘉时代')
        
        self.assertTrue(result)
        mock_trader.market_buy.assert_called_once_with('603101', amount=100)
    
    @patch('easytrader.use')
    def test_buy_stock_not_logged_in(self, mock_easytrader):
        """测试未登录时买入股票"""
        result = self.trader.buy_stock('603101', '汇嘉时代')
        
        self.assertFalse(result)
    
    def test_process_new_record(self):
        """测试处理新记录"""
        record = {
            'id': 445,
            'code': '603101',
            'name': '汇嘉时代',
            'bkname': '新疆',
            'addtime': '2025-08-11 09:36:04.000'
        }
        
        with patch.object(self.trader, 'is_trading_time', return_value=True), \
             patch.object(self.trader, 'buy_stock', return_value=True) as mock_buy:
            
            self.trader.process_new_record(record)
            
            mock_buy.assert_called_once_with('603101', '汇嘉时代')
    
    def test_start_stop_monitoring(self):
        """测试开始和停止监控"""
        with patch.object(self.trader, '_monitor_loop'):
            self.trader.start_monitoring(interval=1)
            self.assertTrue(self.trader.monitoring)
            
            self.trader.stop_monitoring()
            self.assertFalse(self.trader.monitoring)


class TestThsTraderIntegration(unittest.TestCase):
    """集成测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.config = {
            'data_source_url': 'http://101.201.73.219:8082/XSQT/GetBuyCodeInfoII',
            'token': 'ffffffff-e91e-5efd-ffff-ffffa460846b',
            'account_config': {},  # 手动登录模式
            'trade_config': {
                'default_amount': 100
            }
        }
        self.trader = ThsTrader(self.config)
    
    def test_real_data_source(self):
        """测试真实数据源连接"""
        data = self.trader.get_data_source()
        
        if data is not None:
            print(f"获取到 {len(data)} 条记录")
            for record in data[:3]:  # 只打印前3条
                print(f"ID: {record.get('id')}, "
                      f"代码: {record.get('code')}, "
                      f"名称: {record.get('name')}, "
                      f"板块: {record.get('bkname')}")
        else:
            print("无法获取数据源数据")
    
    def test_monitoring_simulation(self):
        """测试监控模拟"""
        print("开始监控模拟测试...")
        
        # 模拟监控5秒
        with patch.object(self.trader, 'process_new_record') as mock_process:
            self.trader.start_monitoring(interval=2)
            time.sleep(5)
            self.trader.stop_monitoring()
            
            print(f"模拟期间处理了 {mock_process.call_count} 条新记录")


class TestRunner:
    """测试运行器"""
    
    def __init__(self):
        self.suite = unittest.TestSuite()
    
    def add_unit_tests(self):
        """添加单元测试"""
        self.suite.addTest(unittest.makeSuite(TestThsTrader))
    
    def add_integration_tests(self):
        """添加集成测试"""
        self.suite.addTest(unittest.makeSuite(TestThsTraderIntegration))
    
    def run_tests(self, verbosity=2):
        """运行测试"""
        runner = unittest.TextTestRunner(verbosity=verbosity)
        result = runner.run(self.suite)
        return result
    
    def run_specific_test(self, test_class, test_method=None):
        """运行特定测试"""
        if test_method:
            suite = unittest.TestSuite()
            suite.addTest(test_class(test_method))
        else:
            suite = unittest.makeSuite(test_class)
        
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        return result


def main():
    """主函数"""
    print("=== 同花顺交易接口测试 ===")
    
    test_runner = TestRunner()
    
    # 选择测试类型
    print("\n请选择测试类型:")
    print("1. 单元测试")
    print("2. 集成测试")
    print("3. 全部测试")
    print("4. 数据源连接测试")
    print("5. 监控模拟测试")
    
    choice = input("请输入选择 (1-5): ").strip()
    
    if choice == '1':
        print("\n运行单元测试...")
        test_runner.add_unit_tests()
        test_runner.run_tests()
    
    elif choice == '2':
        print("\n运行集成测试...")
        test_runner.add_integration_tests()
        test_runner.run_tests()
    
    elif choice == '3':
        print("\n运行全部测试...")
        test_runner.add_unit_tests()
        test_runner.add_integration_tests()
        test_runner.run_tests()
    
    elif choice == '4':
        print("\n测试数据源连接...")
        test_runner.run_specific_test(TestThsTraderIntegration, 'test_real_data_source')
    
    elif choice == '5':
        print("\n运行监控模拟测试...")
        test_runner.run_specific_test(TestThsTraderIntegration, 'test_monitoring_simulation')
    
    else:
        print("无效选择")


if __name__ == '__main__':
    main()
