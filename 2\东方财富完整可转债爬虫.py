#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
东方财富完整可转债数据爬虫

直接从东方财富可转债页面爬取完整的实时数据，包含所有字段
"""

import urllib.request
import urllib.parse
import json
import csv
import time
from datetime import datetime


class CompleteBondSpider:
    """完整可转债数据爬虫"""
    
    def __init__(self):
        """初始化"""
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://data.eastmoney.com/',
            'Accept': 'application/json, text/plain, */*',
        }
    
    def get_bond_list_data(self):
        """获取可转债列表数据（包含实时行情）"""
        
        # 东方财富可转债行情API
        url = "https://push2.eastmoney.com/api/qt/clist/get"
        
        params = {
            'cb': 'jQuery112409748013994908473_1691234567890',
            'pn': '1',
            'pz': '1000',  # 获取1000条数据
            'po': '1',
            'np': '1',
            'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
            'fltt': '2',
            'invt': '2',
            'fid': 'f3',
            'fs': 'b:MK0354',  # 可转债板块
            'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152,f26,f27,f28,f19,f29,f30',
            '_': str(int(time.time() * 1000))
        }
        
        try:
            # 构建URL
            query_string = urllib.parse.urlencode(params)
            full_url = f"{url}?{query_string}"
            
            print("正在获取可转债实时行情数据...")
            
            # 创建请求
            req = urllib.request.Request(full_url, headers=self.headers)
            
            # 发送请求
            with urllib.request.urlopen(req, timeout=30) as response:
                text = response.read().decode('utf-8')
            
            # 提取JSON数据
            start = text.find('(') + 1
            end = text.rfind(')')
            json_str = text[start:end]
            
            data = json.loads(json_str)
            
            if data.get('rc') == 0 and 'data' in data and 'diff' in data['data']:
                bonds_data = data['data']['diff']
                print(f"✓ 获取到 {len(bonds_data)} 只可转债实时数据")
                return bonds_data
            else:
                print("✗ 未获取到可转债数据")
                return []
                
        except Exception as e:
            print(f"✗ 获取数据失败: {e}")
            return []
    
    def get_bond_detail_data(self):
        """获取可转债详细数据（包含转股信息）"""
        
        # 东方财富可转债详细信息API
        url = "https://datacenter-web.eastmoney.com/api/data/v1/get"
        
        all_bonds = []
        page_number = 1
        page_size = 500
        
        while True:
            params = {
                'sortColumns': 'SECURITY_CODE',
                'sortTypes': '1',
                'pageSize': str(page_size),
                'pageNumber': str(page_number),
                'reportName': 'RPT_BOND_CB_LIST',
                'columns': 'ALL',
                'quoteColumns': 'f2,f3,f4,f5,f6,f7,f8,f9,f10,f11,f12,f13,f14,f15,f16,f17,f18,f19,f20,f21,f22,f23,f24,f25,f26,f27,f28,f29,f30',
                'js': '',
                'source': 'WEB',
                'client': 'WEB',
                '_': str(int(time.time() * 1000))
            }
            
            try:
                # 构建URL
                query_string = urllib.parse.urlencode(params)
                full_url = f"{url}?{query_string}"
                
                print(f"正在获取第 {page_number} 页可转债详细数据...")
                
                # 创建请求
                req = urllib.request.Request(full_url, headers=self.headers)
                
                # 发送请求
                with urllib.request.urlopen(req, timeout=30) as response:
                    data = json.loads(response.read().decode('utf-8'))
                
                if data.get('success') and 'result' in data and 'data' in data['result']:
                    page_data = data['result']['data']
                    total_count = data['result'].get('count', 0)
                    
                    print(f"✓ 第 {page_number} 页获取成功: {len(page_data)} 条数据")
                    
                    if not page_data:
                        print("当前页无数据，停止获取")
                        break
                    
                    all_bonds.extend(page_data)
                    
                    # 检查是否还有更多数据
                    if len(all_bonds) >= total_count:
                        print(f"已获取全部数据，总计 {len(all_bonds)} 条")
                        break
                    
                    page_number += 1
                    time.sleep(1)  # 避免请求过快
                    
                else:
                    print(f"第 {page_number} 页获取失败")
                    break
                    
            except Exception as e:
                print(f"✗ 获取第 {page_number} 页数据失败: {e}")
                break
        
        print(f"✓ 总共获取到 {len(all_bonds)} 条可转债详细数据")
        return all_bonds
    
    def merge_bond_data(self, list_data, detail_data):
        """合并行情数据和详细数据"""
        
        print("正在合并数据...")
        
        # 创建详细数据的映射
        detail_map = {}
        for item in detail_data:
            code = item.get('SECURITY_CODE')
            if code:
                detail_map[code] = item
        
        merged_data = []
        
        for item in list_data:
            bond_code = item.get('f12')  # 可转债代码
            if not bond_code:
                continue
            
            # 获取对应的详细数据
            detail = detail_map.get(bond_code, {})
            
            # 合并数据
            merged_item = {
                # 基本信息
                '债券代码': bond_code,
                '债券简称': item.get('f14', ''),
                
                # 实时行情
                '债现价': item.get('f2', ''),  # 最新价
                '涨跌幅': item.get('f3', ''),  # 涨跌幅%
                '涨跌额': item.get('f4', ''),  # 涨跌额
                '成交量': item.get('f5', ''),  # 成交量(手)
                '成交额': item.get('f6', ''),  # 成交额(元)
                '振幅': item.get('f7', ''),    # 振幅%
                '换手率': item.get('f8', ''),  # 换手率%
                '市盈率': item.get('f9', ''),  # 市盈率
                '量比': item.get('f10', ''),   # 量比
                '最高价': item.get('f15', ''), # 最高价
                '最低价': item.get('f16', ''), # 最低价
                '开盘价': item.get('f17', ''), # 开盘价
                '昨收价': item.get('f18', ''), # 昨收价
                
                # 正股信息
                '正股代码': detail.get('CONVERT_STOCK_CODE', ''),
                '正股简称': detail.get('CONVERT_STOCK_NAME', ''),
                '正股价': detail.get('CONVERT_STOCK_PRICE', ''),
                
                # 转股信息
                '转股价': detail.get('CONVERT_PRICE', ''),
                '转股价值': detail.get('CONVERT_VALUE', ''),
                '转股溢价率': detail.get('CONVERT_PREMIUM_RATIO', ''),
                
                # 债券信息
                '纯债价值': detail.get('BOND_VALUE', ''),
                '纯债溢价率': detail.get('BOND_PREMIUM_RATIO', ''),
                '到期收益率': detail.get('YTMRT', ''),
                
                # 发行信息
                '发行规模': detail.get('ISSUE_AMOUNT', ''),
                '上市时间': detail.get('LISTING_DATE', ''),
                '到期日期': detail.get('MATURITY_DATE', ''),
                '剩余年限': detail.get('REMAIN_YEAR', ''),
                '信用评级': detail.get('RATING', ''),
                
                # 其他信息
                '申购代码': detail.get('APPLY_CODE', ''),
                '申购日期': detail.get('APPLY_DATE', ''),
                '股权登记日': detail.get('EQUITY_RECORD_DATE', ''),
                '每股配售额': detail.get('ALLOT_PRICE', ''),
                
                # 元数据
                '数据获取时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                '市场': '上海' if bond_code.startswith(('110', '113', '118')) else '深圳' if bond_code.startswith(('123', '127', '128')) else '未知'
            }
            
            merged_data.append(merged_item)
        
        print(f"✓ 数据合并完成，共 {len(merged_data)} 条记录")
        return merged_data
    
    def save_to_csv(self, data, filename=None):
        """保存数据到CSV文件"""
        
        if not data:
            print("✗ 无数据可保存")
            return ""
        
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"东方财富完整可转债数据_{timestamp}.csv"
        
        try:
            # 获取所有字段名
            fieldnames = list(data[0].keys())
            
            # 写入CSV文件
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(data)
            
            print(f"✓ 数据已保存到: {filename}")
            return filename
            
        except Exception as e:
            print(f"✗ 保存失败: {e}")
            return ""
    
    def display_summary(self, data):
        """显示数据汇总"""
        
        if not data:
            print("无数据")
            return
        
        print(f"\n{'='*80}")
        print(f"完整可转债数据汇总")
        print(f"{'='*80}")
        print(f"可转债数量: {len(data)}")
        
        # 统计有价格数据的可转债
        with_price = [item for item in data if item.get('债现价') and item.get('债现价') not in ['', '-']]
        print(f"有价格数据: {len(with_price)} 只")
        
        # 统计有转股数据的可转债
        with_convert = [item for item in data if item.get('转股价值') and item.get('转股价值') not in ['', '-']]
        print(f"有转股数据: {len(with_convert)} 只")
        
        # 市场分布
        market_count = {}
        for item in data:
            market = item.get('市场', '未知')
            market_count[market] = market_count.get(market, 0) + 1
        
        print(f"\n市场分布:")
        for market, count in market_count.items():
            print(f"  {market}: {count} 只")
        
        # 显示前10条有完整数据的记录
        complete_data = [item for item in data if item.get('债现价') and item.get('转股价值') 
                        and item.get('债现价') not in ['', '-'] and item.get('转股价值') not in ['', '-']]
        
        if complete_data:
            print(f"\n前10只有完整数据的可转债:")
            for i, item in enumerate(complete_data[:10], 1):
                code = item.get('债券代码', '')
                name = item.get('债券简称', '')
                price = item.get('债现价', '')
                convert_value = item.get('转股价值', '')
                premium = item.get('转股溢价率', '')
                stock_name = item.get('正股简称', '')
                
                print(f"  {i:2d}. {code} {name}")
                print(f"      价格:{price} 转股价值:{convert_value} 溢价率:{premium}% 正股:{stock_name}")
    
    def run(self):
        """运行爬虫"""
        print("🚀 东方财富完整可转债数据爬虫")
        print("=" * 60)
        
        try:
            # 1. 获取实时行情数据
            list_data = self.get_bond_list_data()
            
            if not list_data:
                print("❌ 未获取到行情数据")
                return ""
            
            # 2. 获取详细数据
            detail_data = self.get_bond_detail_data()
            
            # 3. 合并数据
            merged_data = self.merge_bond_data(list_data, detail_data)
            
            if not merged_data:
                print("❌ 数据合并失败")
                return ""
            
            # 4. 显示汇总
            self.display_summary(merged_data)
            
            # 5. 保存数据
            filename = self.save_to_csv(merged_data)
            
            if filename:
                print(f"\n✅ 爬取完成！")
                print(f"📁 数据文件: {filename}")
                print(f"📊 可转债数量: {len(merged_data)} 只")
                return filename
            else:
                print("❌ 数据保存失败")
                return ""
                
        except Exception as e:
            print(f"❌ 爬虫运行失败: {e}")
            import traceback
            traceback.print_exc()
            return ""


def main():
    """主函数"""
    spider = CompleteBondSpider()
    result_file = spider.run()
    
    if result_file:
        print(f"\n🎉 爬虫执行成功！")
        print(f"📁 数据文件: {result_file}")
        print(f"💡 现在包含完整的实时行情和转股数据")
    else:
        print(f"\n❌ 爬虫执行失败！")


if __name__ == "__main__":
    main()
