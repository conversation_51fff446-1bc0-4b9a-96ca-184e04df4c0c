# 同花顺交易接口

基于 easytrader 实现的同花顺自动交易系统，支持监控数据源并在有新记录时自动触发买入操作。

## 功能特性

- 🔄 **实时监控**: 监控指定数据源，检测新记录
- 📈 **自动交易**: 新记录触发时自动执行买入操作
- 🛡️ **风险控制**: 内置多重风控机制
- 🧪 **测试模式**: 支持模拟交易，安全测试
- 📊 **账户管理**: 查看账户信息和交易记录
- 🔧 **灵活配置**: 丰富的配置选项

## 安装依赖

```bash
pip install -r requirements.txt
```

## 同花顺客户端设置

使用前需要对同花顺客户端进行以下设置：

1. **系统设置 > 界面设置**: 界面不操作超时时间设为 `0`
2. **系统设置 > 交易设置**: 默认买入价格/买入数量/卖出价格/卖出数量 都设置为 `空`
3. **客户端状态**: 不能最小化，不能处于精简模式

## 快速开始

### 1. 基本使用

```python
from ths_trader import ThsTrader
from config import get_test_config

# 获取测试配置
config = get_test_config()

# 创建交易实例
trader = ThsTrader(config)

# 测试数据源连接
data = trader.get_data_source()
print(f"获取到 {len(data)} 条记录")

# 登录同花顺（测试模式跳过）
if not config['trade_config']['dry_run']:
    trader.login()

# 开始监控
trader.start_monitoring(interval=5)
```

### 2. 运行示例

```bash
# 运行使用示例
python example_usage.py

# 运行测试
python test_ths_trader.py
```

## 配置说明

### 数据源配置

```python
DATA_SOURCE_CONFIG = {
    'url': 'http://101.201.73.219:8082/XSQT/GetBuyCodeInfoII',
    'token': 'ffffffff-e91e-5efd-ffff-ffffa460846b',
    'timeout': 10
}
```

### 交易配置

```python
TRADE_CONFIG = {
    'default_amount': 100,  # 默认买入数量（手）
    'max_position': 10,     # 最大持仓数量
    'price_type': 'market', # 价格类型
    'dry_run': False        # 是否为模拟运行
}
```

### 风控配置

```python
RISK_CONFIG = {
    'max_daily_trades': 20,     # 每日最大交易次数
    'max_single_amount': 500,   # 单次最大买入数量
    'blacklist_codes': [],      # 黑名单股票代码
    'enable_risk_control': True # 是否启用风控
}
```

## API 文档

### ThsTrader 类

#### 初始化

```python
trader = ThsTrader(config)
```

#### 主要方法

- `login()`: 登录同花顺客户端
- `get_data_source()`: 获取数据源数据
- `check_new_records()`: 检查新记录
- `buy_stock(code, name, amount, price)`: 买入股票
- `start_monitoring(interval)`: 开始监控
- `stop_monitoring()`: 停止监控
- `get_account_info()`: 获取账户信息
- `get_today_trades()`: 获取今日成交记录

## 数据源格式

数据源返回的JSON格式：

```json
{
    "Code": "200",
    "Msg": "success",
    "Data": [
        {
            "id": 443,
            "pcday": "********",
            "addtime": "2025-08-11 09:36:04.000",
            "bkname": "新疆",
            "bkcode": "801211",
            "code": "603101",
            "name": "汇嘉时代",
            "codestatus": "初始",
            "addtimecode": "09:36:03"
        }
    ]
}
```

## 测试

### 运行单元测试

```bash
python -m pytest test_ths_trader.py -v
```

### 运行特定测试

```bash
# 测试数据源连接
python test_ths_trader.py
# 选择: 4

# 测试监控功能
python test_ths_trader.py
# 选择: 5
```

## 安全注意事项

1. **测试模式**: 首次使用建议开启 `dry_run` 模式
2. **风险控制**: 合理设置风控参数，避免过度交易
3. **资金管理**: 设置合适的单次买入金额和最大持仓
4. **监控日志**: 定期查看交易日志，及时发现异常

## 故障排除

### 常见问题

1. **登录失败**
   - 检查同花顺客户端是否正常运行
   - 确认客户端设置是否正确
   - 尝试手动登录后再运行程序

2. **数据源连接失败**
   - 检查网络连接
   - 确认数据源URL和Token是否正确
   - 查看防火墙设置

3. **买入失败**
   - 检查账户资金是否充足
   - 确认股票代码是否有效
   - 查看是否在交易时间内

### 日志查看

交易日志保存在 `3/trading.log` 文件中，包含详细的运行信息和错误记录。

## 开发指南

### 自定义策略

```python
def custom_process_record(record):
    """自定义记录处理逻辑"""
    stock_code = record.get('code')
    stock_name = record.get('name')
    bk_name = record.get('bkname')
    
    # 自定义策略逻辑
    if '机器人' in bk_name:
        trader.buy_stock(stock_code, stock_name)

# 替换默认处理方法
trader.process_new_record = custom_process_record
```

### 扩展功能

可以继承 `ThsTrader` 类来扩展功能：

```python
class CustomTrader(ThsTrader):
    def custom_buy_logic(self, record):
        # 自定义买入逻辑
        pass
```

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和券商规定。

## 免责声明

- 本软件仅供学习交流使用
- 使用本软件进行实际交易的风险由用户自行承担
- 作者不对任何交易损失承担责任
- 请在充分了解风险的情况下使用
