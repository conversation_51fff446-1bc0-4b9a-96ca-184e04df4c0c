#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试20天涨停查询

验证修改后的20天涨停查询功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from 涨停正股转债查询 import LimitUpStockBondQuery


def test_20_days_limit_up():
    """测试20天涨停查询"""
    print("🚀 测试20天涨停查询")
    print("=" * 60)
    
    # 数据库配置
    db_config = {
        'host': '***********',
        'port': 3306,
        'user': 'iQuant',
        'password': 'NAAnwaRsb8YGN3F5',
        'database': 'iquant',
        'charset': 'utf8mb4'
    }
    
    try:
        # 初始化查询器
        query_tool = LimitUpStockBondQuery(db_config)
        
        if query_tool.connect_database():
            print("✅ 数据库连接成功")
            print(f"✅ 可转债映射关系已加载: {len(query_tool.bond_stock_mapping)} 只正股")
            
            # 测试1: 使用默认参数（现在是20天）
            print(f"\n🔍 测试1: 使用默认参数查询（应该是20天）...")
            result_default = query_tool.get_limit_up_stocks_with_bonds()
            
            if result_default and 'summary' in result_default:
                summary = result_default['summary']
                print(f"✅ 默认查询成功！")
                print(f"📊 查询结果:")
                print(f"  查询天数: {summary.get('query_days', 'N/A')}")
                print(f"  查询时间范围: {summary.get('query_date_range', 'N/A')}")
                print(f"  涨停股票总数: {summary.get('total_limit_up', 0)} 只")
                print(f"  有对应可转债: {summary.get('with_bonds', 0)} 只")
                print(f"  无对应可转债: {summary.get('without_bonds', 0)} 只")
                print(f"  对应可转债总数: {summary.get('total_bonds', 0)} 只")
            else:
                print("❌ 默认查询失败")
            
            # 测试2: 明确指定20天
            print(f"\n🔍 测试2: 明确指定20天查询...")
            result_20 = query_tool.get_limit_up_stocks_with_bonds(days=20)
            
            if result_20 and 'summary' in result_20:
                summary_20 = result_20['summary']
                print(f"✅ 20天查询成功！")
                print(f"📊 查询结果:")
                print(f"  查询天数: {summary_20.get('query_days', 'N/A')}")
                print(f"  查询时间范围: {summary_20.get('query_date_range', 'N/A')}")
                print(f"  涨停股票总数: {summary_20.get('total_limit_up', 0)} 只")
                print(f"  有对应可转债: {summary_20.get('with_bonds', 0)} 只")
                print(f"  无对应可转债: {summary_20.get('without_bonds', 0)} 只")
                print(f"  对应可转债总数: {summary_20.get('total_bonds', 0)} 只")
            else:
                print("❌ 20天查询失败")
            
            # 测试3: 对比10天和20天的差异
            print(f"\n🔍 测试3: 对比10天和20天的差异...")
            result_10 = query_tool.get_limit_up_stocks_with_bonds(days=10)
            
            if result_10 and result_20 and 'summary' in result_10 and 'summary' in result_20:
                summary_10 = result_10['summary']
                summary_20 = result_20['summary']
                
                print(f"📊 对比结果:")
                print(f"  10天涨停股票: {summary_10.get('total_limit_up', 0)} 只")
                print(f"  20天涨停股票: {summary_20.get('total_limit_up', 0)} 只")
                print(f"  增加股票数: {summary_20.get('total_limit_up', 0) - summary_10.get('total_limit_up', 0)} 只")
                
                print(f"  10天有可转债: {summary_10.get('with_bonds', 0)} 只")
                print(f"  20天有可转债: {summary_20.get('with_bonds', 0)} 只")
                print(f"  增加可转债机会: {summary_20.get('with_bonds', 0) - summary_10.get('with_bonds', 0)} 只")
            
            # 显示20天的详细报告
            if result_20 and result_20.get('stocks_with_bonds'):
                print(f"\n📋 20天涨停转债详细报告:")
                query_tool.display_limit_up_bonds_report(result_20)
                
                # 导出20天数据
                print(f"\n📁 导出20天数据...")
                output_file = query_tool.export_limit_up_bonds(result_20, "20天涨停转债查询结果.csv")
                if output_file:
                    print(f"✅ 20天数据已导出到: {output_file}")
            
            query_tool.close()
            return True
            
        else:
            print("❌ 数据库连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def show_modification_summary():
    """显示修改总结"""
    print(f"\n{'='*60}")
    print("📋 修改总结")
    print(f"{'='*60}")
    
    print("🔧 已修改的函数:")
    print("  1. get_limit_up_stocks_recent_days(days=20)  # 原来是 days=10")
    print("  2. get_limit_up_stocks_with_bonds(days=20)   # 原来是 days=30")
    
    print("\n📝 修改说明:")
    print("  • 默认查询天数从10天改为20天")
    print("  • 现在不指定参数时，自动查询最近20天的涨停数据")
    print("  • 仍然支持手动指定任意天数")
    
    print("\n🚀 使用方法:")
    print("  # 使用默认20天")
    print("  result = query_tool.get_limit_up_stocks_with_bonds()")
    print("  ")
    print("  # 手动指定天数")
    print("  result = query_tool.get_limit_up_stocks_with_bonds(days=30)")
    
    print("\n💡 优势:")
    print("  • 20天范围更大，能捕获更多涨停机会")
    print("  • 增加可转债投资机会的发现")
    print("  • 保持系统灵活性，支持自定义天数")


def main():
    """主函数"""
    # 显示修改总结
    show_modification_summary()
    
    # 运行测试
    success = test_20_days_limit_up()
    
    print(f"\n{'='*60}")
    print("📊 测试结果")
    print(f"{'='*60}")
    
    if success:
        print("✅ 20天涨停查询修改成功！")
        print("💡 现在系统默认查询最近20天的涨停数据")
        print("🔥 可以发现更多的涨停转债投资机会")
    else:
        print("❌ 测试失败，请检查修改")


if __name__ == "__main__":
    main()
