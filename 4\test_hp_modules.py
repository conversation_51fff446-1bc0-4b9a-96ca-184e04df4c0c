"""
测试HP模块完整性
验证所有HP模块是否正常工作
"""
import time
import matplotlib.pyplot as plt
import HP_global as g
import HP_tdx as htdx
from HP_formula import *
import HP_plt as hplt

def test_hp_modules():
    """测试所有HP模块"""
    print("=== HP模块完整性测试 ===")
    
    # 1. 测试HP_global
    print("\n1. 测试HP_global模块...")
    print(f"当前时间: {g.get_current_time_str()}")
    print(f"机器时间: {g.MACHINETIME()}")
    print(f"是否交易时间: {g.is_trading_time()}")
    
    # 2. 测试HP_tdx
    print("\n2. 测试HP_tdx模块...")
    try:
        hq = htdx.TdxInit(ip='**************', port=7709)
        if hq.connected:
            print("✅ 通达信连接成功")
            
            # 获取茅台股票信息
            info = htdx.get_stock_info(1, '600519')
            if info:
                print(f"✅ 茅台股票信息: {info['name']} 价格:{info['price']}")
            
            # 获取K线数据
            df = htdx.get_security_bars(nCategory=9, nMarket=1, code='600519', nCount=30)
            if not df.empty:
                print(f"✅ K线数据获取成功: {len(df)} 条记录")
                print(f"最新收盘价: {df['close'].iloc[-1] if 'close' in df.columns else '未知'}")
            else:
                print("⚠️ K线数据获取失败")
            
            hq.disconnect()
        else:
            print("❌ 通达信连接失败")
            df = None
    except Exception as e:
        print(f"❌ 通达信测试失败: {e}")
        df = None
    
    # 3. 测试HP_formula
    print("\n3. 测试HP_formula模块...")
    if df is not None and not df.empty and 'close' in df.columns:
        try:
            # 初始化数据
            mydf = initmydf(df)
            CLOSE = mydf['close']
            HIGH = mydf['high'] if 'high' in mydf.columns else CLOSE
            LOW = mydf['low'] if 'low' in mydf.columns else CLOSE
            
            # 测试技术指标
            ma5 = MA(CLOSE, 5)
            ma10 = MA(CLOSE, 10)
            ema12 = EMA(CLOSE, 12)
            ema26 = EMA(CLOSE, 26)
            
            print(f"✅ MA5最新值: {ma5.iloc[-1]:.2f}")
            print(f"✅ MA10最新值: {ma10.iloc[-1]:.2f}")
            
            # 测试MACD
            dif = ema12 - ema26
            dea = EMA(dif, 9)
            macd = 2 * (dif - dea)
            
            print(f"✅ MACD DIF: {dif.iloc[-1]:.4f}")
            print(f"✅ MACD DEA: {dea.iloc[-1]:.4f}")
            print(f"✅ MACD: {macd.iloc[-1]:.4f}")
            
            # 测试KDJ
            if 'high' in mydf.columns and 'low' in mydf.columns:
                k, d, j = KDJ(HIGH, LOW, CLOSE)
                print(f"✅ KDJ K: {k.iloc[-1]:.2f}")
                print(f"✅ KDJ D: {d.iloc[-1]:.2f}")
                print(f"✅ KDJ J: {j.iloc[-1]:.2f}")
            
            # 测试交叉函数
            cross_signal = CROSS(ma5, ma10)
            if cross_signal.iloc[-1] > 0:
                print("✅ 检测到MA5上穿MA10金叉信号")
            else:
                print("✅ 当前无金叉信号")
                
        except Exception as e:
            print(f"❌ 技术指标计算失败: {e}")
    else:
        print("⚠️ 跳过技术指标测试（无有效数据）")
    
    # 4. 测试股票池
    print("\n4. 测试股票池...")
    try:
        codes = htdx.getzxgfile('test.blk')  # 测试不存在的文件
        print(f"✅ 股票池加载成功: {len(codes)} 只股票")
        for i, (market, code) in enumerate(codes[:3]):
            print(f"  {i+1}. 市场{market} 代码{code}")
    except Exception as e:
        print(f"❌ 股票池测试失败: {e}")
    
    # 5. 测试数据保存和加载
    print("\n5. 测试数据持久化...")
    try:
        test_data = {'最大买股数量': 3, '买股数量': 1, '测试时间': g.get_current_time_str()}
        if g.savem('test_hp.dat', test_data):
            loaded_data = g.loadm('test_hp.dat')
            if loaded_data:
                print(f"✅ 数据保存和加载成功: {loaded_data}")
            else:
                print("❌ 数据加载失败")
        else:
            print("❌ 数据保存失败")
    except Exception as e:
        print(f"❌ 数据持久化测试失败: {e}")
    
    print("\n=== HP模块测试完成 ===")

def test_trading_simulation():
    """模拟交易测试"""
    print("\n=== 模拟交易测试 ===")
    
    # 模拟交易参数
    最大买股数量 = 3
    买股数量 = 0
    每笔金额 = 10000
    滑点 = 0.001
    止损幅度 = -0.03
    
    # 模拟股票池
    codes = [(1, '600519'), (0, '000001'), (0, '000002')]
    
    print(f"交易参数:")
    print(f"  最大买股数量: {最大买股数量}")
    print(f"  每笔金额: {每笔金额}")
    print(f"  滑点: {滑点}")
    print(f"  止损幅度: {止损幅度}")
    
    # 模拟获取数据和计算信号
    for market, code in codes:
        print(f"\n处理股票: {code}")
        
        try:
            # 获取股票信息
            info = htdx.get_stock_info(market, code)
            if info:
                current_price = info['price']
                print(f"  当前价格: {current_price}")
                
                # 模拟买入条件检查
                if 买股数量 < 最大买股数量:
                    买入价格 = round(current_price * (1 + 滑点), 2)
                    买入数量 = int((每笔金额 / 买入价格) / 100) * 100
                    
                    print(f"  模拟买入: 价格{买入价格}, 数量{买入数量}手")
                    买股数量 += 1
                else:
                    print(f"  达到最大持仓数量，跳过")
            else:
                print(f"  获取股票信息失败")
                
        except Exception as e:
            print(f"  处理股票{code}失败: {e}")
    
    print(f"\n模拟交易完成，当前持仓数量: {买股数量}")

def main():
    """主函数"""
    print("HP模块API安装和测试")
    print("=" * 50)
    
    # 测试模块
    test_hp_modules()
    
    # 模拟交易测试
    test_trading_simulation()
    
    print("\n" + "=" * 50)
    print("🎉 所有测试完成！")
    print("\n现在您可以运行原始的交易脚本了:")
    print("  python 同花顺交易0603.py")
    print("  python 演示_同花顺自动交易按金额.py")

if __name__ == "__main__":
    main()
