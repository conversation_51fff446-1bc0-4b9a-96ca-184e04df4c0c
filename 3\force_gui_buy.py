"""
强制GUI自动化买入脚本
确保股票代码正确输入到同花顺交易界面
"""
import pyautogui
import time
from datetime import datetime

def force_input_stock_code():
    """强制在同花顺界面输入股票代码"""
    
    # 交易参数
    stock_code = '000528'
    stock_name = '柳工'
    buy_price = 8.52
    buy_amount = 100
    total_amount = buy_price * buy_amount
    
    print("🤖 强制GUI自动化买入系统")
    print("=" * 50)
    print(f"📊 交易参数:")
    print(f"   股票代码: {stock_code}")
    print(f"   股票名称: {stock_name}")
    print(f"   买入价格: {buy_price}元")
    print(f"   买入数量: {buy_amount}股")
    print(f"   总金额: {total_amount}元")
    
    # 设置pyautogui参数
    pyautogui.FAILSAFE = True
    pyautogui.PAUSE = 0.3
    
    print(f"\n⚠️ 重要提示:")
    print(f"1. 请确保同花顺买入界面已打开")
    print(f"2. 请将鼠标移开，避免干扰自动操作")
    print(f"3. 如需紧急停止，将鼠标移到屏幕左上角")
    
    input("\n准备就绪后按回车开始自动输入...")
    
    print(f"\n⏰ 倒计时开始...")
    for i in range(5, 0, -1):
        print(f"   {i}秒后开始自动输入...")
        time.sleep(1)
    
    try:
        print(f"\n🔤 开始强制输入股票代码...")
        
        # 方法1: 多次Tab键导航 + 输入
        print("   方法1: 使用Tab键导航...")
        
        # 按多次Tab键确保到达证券代码输入框
        for i in range(10):
            pyautogui.press('tab')
            time.sleep(0.2)
        
        # 清空并输入股票代码
        print(f"   清空输入框并输入: {stock_code}")
        pyautogui.hotkey('ctrl', 'a')  # 全选
        time.sleep(0.3)
        pyautogui.press('delete')      # 删除
        time.sleep(0.3)
        pyautogui.write(stock_code)    # 输入代码
        time.sleep(0.5)
        pyautogui.press('enter')       # 确认
        time.sleep(2)                  # 等待股票名称加载
        
        print(f"✅ 股票代码输入完成")
        
        # 输入买入价格
        print(f"   输入买入价格: {buy_price}")
        pyautogui.press('tab')
        time.sleep(0.3)
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(0.2)
        pyautogui.write(str(buy_price))
        time.sleep(0.5)
        
        print(f"✅ 买入价格输入完成")
        
        # 输入买入数量
        print(f"   输入买入数量: {buy_amount}")
        pyautogui.press('tab')
        time.sleep(0.3)
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(0.2)
        pyautogui.write(str(buy_amount))
        time.sleep(0.5)
        
        print(f"✅ 买入数量输入完成")
        
        # 检查输入结果
        print(f"\n🔍 请检查同花顺界面中的信息:")
        print(f"   - 证券代码: {stock_code}")
        print(f"   - 证券名称: {stock_name}")
        print(f"   - 买入价格: {buy_price}")
        print(f"   - 买入数量: {buy_amount}")
        print(f"   - 总金额: {total_amount}")
        
        # 确认是否正确
        check_result = input(f"\n信息是否正确显示在同花顺界面中？(y/n): ").lower().strip()
        
        if check_result == 'y':
            print(f"✅ 信息输入正确！")
            
            # 询问是否点击买入
            confirm_buy = input(f"是否点击买入按钮？(输入 'YES' 确认): ").strip()
            
            if confirm_buy == 'YES':
                print(f"🛒 点击买入按钮...")
                
                # 尝试多种点击买入的方法
                try:
                    # 方法1: Alt+B快捷键
                    pyautogui.hotkey('alt', 'b')
                    time.sleep(1)
                    print(f"✅ 使用Alt+B快捷键点击买入")
                    
                except:
                    try:
                        # 方法2: Enter键
                        pyautogui.press('enter')
                        time.sleep(1)
                        print(f"✅ 使用Enter键确认买入")
                        
                    except:
                        print(f"⚠️ 自动点击失败，请手动点击买入按钮")
                
                # 记录交易日志
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                log_entry = f"{timestamp} - 强制GUI买入 {stock_name}({stock_code}) {buy_amount}股 @{buy_price}元 总额{total_amount}元\n"
                
                with open("force_gui_buy_log.txt", "a", encoding="utf-8") as f:
                    f.write(log_entry)
                
                print(f"📝 交易日志已记录到 force_gui_buy_log.txt")
                print(f"✅ 买入操作完成！")
                return True
            else:
                print(f"❌ 用户取消买入")
                return False
        else:
            print(f"❌ 信息输入不正确")
            
            # 提供手动输入指导
            print(f"\n📋 请手动输入以下信息:")
            print(f"   证券代码: {stock_code}")
            print(f"   买入价格: {buy_price}")
            print(f"   买入数量: {buy_amount}")
            
            manual_confirm = input(f"手动输入完成后按回车确认: ")
            return True
            
    except Exception as e:
        print(f"❌ 自动输入失败: {e}")
        
        # 提供手动输入指导
        print(f"\n📋 请手动输入以下信息:")
        print(f"   证券代码: {stock_code}")
        print(f"   买入价格: {buy_price}")
        print(f"   买入数量: {buy_amount}")
        
        return False

def alternative_input_method():
    """备选输入方法 - 使用鼠标点击"""
    
    stock_code = '000528'
    buy_price = 8.52
    buy_amount = 100
    
    print(f"\n🖱️ 备选方法: 使用鼠标点击输入")
    print(f"请确保同花顺买入界面已打开")
    
    input("准备就绪后按回车开始...")
    
    print(f"⏰ 3秒后开始鼠标操作...")
    for i in range(3, 0, -1):
        print(f"   {i}...")
        time.sleep(1)
    
    try:
        # 根据您的截图调整坐标
        # 这些坐标可能需要根据实际界面调整
        
        # 点击证券代码输入框
        print(f"   点击证券代码输入框...")
        pyautogui.click(x=320, y=60)
        time.sleep(0.5)
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(0.2)
        pyautogui.write(stock_code)
        time.sleep(0.5)
        pyautogui.press('enter')
        time.sleep(1.5)
        
        # 点击买入价格输入框
        print(f"   点击买入价格输入框...")
        pyautogui.click(x=320, y=98)
        time.sleep(0.5)
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(0.2)
        pyautogui.write(str(buy_price))
        time.sleep(0.5)
        
        # 点击买入数量输入框
        print(f"   点击买入数量输入框...")
        pyautogui.click(x=320, y=118)
        time.sleep(0.5)
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(0.2)
        pyautogui.write(str(buy_amount))
        time.sleep(0.5)
        
        print(f"✅ 鼠标点击输入完成")
        return True
        
    except Exception as e:
        print(f"❌ 鼠标点击失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 强制GUI自动化买入 - 确保股票代码正确输入")
    print("=" * 60)
    
    print("选择输入方法:")
    print("1. 键盘Tab导航输入 (推荐)")
    print("2. 鼠标点击输入")
    print("3. 退出")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    try:
        if choice == '1':
            success = force_input_stock_code()
        elif choice == '2':
            success = alternative_input_method()
        elif choice == '3':
            print("👋 程序退出")
            return
        else:
            print("❌ 无效选择")
            return
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 GUI自动化输入完成！")
            print("请在同花顺中确认委托状态")
        else:
            print("❌ GUI自动化输入失败")
            print("请手动完成输入操作")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n❌ 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")

if __name__ == "__main__":
    main()
