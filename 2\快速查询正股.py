#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
可转债正股快速查询工具

简单快速地查询可转债对应的正股信息
"""

import pandas as pd
from typing import Dict, List, Optional
from 获取可转债数据 import ConvertibleBondData


class QuickBondStockQuery:
    """快速可转债正股查询"""
    
    def __init__(self):
        """初始化查询工具"""
        self.mapping = self._load_mapping_data()
        self.bond_tool = ConvertibleBondData()

        # 确保不使用模拟模式
        if hasattr(self.bond_tool, 'mock_mode') and self.bond_tool.mock_mode:
            print("警告: 数据服务器连接失败，无法获取真实数据")
            raise ConnectionError("无法连接到真实数据服务器")
        else:
            print("✓ 已连接到真实数据服务器")

    def query_stock(self, bond_code: str, include_quotes: bool = False) -> Optional[Dict]:
        """
        查询可转债对应的正股

        Args:
            bond_code: 可转债代码
            include_quotes: 是否包含实时行情

        Returns:
            Dict: 正股信息
        """
        if bond_code in self.mapping:
            info = self.mapping[bond_code]
            result = {
                'bond_code': bond_code,
                'bond_name': info['bond_name'],
                'stock_code': info['stock'],
                'stock_name': info['name'],
                'market': '上海' if info['stock'].startswith(('60', '68', '11', '12', '13')) else '深圳'
            }

            # 如果需要包含行情数据
            if include_quotes:
                quotes = self.get_real_quotes(bond_code=bond_code, stock_code=info['stock'])
                result.update(quotes)

                # 添加价格信息到基本信息中
                if quotes['bond_quotes']:
                    result['bond_price'] = quotes['bond_quotes'].get('price', 0)
                if quotes['stock_quotes']:
                    result['stock_price'] = quotes['stock_quotes'].get('price', 0)

            return result
        return None

    def get_real_quotes(self, bond_code: str = None, stock_code: str = None) -> Dict:
        """
        获取真实行情数据

        Args:
            bond_code: 可转债代码
            stock_code: 正股代码

        Returns:
            Dict: 行情数据
        """
        result = {
            'bond_quotes': None,
            'stock_quotes': None,
            'timestamp': pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        try:
            # 获取可转债行情
            if bond_code:
                bond_quotes = self.bond_tool.get_bond_quotes(bond_code)
                if not bond_quotes.empty:
                    result['bond_quotes'] = bond_quotes.iloc[0].to_dict()

            # 获取正股行情
            if stock_code:
                # 判断正股市场
                stock_market = 1 if stock_code.startswith(('60', '68', '11', '12', '13')) else 0

                if self.bond_tool.client and not self.bond_tool.mock_mode:
                    stock_quotes = self.bond_tool.client.quotes(symbol=stock_code, market=stock_market)
                    if not stock_quotes.empty:
                        result['stock_quotes'] = stock_quotes.iloc[0].to_dict()
                else:
                    print(f"无法获取正股 {stock_code} 行情：连接未建立")

        except Exception as e:
            print(f"获取行情数据失败: {e}")

        return result

    def query_bonds(self, stock_code: str) -> List[Dict]:
        """
        查询正股对应的可转债
        
        Args:
            stock_code: 正股代码
            
        Returns:
            List[Dict]: 可转债列表
        """
        bonds = []
        for bond_code, info in self.mapping.items():
            if info['stock'] == stock_code:
                bonds.append({
                    'bond_code': bond_code,
                    'bond_name': info['bond_name'],
                    'stock_code': stock_code,
                    'stock_name': info['name']
                })
        return bonds
    
    def search(self, keyword: str) -> List[Dict]:
        """
        搜索可转债或正股
        
        Args:
            keyword: 搜索关键词
            
        Returns:
            List[Dict]: 搜索结果
        """
        results = []
        keyword = keyword.upper()
        
        for bond_code, info in self.mapping.items():
            if (keyword in bond_code or 
                keyword in info['stock'] or 
                keyword in info['name'] or 
                keyword in info['bond_name']):
                
                results.append({
                    'bond_code': bond_code,
                    'bond_name': info['bond_name'],
                    'stock_code': info['stock'],
                    'stock_name': info['name'],
                    'market': '上海' if info['stock'].startswith(('60', '68', '11', '12', '13')) else '深圳'
                })
        
        return results
    
    def export_to_csv(self, filename: str = None) -> str:
        """
        导出映射表到CSV
        
        Args:
            filename: 文件名
            
        Returns:
            str: 文件路径
        """
        if filename is None:
            filename = f"可转债正股映射表.csv"
        
        data = []
        for bond_code, info in self.mapping.items():
            data.append({
                'bond_code': bond_code,
                'bond_name': info['bond_name'],
                'stock_code': info['stock'],
                'stock_name': info['name'],
                'market': '上海' if info['stock'].startswith(('60', '68', '11', '12', '13')) else '深圳'
            })
        
        df = pd.DataFrame(data)
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"映射表已导出到: {filename}")
        return filename

    def close(self):
        """关闭连接"""
        if hasattr(self, 'bond_tool'):
            self.bond_tool.close()


def main():
    """主函数"""
    print("=" * 50)
    print("可转债正股快速查询工具")
    print("=" * 50)
    
    query = QuickBondStockQuery()
    
    while True:
        print("\n可用功能:")
        print("1. 查询可转债对应的正股")
        print("2. 查询正股对应的可转债")
        print("3. 查询可转债和正股实时行情")
        print("4. 搜索可转债/正股")
        print("5. 导出映射表")
        print("6. 退出")
        
        choice = input("\n请选择功能 (1-6): ").strip()
        
        if choice == '1':
            bond_code = input("请输入可转债代码: ").strip()
            result = query.query_stock(bond_code)
            if result:
                print(f"\n查询结果:")
                print(f"可转债: {result['bond_code']} - {result['bond_name']}")
                print(f"对应正股: {result['stock_code']} - {result['stock_name']}")
                print(f"市场: {result['market']}")
            else:
                print(f"未找到可转债 {bond_code} 对应的正股信息")
        
        elif choice == '2':
            stock_code = input("请输入正股代码: ").strip()
            results = query.query_bonds(stock_code)
            if results:
                print(f"\n查询结果:")
                for result in results:
                    print(f"可转债: {result['bond_code']} - {result['bond_name']}")
            else:
                print(f"未找到正股 {stock_code} 对应的可转债")
        
        elif choice == '3':
            bond_code = input("请输入可转债代码: ").strip()
            print("正在获取实时行情数据...")
            result = query.query_stock(bond_code, include_quotes=True)

            if result:
                print(f"\n=== {result['bond_code']} - {result['bond_name']} ===")
                print(f"对应正股: {result['stock_code']} - {result['stock_name']}")
                print(f"市场: {result['market']}")
                print(f"查询时间: {result.get('timestamp', 'N/A')}")

                # 显示可转债行情
                if result.get('bond_quotes'):
                    bond_q = result['bond_quotes']
                    print(f"\n可转债行情:")
                    print(f"  当前价格: {bond_q.get('price', 'N/A')}")
                    print(f"  开盘价: {bond_q.get('open', 'N/A')}")
                    print(f"  最高价: {bond_q.get('high', 'N/A')}")
                    print(f"  最低价: {bond_q.get('low', 'N/A')}")
                    print(f"  成交量: {bond_q.get('vol', 'N/A')}")
                else:
                    print(f"\n可转债行情: 暂无数据")

                # 显示正股行情
                if result.get('stock_quotes'):
                    stock_q = result['stock_quotes']
                    print(f"\n正股行情:")
                    print(f"  当前价格: {stock_q.get('price', 'N/A')}")
                    print(f"  开盘价: {stock_q.get('open', 'N/A')}")
                    print(f"  最高价: {stock_q.get('high', 'N/A')}")
                    print(f"  最低价: {stock_q.get('low', 'N/A')}")
                    print(f"  成交量: {stock_q.get('vol', 'N/A')}")
                else:
                    print(f"\n正股行情: 暂无数据")
            else:
                print(f"未找到可转债 {bond_code} 的信息")

        elif choice == '4':
            keyword = input("请输入搜索关键词: ").strip()
            results = query.search(keyword)
            if results:
                print(f"\n搜索结果 (共 {len(results)} 条):")
                for i, result in enumerate(results[:10], 1):  # 只显示前10条
                    print(f"{i:2d}. {result['bond_code']}({result['bond_name']}) -> {result['stock_code']}({result['stock_name']})")
                if len(results) > 10:
                    print(f"... 还有 {len(results) - 10} 条结果")
            else:
                print(f"未找到包含 '{keyword}' 的结果")
        
        elif choice == '5':
            filename = query.export_to_csv()
            print(f"映射表已导出: {filename}")

        elif choice == '6':
            print("退出程序")
            query.close()
            break
        
        else:
            print("无效选择，请重新输入")


if __name__ == "__main__":
    main()
