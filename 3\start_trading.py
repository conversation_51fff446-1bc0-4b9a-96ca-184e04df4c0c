"""
同花顺交易接口启动脚本
用于启动自动交易监控系统
"""
import sys
import os
import time
import signal
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ths_trader import ThsTrader
from config import get_config, get_test_config, validate_config

class TradingSystem:
    """交易系统管理类"""
    
    def __init__(self):
        self.trader = None
        self.running = False
        
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            print("\n收到退出信号，正在安全关闭...")
            self.stop()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def choose_mode(self):
        """选择运行模式"""
        print("请选择运行模式:")
        print("1. 测试模式 (模拟交易，安全测试)")
        print("2. 实盘模式 (真实交易，需要同花顺客户端)")
        
        while True:
            choice = input("请输入选择 (1-2): ").strip()
            if choice == '1':
                return get_test_config()
            elif choice == '2':
                return get_config()
            else:
                print("无效选择，请重新输入")
    
    def validate_setup(self, config):
        """验证系统设置"""
        print("\n=== 系统验证 ===")
        
        # 验证配置
        errors = validate_config(config)
        if errors:
            print("配置验证失败:")
            for error in errors:
                print(f"  ✗ {error}")
            return False
        print("✓ 配置验证通过")
        
        # 测试数据源连接
        trader = ThsTrader(config)
        data = trader.get_data_source()
        if not data:
            print("✗ 数据源连接失败")
            return False
        print(f"✓ 数据源连接成功，获取到 {len(data)} 条记录")
        
        # 如果是实盘模式，提醒用户检查同花顺客户端
        if not config['trade_config']['dry_run']:
            print("\n⚠️  实盘模式注意事项:")
            print("1. 确保同花顺客户端已正常启动并登录")
            print("2. 检查客户端设置（界面超时时间=0，默认价格/数量=空）")
            print("3. 确保账户资金充足")
            print("4. 建议先小额测试")
            
            confirm = input("\n确认继续？(y/n): ").lower()
            if confirm != 'y':
                return False
        
        return True
    
    def start(self):
        """启动交易系统"""
        print("同花顺自动交易系统")
        print("=" * 50)
        
        # 设置信号处理
        self.setup_signal_handlers()
        
        # 选择运行模式
        config = self.choose_mode()
        
        # 验证系统设置
        if not self.validate_setup(config):
            print("系统验证失败，退出")
            return
        
        # 创建交易实例
        self.trader = ThsTrader(config)
        
        # 如果是实盘模式，尝试登录
        if not config['trade_config']['dry_run']:
            print("\n=== 登录同花顺客户端 ===")
            if not self.trader.login():
                print("登录失败，退出")
                return
            print("✓ 登录成功")
        
        # 显示当前数据
        self.show_current_data()
        
        # 开始监控
        self.start_monitoring(config)
    
    def show_current_data(self):
        """显示当前数据"""
        print("\n=== 当前数据 ===")
        data = self.trader.get_data_source()
        if data:
            print(f"数据源记录数: {len(data)}")
            print("最新3条记录:")
            for i, record in enumerate(data[:3]):
                print(f"  {i+1}. {record['name']}({record['code']}) - {record['bkname']} - {record['addtime']}")
        else:
            print("无法获取数据")
    
    def start_monitoring(self, config):
        """开始监控"""
        interval = config['monitor_config']['interval']
        
        print(f"\n=== 开始监控 ===")
        print(f"监控间隔: {interval}秒")
        print(f"运行模式: {'测试模式' if config['trade_config']['dry_run'] else '实盘模式'}")
        print("按 Ctrl+C 停止监控")
        print("-" * 50)
        
        self.running = True
        self.trader.start_monitoring(interval)
        
        try:
            # 主循环，显示状态信息
            while self.running:
                time.sleep(30)  # 每30秒显示一次状态
                self.show_status()
        except KeyboardInterrupt:
            pass
        finally:
            self.stop()
    
    def show_status(self):
        """显示运行状态"""
        if not self.running:
            return
            
        print(f"\n[{time.strftime('%H:%M:%S')}] 系统运行中...")
        
        # 显示账户信息（仅实盘模式）
        if self.trader.is_logged_in:
            account_info = self.trader.get_account_info()
            if account_info:
                print(f"  账户余额: {account_info.get('balance', '未知')}")
                print(f"  持仓数量: {len(account_info.get('position', []))}")
        
        # 显示今日交易（仅实盘模式）
        if self.trader.is_logged_in:
            today_trades = self.trader.get_today_trades()
            if today_trades:
                print(f"  今日成交: {len(today_trades)} 笔")
    
    def stop(self):
        """停止系统"""
        if self.running:
            print("\n正在停止监控...")
            self.running = False
            if self.trader:
                self.trader.stop_monitoring()
            print("系统已停止")


def main():
    """主函数"""
    try:
        system = TradingSystem()
        system.start()
    except Exception as e:
        print(f"系统运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
