"""
专门解决股票代码输入问题的脚本
针对同花顺证券代码输入框无法输入的问题
"""
import pyautogui
import time
from datetime import datetime

def fix_stock_code_input():
    """专门修复股票代码输入问题"""
    
    stock_code = '000528'
    stock_name = '柳工'
    
    print("🔧 股票代码输入修复工具")
    print("=" * 50)
    print(f"目标: 在同花顺证券代码框输入 {stock_code}")
    print(f"股票: {stock_name}")
    
    # 设置pyautogui参数
    pyautogui.FAILSAFE = True
    pyautogui.PAUSE = 0.2
    
    print(f"\n📋 操作步骤:")
    print(f"1. 确保同花顺买入界面已打开")
    print(f"2. 程序将尝试多种方法输入股票代码")
    print(f"3. 如果自动输入失败，会提供手动指导")
    
    input("\n准备就绪后按回车开始...")
    
    print(f"\n⏰ 倒计时开始...")
    for i in range(3, 0, -1):
        print(f"   {i}秒后开始...")
        time.sleep(1)
    
    success = False
    
    # 方法1: 精确点击证券代码输入框
    print(f"\n🎯 方法1: 精确点击证券代码输入框")
    try:
        # 根据您的截图，证券代码输入框位置
        print(f"   点击证券代码输入框...")
        pyautogui.click(x=320, y=85)  # 证券代码框位置
        time.sleep(0.5)
        
        # 确保输入框获得焦点
        pyautogui.click(x=320, y=85)  # 再次点击确保焦点
        time.sleep(0.3)
        
        # 清空输入框
        print(f"   清空输入框...")
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(0.2)
        pyautogui.press('delete')
        time.sleep(0.2)
        
        # 输入股票代码
        print(f"   输入股票代码: {stock_code}")
        pyautogui.write(stock_code)
        time.sleep(0.5)
        
        # 按回车确认
        pyautogui.press('enter')
        time.sleep(1.5)
        
        print(f"✅ 方法1完成")
        
        # 检查是否成功
        check1 = input(f"证券代码框是否显示 '{stock_code}'？(y/n): ").lower().strip()
        if check1 == 'y':
            success = True
            print(f"🎉 方法1成功！")
        else:
            print(f"⚠️ 方法1失败，尝试方法2...")
            
    except Exception as e:
        print(f"❌ 方法1异常: {e}")
    
    # 方法2: 使用Tab键导航
    if not success:
        print(f"\n⌨️ 方法2: 使用Tab键导航")
        try:
            print(f"   使用Tab键导航到证券代码框...")
            
            # 按多次Tab键
            for i in range(15):
                pyautogui.press('tab')
                time.sleep(0.1)
            
            # 清空并输入
            print(f"   清空并输入股票代码...")
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.2)
            pyautogui.press('delete')
            time.sleep(0.2)
            pyautogui.write(stock_code)
            time.sleep(0.5)
            pyautogui.press('enter')
            time.sleep(1.5)
            
            print(f"✅ 方法2完成")
            
            # 检查是否成功
            check2 = input(f"证券代码框是否显示 '{stock_code}'？(y/n): ").lower().strip()
            if check2 == 'y':
                success = True
                print(f"🎉 方法2成功！")
            else:
                print(f"⚠️ 方法2失败，尝试方法3...")
                
        except Exception as e:
            print(f"❌ 方法2异常: {e}")
    
    # 方法3: 逐字符输入
    if not success:
        print(f"\n🔤 方法3: 逐字符输入")
        try:
            # 先点击输入框
            pyautogui.click(x=320, y=85)
            time.sleep(0.5)
            
            # 清空
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.2)
            pyautogui.press('delete')
            time.sleep(0.2)
            
            # 逐字符输入
            print(f"   逐字符输入: {stock_code}")
            for char in stock_code:
                pyautogui.write(char)
                time.sleep(0.3)  # 每个字符间隔
            
            time.sleep(0.5)
            pyautogui.press('enter')
            time.sleep(1.5)
            
            print(f"✅ 方法3完成")
            
            # 检查是否成功
            check3 = input(f"证券代码框是否显示 '{stock_code}'？(y/n): ").lower().strip()
            if check3 == 'y':
                success = True
                print(f"🎉 方法3成功！")
            else:
                print(f"⚠️ 方法3失败，尝试方法4...")
                
        except Exception as e:
            print(f"❌ 方法3异常: {e}")
    
    # 方法4: 使用剪贴板
    if not success:
        print(f"\n📋 方法4: 使用剪贴板")
        try:
            import pyperclip
            
            # 复制股票代码到剪贴板
            pyperclip.copy(stock_code)
            print(f"   已复制 '{stock_code}' 到剪贴板")
            
            # 点击输入框
            pyautogui.click(x=320, y=85)
            time.sleep(0.5)
            
            # 清空并粘贴
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.2)
            pyautogui.hotkey('ctrl', 'v')  # 粘贴
            time.sleep(0.5)
            pyautogui.press('enter')
            time.sleep(1.5)
            
            print(f"✅ 方法4完成")
            
            # 检查是否成功
            check4 = input(f"证券代码框是否显示 '{stock_code}'？(y/n): ").lower().strip()
            if check4 == 'y':
                success = True
                print(f"🎉 方法4成功！")
                
        except ImportError:
            print(f"❌ 需要安装pyperclip: pip install pyperclip")
        except Exception as e:
            print(f"❌ 方法4异常: {e}")
    
    # 如果所有自动方法都失败
    if not success:
        print(f"\n❌ 所有自动方法都失败")
        print(f"📋 手动输入指导:")
        print(f"   1. 用鼠标点击证券代码输入框")
        print(f"   2. 清空输入框内容")
        print(f"   3. 手动输入: {stock_code}")
        print(f"   4. 按回车确认")
        
        manual_input = input(f"\n手动输入完成后按回车确认: ")
        
        # 最终检查
        final_check = input(f"证券代码框是否显示 '{stock_code}'？(y/n): ").lower().strip()
        if final_check == 'y':
            success = True
            print(f"🎉 手动输入成功！")
    
    # 总结
    print(f"\n" + "=" * 50)
    if success:
        print(f"✅ 股票代码输入成功！")
        print(f"📊 证券代码: {stock_code}")
        print(f"📈 股票名称: {stock_name}")
        print(f"💡 现在可以继续设置价格和数量")
        
        # 记录成功日志
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"{timestamp} - 成功输入股票代码 {stock_code} ({stock_name})\n"
        
        with open("stock_code_input_log.txt", "a", encoding="utf-8") as f:
            f.write(log_entry)
        
        print(f"📝 成功日志已记录")
        
    else:
        print(f"❌ 股票代码输入失败")
        print(f"💡 建议:")
        print(f"   1. 检查同花顺界面是否正确打开")
        print(f"   2. 尝试手动点击输入框")
        print(f"   3. 检查输入法状态")
        print(f"   4. 重启同花顺后重试")
    
    print(f"=" * 50)
    return success

def main():
    """主函数"""
    try:
        fix_stock_code_input()
    except KeyboardInterrupt:
        print(f"\n❌ 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")

if __name__ == "__main__":
    main()
