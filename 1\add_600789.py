#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
直接向测试板块添加股票 600789

简单直接的脚本，无需交互
"""

from stock_crud_tool import StockCRUDTool


def main():
    """直接添加股票 600789 到测试板块"""
    print("向测试板块添加股票 600789")
    print("-" * 30)
    
    # 配置
    tdx_dir = "D:/zyb(6.3.5 7.66)M"
    block_name = "测试板块"
    stock_code = "600789"
    
    # 初始化工具
    crud_tool = StockCRUDTool(tdxdir=tdx_dir)
    
    # 查询当前板块
    print(f"查询板块 '{block_name}' 当前内容...")
    result = crud_tool.get_block_stocks(block_name)
    
    if result['success']:
        print(f"当前股票: {result['stocks']}")
        
        # 检查是否已存在
        if stock_code in result['stocks']:
            print(f"股票 {stock_code} 已存在，无需添加")
            return
        
        # 添加股票
        print(f"添加股票 {stock_code}...")
        result = crud_tool.add_stocks_to_block(block_name, [stock_code])
        
        if result['success']:
            print(f"✓ 添加成功！")
            print(f"最终股票数量: {result['final_count']}")
            
            # 验证结果
            result = crud_tool.get_block_stocks(block_name)
            if result['success']:
                print(f"最终股票列表: {result['stocks']}")
        else:
            print(f"✗ 添加失败: {result['message']}")
    else:
        print(f"板块不存在: {result['message']}")
        print(f"创建新板块并添加股票 {stock_code}...")
        
        result = crud_tool.create_block_with_stocks(block_name, [stock_code])
        if result['success']:
            print(f"✓ 创建成功！添加了 {result['added_count']} 只股票")
        else:
            print(f"✗ 创建失败: {result['message']}")


if __name__ == "__main__":
    main()
