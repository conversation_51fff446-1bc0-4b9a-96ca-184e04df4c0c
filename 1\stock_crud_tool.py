#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
板块文件中股票数据 CRUD 操作工具

功能：
1. 对板块文件里的股票数据进行增删改查操作
2. 提供批量操作功能
3. 数据验证和错误处理
4. 支持股票代码格式验证

作者: Stock CRUD Tool
日期: 2025-08-06
"""

import os
import re
from typing import List, Dict, Set, Optional, Tuple
from dataclasses import dataclass

try:
    from mootdx.tools.customize import Customize
except ImportError:
    print("警告: 无法导入 mootdx.tools.customize.Customize")
    Customize = None


@dataclass
class StockInfo:
    """股票信息数据类"""
    code: str
    name: str = ""
    market: str = ""  # 市场类型：SH/SZ
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.market and self.code:
            self.market = self._detect_market()
    
    def _detect_market(self) -> str:
        """根据股票代码检测市场"""
        if self.code.startswith(('60', '68', '11', '12', '13')):
            return 'SH'  # 上海
        elif self.code.startswith(('00', '30', '12', '20')):
            return 'SZ'  # 深圳
        return 'UNKNOWN'


class StockCRUDTool:
    """股票数据 CRUD 操作工具类"""
    
    def __init__(self, tdxdir: str = None):
        """
        初始化工具

        Args:
            tdxdir: 通达信安装目录，如果为None则使用模拟模式
        """
        self.tdxdir = tdxdir
        self.custom = None
        self.mock_mode = False

        if Customize and tdxdir and self._validate_tdx_dir(tdxdir):
            try:
                self.custom = Customize(tdxdir=tdxdir)
            except Exception as e:
                print(f"初始化 Customize 失败: {e}")
                print("切换到模拟模式...")
                self.custom = self._create_mock_customize()
                self.mock_mode = True
        else:
            self.custom = self._create_mock_customize()
            self.mock_mode = True

        # 股票代码验证正则
        self.stock_pattern = re.compile(r'^[0-9]{6}$')
    
    def _validate_tdx_dir(self, tdxdir: str) -> bool:
        """验证通达信目录是否有效"""
        if not tdxdir or not os.path.exists(tdxdir):
            return False

        # 检查是否存在必要的子目录
        blocknew_dir = os.path.join(tdxdir, "T0002", "blocknew")
        return os.path.exists(blocknew_dir)

    def _create_mock_customize(self):
        """创建模拟的 Customize 对象"""
        from unittest.mock import MagicMock

        mock = MagicMock()

        # 模拟数据存储
        self._mock_blocks = {}

        def mock_create(name, symbol):
            if name in self._mock_blocks:
                return False  # 板块已存在
            self._mock_blocks[name] = symbol[:]
            return True

        def mock_update(name, symbol):
            if name not in self._mock_blocks:
                return False  # 板块不存在
            self._mock_blocks[name] = symbol[:]
            return True

        def mock_search(name):
            return self._mock_blocks.get(name, None)

        def mock_remove(name):
            if name in self._mock_blocks:
                del self._mock_blocks[name]
                return True
            return False

        mock.create.side_effect = mock_create
        mock.update.side_effect = mock_update
        mock.search.side_effect = mock_search
        mock.remove.side_effect = mock_remove

        return mock

    def validate_stock_code(self, code: str) -> bool:
        """
        验证股票代码格式
        
        Args:
            code: 股票代码
            
        Returns:
            bool: 是否有效
        """
        return bool(self.stock_pattern.match(code))
    
    def validate_stock_codes(self, codes: List[str]) -> Tuple[List[str], List[str]]:
        """
        批量验证股票代码
        
        Args:
            codes: 股票代码列表
            
        Returns:
            Tuple[List[str], List[str]]: (有效代码列表, 无效代码列表)
        """
        valid_codes = []
        invalid_codes = []
        
        for code in codes:
            if self.validate_stock_code(code):
                valid_codes.append(code)
            else:
                invalid_codes.append(code)
        
        return valid_codes, invalid_codes
    
    def create_block_with_stocks(self, block_name: str, stock_codes: List[str]) -> Dict[str, any]:
        """
        创建板块并添加股票（Create 操作）
        
        Args:
            block_name: 板块名称
            stock_codes: 股票代码列表
            
        Returns:
            Dict: 操作结果
        """
        result = {
            'success': False,
            'message': '',
            'valid_codes': [],
            'invalid_codes': [],
            'added_count': 0
        }
        
        if not self.custom:
            result['message'] = 'Customize 对象未初始化'
            return result
        
        # 验证股票代码
        valid_codes, invalid_codes = self.validate_stock_codes(stock_codes)
        result['valid_codes'] = valid_codes
        result['invalid_codes'] = invalid_codes
        
        if invalid_codes:
            print(f"警告: 发现无效股票代码: {invalid_codes}")
        
        if not valid_codes:
            result['message'] = '没有有效的股票代码'
            return result
        
        try:
            success = self.custom.create(name=block_name, symbol=valid_codes)
            if success:
                result['success'] = True
                result['added_count'] = len(valid_codes)
                result['message'] = f'成功创建板块 "{block_name}"，添加 {len(valid_codes)} 只股票'
            else:
                result['message'] = f'创建板块 "{block_name}" 失败'
        except Exception as e:
            result['message'] = f'创建板块时发生错误: {str(e)}'
        
        return result
    
    def get_block_stocks(self, block_name: str) -> Dict[str, any]:
        """
        获取板块中的股票列表（Read 操作）
        
        Args:
            block_name: 板块名称
            
        Returns:
            Dict: 查询结果
        """
        result = {
            'success': False,
            'message': '',
            'stocks': [],
            'stock_count': 0
        }
        
        if not self.custom:
            result['message'] = 'Customize 对象未初始化'
            return result
        
        try:
            stocks = self.custom.search(name=block_name)
            if stocks:
                result['success'] = True
                result['stocks'] = stocks
                result['stock_count'] = len(stocks)
                result['message'] = f'板块 "{block_name}" 包含 {len(stocks)} 只股票'
            else:
                result['message'] = f'板块 "{block_name}" 不存在或为空'
        except Exception as e:
            result['message'] = f'查询板块时发生错误: {str(e)}'
        
        return result
    
    def add_stocks_to_block(self, block_name: str, new_stock_codes: List[str]) -> Dict[str, any]:
        """
        向板块添加股票（Update 操作 - 添加）
        
        Args:
            block_name: 板块名称
            new_stock_codes: 要添加的股票代码列表
            
        Returns:
            Dict: 操作结果
        """
        result = {
            'success': False,
            'message': '',
            'added_codes': [],
            'duplicate_codes': [],
            'invalid_codes': [],
            'final_count': 0
        }
        
        # 获取当前板块股票
        current_result = self.get_block_stocks(block_name)
        if not current_result['success']:
            result['message'] = current_result['message']
            return result
        
        current_stocks = set(current_result['stocks'])
        
        # 验证新股票代码
        valid_codes, invalid_codes = self.validate_stock_codes(new_stock_codes)
        result['invalid_codes'] = invalid_codes
        
        # 检查重复
        added_codes = []
        duplicate_codes = []
        
        for code in valid_codes:
            if code in current_stocks:
                duplicate_codes.append(code)
            else:
                added_codes.append(code)
        
        result['added_codes'] = added_codes
        result['duplicate_codes'] = duplicate_codes
        
        if not added_codes:
            result['message'] = '没有新的股票需要添加'
            return result
        
        # 合并股票列表
        final_stocks = list(current_stocks) + added_codes
        
        try:
            success = self.custom.update(name=block_name, symbol=final_stocks)
            if success:
                result['success'] = True
                result['final_count'] = len(final_stocks)
                result['message'] = f'成功向板块 "{block_name}" 添加 {len(added_codes)} 只股票'
            else:
                result['message'] = f'更新板块 "{block_name}" 失败'
        except Exception as e:
            result['message'] = f'添加股票时发生错误: {str(e)}'
        
        return result
    
    def remove_stocks_from_block(self, block_name: str, remove_stock_codes: List[str]) -> Dict[str, any]:
        """
        从板块移除股票（Update 操作 - 删除）
        
        Args:
            block_name: 板块名称
            remove_stock_codes: 要移除的股票代码列表
            
        Returns:
            Dict: 操作结果
        """
        result = {
            'success': False,
            'message': '',
            'removed_codes': [],
            'not_found_codes': [],
            'final_count': 0
        }
        
        # 获取当前板块股票
        current_result = self.get_block_stocks(block_name)
        if not current_result['success']:
            result['message'] = current_result['message']
            return result
        
        current_stocks = set(current_result['stocks'])
        
        # 检查要删除的股票是否存在
        removed_codes = []
        not_found_codes = []
        
        for code in remove_stock_codes:
            if code in current_stocks:
                removed_codes.append(code)
            else:
                not_found_codes.append(code)
        
        result['removed_codes'] = removed_codes
        result['not_found_codes'] = not_found_codes
        
        if not removed_codes:
            result['message'] = '没有找到要删除的股票'
            return result
        
        # 移除股票
        final_stocks = [code for code in current_stocks if code not in removed_codes]
        
        try:
            success = self.custom.update(name=block_name, symbol=final_stocks)
            if success:
                result['success'] = True
                result['final_count'] = len(final_stocks)
                result['message'] = f'成功从板块 "{block_name}" 移除 {len(removed_codes)} 只股票'
            else:
                result['message'] = f'更新板块 "{block_name}" 失败'
        except Exception as e:
            result['message'] = f'移除股票时发生错误: {str(e)}'
        
        return result
    
    def replace_block_stocks(self, block_name: str, new_stock_codes: List[str]) -> Dict[str, any]:
        """
        替换板块中的所有股票（Update 操作 - 替换）
        
        Args:
            block_name: 板块名称
            new_stock_codes: 新的股票代码列表
            
        Returns:
            Dict: 操作结果
        """
        result = {
            'success': False,
            'message': '',
            'valid_codes': [],
            'invalid_codes': [],
            'final_count': 0
        }
        
        # 验证股票代码
        valid_codes, invalid_codes = self.validate_stock_codes(new_stock_codes)
        result['valid_codes'] = valid_codes
        result['invalid_codes'] = invalid_codes
        
        if not valid_codes:
            result['message'] = '没有有效的股票代码'
            return result
        
        try:
            success = self.custom.update(name=block_name, symbol=valid_codes)
            if success:
                result['success'] = True
                result['final_count'] = len(valid_codes)
                result['message'] = f'成功替换板块 "{block_name}" 中的股票，现有 {len(valid_codes)} 只股票'
            else:
                result['message'] = f'替换板块 "{block_name}" 股票失败'
        except Exception as e:
            result['message'] = f'替换股票时发生错误: {str(e)}'
        
        return result
    
    def delete_block(self, block_name: str) -> Dict[str, any]:
        """
        删除整个板块（Delete 操作）
        
        Args:
            block_name: 板块名称
            
        Returns:
            Dict: 操作结果
        """
        result = {
            'success': False,
            'message': ''
        }
        
        if not self.custom:
            result['message'] = 'Customize 对象未初始化'
            return result
        
        try:
            success = self.custom.remove(name=block_name)
            if success:
                result['success'] = True
                result['message'] = f'成功删除板块 "{block_name}"'
            else:
                result['message'] = f'删除板块 "{block_name}" 失败'
        except Exception as e:
            result['message'] = f'删除板块时发生错误: {str(e)}'
        
        return result
    
    def batch_operations(self, operations: List[Dict]) -> List[Dict]:
        """
        批量操作
        
        Args:
            operations: 操作列表，每个操作包含 type, block_name, stocks 等字段
            
        Returns:
            List[Dict]: 操作结果列表
        """
        results = []
        
        for i, op in enumerate(operations):
            op_type = op.get('type', '').lower()
            block_name = op.get('block_name', '')
            stocks = op.get('stocks', [])
            
            print(f"\n执行批量操作 {i+1}/{len(operations)}: {op_type} - {block_name}")
            
            if op_type == 'create':
                result = self.create_block_with_stocks(block_name, stocks)
            elif op_type == 'read':
                result = self.get_block_stocks(block_name)
            elif op_type == 'add':
                result = self.add_stocks_to_block(block_name, stocks)
            elif op_type == 'remove':
                result = self.remove_stocks_from_block(block_name, stocks)
            elif op_type == 'replace':
                result = self.replace_block_stocks(block_name, stocks)
            elif op_type == 'delete':
                result = self.delete_block(block_name)
            else:
                result = {
                    'success': False,
                    'message': f'未知操作类型: {op_type}'
                }
            
            result['operation'] = op
            results.append(result)
            
            print(f"结果: {result['message']}")
        
        return results


def demo_stock_crud_operations():
    """演示股票 CRUD 操作"""
    print("=" * 60)
    print("股票数据 CRUD 操作演示")
    print("=" * 60)
    
    # 使用临时目录进行演示
    tool = StockCRUDTool()
    
    # 演示数据
    demo_operations = [
        {
            'type': 'create',
            'block_name': 'CRUD测试板块',
            'stocks': ['600036', '600016', '000001', 'invalid_code']
        },
        {
            'type': 'read',
            'block_name': 'CRUD测试板块'
        },
        {
            'type': 'add',
            'block_name': 'CRUD测试板块',
            'stocks': ['000002', '600519', '600036']  # 包含重复
        },
        {
            'type': 'remove',
            'block_name': 'CRUD测试板块',
            'stocks': ['600016', '999999']  # 包含不存在的
        },
        {
            'type': 'replace',
            'block_name': 'CRUD测试板块',
            'stocks': ['300059', '002415', '000858']
        },
        {
            'type': 'read',
            'block_name': 'CRUD测试板块'
        },
        {
            'type': 'delete',
            'block_name': 'CRUD测试板块'
        }
    ]
    
    # 执行批量操作
    results = tool.batch_operations(demo_operations)
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("操作结果汇总")
    print("=" * 60)
    
    success_count = sum(1 for r in results if r['success'])
    print(f"总操作数: {len(results)}")
    print(f"成功操作: {success_count}")
    print(f"失败操作: {len(results) - success_count}")


if __name__ == "__main__":
    demo_stock_crud_operations()
