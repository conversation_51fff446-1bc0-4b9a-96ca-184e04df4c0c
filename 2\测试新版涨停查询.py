#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试新版涨停正股转债查询功能

使用东方财富终极完整可转债数据测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from 涨停正股转债查询 import LimitUpStockBondQuery


def test_bond_mapping_loading():
    """测试可转债映射关系加载"""
    print("🔍 测试可转债映射关系加载...")
    
    # 数据库配置（用于初始化，但不连接数据库）
    db_config = {
        'host': '***********',
        'port': 3306,
        'user': 'iQuant',
        'password': 'NAAnwaRsb8YGN3F5',
        'database': 'iquant',
        'charset': 'utf8mb4'
    }
    
    try:
        # 初始化查询器（会自动加载映射关系）
        query_tool = LimitUpStockBondQuery(db_config)
        
        if query_tool.bond_stock_mapping:
            print(f"✅ 映射关系加载成功！")
            print(f"📊 统计信息:")
            print(f"  正股数量: {len(query_tool.bond_stock_mapping)}")
            
            total_bonds = sum(len(bonds) for bonds in query_tool.bond_stock_mapping.values())
            print(f"  可转债数量: {total_bonds}")
            
            # 统计有实时数据的可转债
            realtime_bonds = sum(1 for bonds in query_tool.bond_stock_mapping.values() 
                               for bond in bonds if bond.get('has_realtime_data'))
            print(f"  有实时数据: {realtime_bonds}")
            
            # 测试特定股票
            print(f"\n🎯 测试特定股票:")
            test_stocks = ['600326', '002475', '600000', '300058', '601988']
            
            for stock_code in test_stocks:
                if stock_code in query_tool.bond_stock_mapping:
                    bonds = query_tool.bond_stock_mapping[stock_code]
                    print(f"\n  ✓ {stock_code}: {len(bonds)} 只可转债")
                    
                    for bond in bonds:
                        realtime_str = " 🔥" if bond.get('has_realtime_data') else ""
                        price_str = f" 价格:{bond.get('current_price')}" if bond.get('current_price') else ""
                        rating_str = f" 评级:{bond.get('rating')}" if bond.get('rating') else ""
                        
                        print(f"    • {bond['bond_code']} {bond['bond_name']}{price_str}{rating_str}{realtime_str}")
                else:
                    print(f"  ✗ {stock_code}: 未找到对应可转债")
            
            # 显示有实时数据的可转债示例
            print(f"\n🔥 有实时数据的可转债示例:")
            realtime_examples = []
            for stock_code, bonds in query_tool.bond_stock_mapping.items():
                for bond in bonds:
                    if bond.get('has_realtime_data') and bond.get('current_price'):
                        realtime_examples.append((stock_code, bond))
                        if len(realtime_examples) >= 10:
                            break
                if len(realtime_examples) >= 10:
                    break
            
            for i, (stock_code, bond) in enumerate(realtime_examples, 1):
                price = bond.get('current_price', '')
                change = bond.get('change_rate', '')
                rating = bond.get('rating', '')
                
                change_str = f" 涨幅:{change}%" if change and change not in ['', '-'] else ""
                rating_str = f" 评级:{rating}" if rating and rating not in ['', '-'] else ""
                
                print(f"  {i:2d}. {bond['bond_code']} {bond['bond_name']} → {stock_code}")
                print(f"      价格:{price}{change_str}{rating_str}")
            
            return True
            
        else:
            print("❌ 映射关系加载失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_mock_limit_up_query():
    """测试模拟涨停查询（不连接数据库）"""
    print(f"\n{'='*60}")
    print("🔍 测试模拟涨停查询")
    print(f"{'='*60}")
    
    # 数据库配置
    db_config = {
        'host': '***********',
        'port': 3306,
        'user': 'iQuant',
        'password': 'NAAnwaRsb8YGN3F5',
        'database': 'iquant',
        'charset': 'utf8mb4'
    }
    
    try:
        # 初始化查询器
        query_tool = LimitUpStockBondQuery(db_config)
        
        if not query_tool.bond_stock_mapping:
            print("❌ 映射关系未加载，无法进行测试")
            return False
        
        # 模拟一些涨停股票数据
        mock_limit_up_stocks = [
            {'symbol': '600326', 'day': '2025-08-06', 'close': 15.50, 'change_pct': 10.02},
            {'symbol': '002475', 'day': '2025-08-06', 'close': 45.20, 'change_pct': 10.01},
            {'symbol': '600000', 'day': '2025-08-05', 'close': 12.80, 'change_pct': 9.98},
            {'symbol': '300058', 'day': '2025-08-05', 'close': 8.90, 'change_pct': 10.05},
            {'symbol': '000001', 'day': '2025-08-04', 'close': 25.60, 'change_pct': 10.00},  # 无可转债
        ]
        
        print(f"📊 模拟涨停股票数据: {len(mock_limit_up_stocks)} 只")
        
        # 模拟查询结果处理
        stocks_with_bonds = []
        stocks_without_bonds = []
        total_bonds = 0
        
        for stock in mock_limit_up_stocks:
            stock_code = stock['symbol']
            
            if stock_code in query_tool.bond_stock_mapping:
                bonds = query_tool.bond_stock_mapping[stock_code]
                
                stock_info = {
                    'symbol': stock_code,
                    'latest_limit_up_date': stock['day'],
                    'latest_close': stock['close'],
                    'latest_change_pct': stock['change_pct'],
                    'limit_up_count': 1,
                    'limit_up_dates': [stock['day']],
                    'bonds': bonds
                }
                
                stocks_with_bonds.append(stock_info)
                total_bonds += len(bonds)
            else:
                stock_info = {
                    'symbol': stock_code,
                    'latest_limit_up_date': stock['day'],
                    'latest_close': stock['close'],
                    'latest_change_pct': stock['change_pct'],
                    'limit_up_count': 1
                }
                
                stocks_without_bonds.append(stock_info)
        
        # 构建结果
        result = {
            'summary': {
                'query_days': 10,
                'query_date_range': '2025-07-28 至 2025-08-06',
                'total_limit_up': len(mock_limit_up_stocks),
                'with_bonds': len(stocks_with_bonds),
                'without_bonds': len(stocks_without_bonds),
                'total_bonds': total_bonds
            },
            'stocks_with_bonds': stocks_with_bonds,
            'stocks_without_bonds': stocks_without_bonds
        }
        
        # 显示报告
        print(f"\n📋 生成模拟查询报告:")
        query_tool.display_limit_up_bonds_report(result)
        
        # 导出数据
        print(f"\n📁 导出模拟数据:")
        output_file = query_tool.export_limit_up_bonds(result, "模拟涨停转债查询结果.csv")
        
        if output_file:
            print(f"✅ 模拟测试成功！数据已导出到: {output_file}")
            return True
        else:
            print("❌ 导出失败")
            return False
            
    except Exception as e:
        print(f"❌ 模拟测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🚀 新版涨停正股转债查询测试")
    print("=" * 60)
    
    # 测试1: 映射关系加载
    success1 = test_bond_mapping_loading()
    
    # 测试2: 模拟涨停查询
    success2 = test_mock_limit_up_query()
    
    print(f"\n{'='*60}")
    print("📊 测试结果汇总")
    print(f"{'='*60}")
    print(f"映射关系加载: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"模拟涨停查询: {'✅ 成功' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print(f"\n🎉 所有测试通过！")
        print(f"💡 新版涨停查询系统已就绪，包含完整的实时行情数据")
        print(f"🔥 现在可以显示可转债的实时价格、涨跌幅、成交量等信息")
    else:
        print(f"\n❌ 部分测试失败，请检查配置")


if __name__ == "__main__":
    main()
