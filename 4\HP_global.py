"""
HP_global.py - 小白量化全局模块替代实现
提供全局变量和通用函数
"""
import time
from datetime import datetime
import pickle
import os

# 全局变量
CLOSE = None
LOW = None
HIGH = None
OPEN = None
VOL = None
AMOUNT = None

# 简化版本的全局变量别名
C = None
L = None
H = None
O = None
V = None
AMO = None

def MACHINETIME():
    """
    获取机器时间，格式为HHMMSS
    例如：93000 表示 9:30:00
    """
    now = datetime.now()
    return now.hour * 10000 + now.minute * 100 + now.second

def savem(filename, data):
    """
    保存数据到文件
    
    Args:
        filename: 文件名
        data: 要保存的数据
    """
    try:
        with open(filename, 'wb') as f:
            pickle.dump(data, f)
        return True
    except Exception as e:
        print(f"保存文件失败: {e}")
        return False

def loadm(filename):
    """
    从文件加载数据
    
    Args:
        filename: 文件名
        
    Returns:
        加载的数据，如果失败返回None
    """
    try:
        if os.path.exists(filename):
            with open(filename, 'rb') as f:
                return pickle.load(f)
        else:
            print(f"文件不存在: {filename}")
            return None
    except Exception as e:
        print(f"加载文件失败: {e}")
        return None

def get_current_time_str():
    """获取当前时间字符串"""
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

def is_trading_time():
    """
    判断是否在交易时间内
    
    Returns:
        bool: True表示在交易时间内
    """
    now = datetime.now()
    current_time = now.time()
    weekday = now.weekday()
    
    # 周末不交易
    if weekday >= 5:
        return False
    
    # 交易时间: 9:30-11:30, 13:00-15:00
    morning_start = datetime.strptime("09:30", "%H:%M").time()
    morning_end = datetime.strptime("11:30", "%H:%M").time()
    afternoon_start = datetime.strptime("13:00", "%H:%M").time()
    afternoon_end = datetime.strptime("15:00", "%H:%M").time()
    
    return (morning_start <= current_time <= morning_end) or \
           (afternoon_start <= current_time <= afternoon_end)

def log_message(message, level="INFO"):
    """
    记录日志消息
    
    Args:
        message: 日志消息
        level: 日志级别
    """
    timestamp = get_current_time_str()
    print(f"[{timestamp}] [{level}] {message}")

# 初始化函数
def init_globals():
    """初始化全局变量"""
    global CLOSE, LOW, HIGH, OPEN, VOL, AMOUNT
    global C, L, H, O, V, AMO
    
    CLOSE = LOW = HIGH = OPEN = VOL = AMOUNT = None
    C = L = H = O = V = AMO = None

# 模块初始化
init_globals()

if __name__ == "__main__":
    # 测试代码
    print("HP_global 模块测试")
    print(f"当前时间: {get_current_time_str()}")
    print(f"机器时间: {MACHINETIME()}")
    print(f"是否交易时间: {is_trading_time()}")
    
    # 测试保存和加载
    test_data = [1, 2, 3, "test"]
    if savem("test.dat", test_data):
        loaded_data = loadm("test.dat")
        print(f"保存和加载测试: {loaded_data}")
        
        # 清理测试文件
        if os.path.exists("test.dat"):
            os.remove("test.dat")
