# API 服务配置，参考 CSDN 文章
CFG = {
    # 同花顺 xiadan.exe 路径
    'exe_path': 'D:\\同花顺软件\\同花顺\\xiadan.exe',

    # 工作队列与日志
    'activework_path': './5/work_queue/ActiveWork.csv',
    'activework_field': [
        'key', 'strategy_no', 'stock_no', 'stock_name',
        'amount', 'operate', 'price', 'status'
    ],

    'workdatalog_path': './5/Work_Data_Log.csv',
    'workdata_field': [
        'key', '委托时间', '证券代码', '证券名称', '操作', '备注',
        '委托数量', '成交数量', '委托价格', '成交均价',
        '撤消数量', '合同编号', '策略编号'
    ],

    # 时序节流
    'sleepA': 0.2,
    'sleepB': 0.5,
    'sleepC': 1.0,

    # 端口
    'port': 6003,
}

