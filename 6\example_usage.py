#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
题材数据爬虫使用示例
"""

from plate_data_crawler import PlateDataCrawler
from config import POPULAR_PLATES
import json
import time


def example_single_plate():
    """示例1: 爬取单个题材数据"""
    print("=" * 60)
    print("示例1: 爬取单个题材数据")
    print("=" * 60)
    
    crawler = PlateDataCrawler()
    
    # 爬取汽车芯片题材
    plate_name = "汽车芯片"
    stocks = crawler.crawl_plate_data(plate_name)
    
    if stocks:
        print(f"✅ 成功获取 {plate_name} 题材数据")
        print(f"📊 共 {len(stocks)} 只股票")
        
        # 显示前5只股票信息
        print("\n前5只股票信息:")
        for i, stock in enumerate(stocks[:5], 1):
            print(f"{i}. {stock}")
    else:
        print(f"❌ 获取 {plate_name} 题材数据失败")


def example_multiple_plates():
    """示例2: 批量爬取多个题材数据"""
    print("\n" + "=" * 60)
    print("示例2: 批量爬取多个题材数据")
    print("=" * 60)
    
    crawler = PlateDataCrawler()
    
    # 选择要爬取的题材
    target_plates = [
        "汽车芯片",
        "人工智能", 
        "新能源汽车",
        "半导体",
        "5G概念"
    ]
    
    results = {}
    
    for plate_name in target_plates:
        print(f"\n🔄 正在爬取: {plate_name}")
        
        stocks = crawler.crawl_plate_data(plate_name)
        
        if stocks:
            results[plate_name] = stocks
            print(f"✅ {plate_name}: {len(stocks)} 只股票")
        else:
            print(f"❌ {plate_name}: 获取失败")
        
        # 避免请求过于频繁
        time.sleep(2)
    
    # 汇总结果
    print(f"\n📈 爬取汇总:")
    total_stocks = 0
    for plate_name, stocks in results.items():
        count = len(stocks)
        total_stocks += count
        print(f"  {plate_name}: {count} 只股票")
    
    print(f"\n🎯 总计: {total_stocks} 只股票，{len(results)} 个题材")


def example_custom_params():
    """示例3: 使用自定义参数"""
    print("\n" + "=" * 60)
    print("示例3: 使用自定义参数")
    print("=" * 60)
    
    crawler = PlateDataCrawler()
    
    # 使用自定义参数获取数据
    custom_uid = "681b0a4992d4437dd4e7e936"
    custom_zoom_str = "5TUD1oCMGx4tV7gmQEEhKkG34MNUwa9m"
    
    raw_data = crawler.get_plate_stocks(
        plate_name="光伏概念",
        uid=custom_uid,
        zoom_str=custom_zoom_str,
        vip_level=0
    )
    
    if raw_data:
        print("✅ 成功获取原始数据")
        print(f"📄 原始数据结构: {type(raw_data)}")
        
        # 解析数据
        stocks = crawler.parse_stock_data(raw_data)
        print(f"📊 解析得到 {len(stocks)} 只股票")
        
        # 保存数据
        filename = crawler.save_to_csv(stocks, "光伏概念")
        if filename:
            print(f"💾 数据已保存到: {filename}")
    else:
        print("❌ 获取数据失败")


def example_data_analysis():
    """示例4: 简单数据分析"""
    print("\n" + "=" * 60)
    print("示例4: 简单数据分析")
    print("=" * 60)
    
    crawler = PlateDataCrawler()
    
    # 获取数据
    stocks = crawler.crawl_plate_data("锂电池", save_csv=False)
    
    if not stocks:
        print("❌ 获取数据失败")
        return
    
    print(f"📊 锂电池题材分析 (共{len(stocks)}只股票)")
    
    # 分析数据结构
    if stocks:
        print(f"\n📋 数据字段:")
        sample_stock = stocks[0]
        for key, value in sample_stock.items():
            print(f"  {key}: {type(value).__name__} = {value}")
    
    # 如果有价格相关字段，进行简单统计
    # 注意：这里需要根据实际API返回的字段进行调整
    print(f"\n📈 基本统计:")
    print(f"  股票总数: {len(stocks)}")
    
    # 保存分析结果
    analysis_result = {
        "plate_name": "锂电池",
        "total_stocks": len(stocks),
        "analysis_time": time.strftime("%Y-%m-%d %H:%M:%S"),
        "sample_data": stocks[:3] if len(stocks) >= 3 else stocks
    }
    
    with open("锂电池_分析结果.json", "w", encoding="utf-8") as f:
        json.dump(analysis_result, f, ensure_ascii=False, indent=2)
    
    print("💾 分析结果已保存到: 锂电池_分析结果.json")


def example_error_handling():
    """示例5: 错误处理"""
    print("\n" + "=" * 60)
    print("示例5: 错误处理示例")
    print("=" * 60)
    
    crawler = PlateDataCrawler()
    
    # 测试不存在的题材
    invalid_plates = ["不存在的题材", "测试题材123"]
    
    for plate_name in invalid_plates:
        print(f"\n🔄 测试题材: {plate_name}")
        
        stocks = crawler.crawl_plate_data(plate_name)
        
        if stocks:
            print(f"✅ 意外成功: {len(stocks)} 只股票")
        else:
            print(f"❌ 预期失败: 题材不存在或网络错误")


def main():
    """主函数"""
    print("🚀 题材数据爬虫使用示例")
    print("=" * 60)
    
    try:
        # 运行各个示例
        example_single_plate()
        example_multiple_plates() 
        example_custom_params()
        example_data_analysis()
        example_error_handling()
        
        print("\n" + "=" * 60)
        print("🎉 所有示例运行完成!")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")


if __name__ == "__main__":
    main()
