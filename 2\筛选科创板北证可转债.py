#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
筛选科创板和北证股票对应的可转债

从涨停转债查询结果中筛选出科创板（68开头）和北证（8开头）股票对应的可转债
"""

import pandas as pd
from datetime import datetime


def filter_kechuang_beice_bonds():
    """筛选科创板和北证股票对应的可转债"""
    
    print("🔍 筛选科创板和北证股票对应的可转债")
    print("=" * 60)
    
    try:
        # 读取涨停转债查询结果
        df = pd.read_csv("涨停正股转债列表_20250807_010357.csv", encoding='utf-8-sig')
        print(f"✓ 读取数据: {len(df)} 条记录")
        
        # 筛选科创板股票（68开头）
        kechuang_df = df[df['stock_symbol'].astype(str).str.startswith('68')].copy()
        print(f"✓ 科创板股票: {len(kechuang_df)} 条")
        
        # 筛选北证股票（8开头，但排除68开头）
        beice_df = df[
            df['stock_symbol'].astype(str).str.startswith('8') & 
            ~df['stock_symbol'].astype(str).str.startswith('68')
        ].copy()
        print(f"✓ 北证股票: {len(beice_df)} 条")
        
        # 合并数据
        combined_df = pd.concat([kechuang_df, beice_df], ignore_index=True)
        print(f"✓ 合计: {len(combined_df)} 条科创板+北证可转债记录")
        
        if combined_df.empty:
            print("⚠️  没有找到科创板或北证股票对应的可转债")
            return
        
        # 添加市场标识
        combined_df['市场类型'] = combined_df['stock_symbol'].astype(str).apply(
            lambda x: '科创板' if x.startswith('68') else '北证' if x.startswith('8') else '其他'
        )
        
        # 显示详细信息
        display_detailed_info(combined_df)
        
        # 导出筛选结果
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"科创板北证可转债_{timestamp}.csv"
        combined_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"\n📁 筛选结果已导出: {output_file}")
        
        return combined_df
        
    except Exception as e:
        print(f"❌ 筛选失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def display_detailed_info(df):
    """显示详细信息"""
    
    print(f"\n{'='*80}")
    print("科创板和北证股票对应的可转债详细信息")
    print(f"{'='*80}")
    
    # 按市场类型分组统计
    market_stats = df['市场类型'].value_counts()
    print(f"📊 市场分布:")
    for market, count in market_stats.items():
        print(f"  {market}: {count} 只")
    
    # 显示科创板可转债
    kechuang_bonds = df[df['市场类型'] == '科创板']
    if not kechuang_bonds.empty:
        print(f"\n🚀 科创板股票对应的可转债 ({len(kechuang_bonds)} 只):")
        print("-" * 80)
        
        for i, (_, row) in enumerate(kechuang_bonds.iterrows(), 1):
            stock_symbol = row['stock_symbol']
            stock_date = row['latest_limit_up_date']
            stock_close = row['latest_close']
            stock_change = row['latest_change_pct']
            
            bond_code = row['bond_code']
            bond_name = row['bond_name']
            bond_market = row['bond_market']
            bond_price = row.get('bond_current_price', '')
            bond_change = row.get('bond_change_rate', '')
            bond_rating = row.get('bond_rating', '')
            has_realtime = row.get('has_realtime_data', False)
            
            print(f"{i:2d}. 正股: {stock_symbol} - {stock_date}")
            print(f"    收盘: {stock_close:.2f} 元, 涨幅: {stock_change:.2f}%")
            
            # 可转债信息
            realtime_str = " 🔥" if has_realtime else ""
            if bond_price and str(bond_price) not in ['', 'nan', '--']:
                try:
                    price_val = float(bond_price)
                    price_info = f"价格: {price_val:.2f}"
                    if bond_change and str(bond_change) not in ['', 'nan', '--']:
                        change_val = float(bond_change)
                        price_info += f", 涨幅: {change_val:.2f}%"
                except:
                    price_info = "价格: --"
            else:
                price_info = "价格: --"
            
            rating_str = f", 评级: {bond_rating}" if bond_rating and bond_rating not in ['', 'nan'] else ""
            
            print(f"    可转债: {bond_code} {bond_name} ({bond_market})")
            print(f"    {price_info}{rating_str}{realtime_str}")
            print()
    
    # 显示北证可转债
    beice_bonds = df[df['市场类型'] == '北证']
    if not beice_bonds.empty:
        print(f"\n🏛️  北证股票对应的可转债 ({len(beice_bonds)} 只):")
        print("-" * 80)
        
        for i, (_, row) in enumerate(beice_bonds.iterrows(), 1):
            stock_symbol = row['stock_symbol']
            stock_date = row['latest_limit_up_date']
            stock_close = row['latest_close']
            stock_change = row['latest_change_pct']
            
            bond_code = row['bond_code']
            bond_name = row['bond_name']
            bond_market = row['bond_market']
            bond_price = row.get('bond_current_price', '')
            bond_change = row.get('bond_change_rate', '')
            bond_rating = row.get('bond_rating', '')
            has_realtime = row.get('has_realtime_data', False)
            
            print(f"{i:2d}. 正股: {stock_symbol} - {stock_date}")
            print(f"    收盘: {stock_close:.2f} 元, 涨幅: {stock_change:.2f}%")
            
            # 可转债信息
            realtime_str = " 🔥" if has_realtime else ""
            if bond_price and str(bond_price) not in ['', 'nan', '--']:
                try:
                    price_val = float(bond_price)
                    price_info = f"价格: {price_val:.2f}"
                    if bond_change and str(bond_change) not in ['', 'nan', '--']:
                        change_val = float(bond_change)
                        price_info += f", 涨幅: {change_val:.2f}%"
                except:
                    price_info = "价格: --"
            else:
                price_info = "价格: --"
            
            rating_str = f", 评级: {bond_rating}" if bond_rating and bond_rating not in ['', 'nan'] else ""
            
            print(f"    可转债: {bond_code} {bond_name} ({bond_market})")
            print(f"    {price_info}{rating_str}{realtime_str}")
            print()
    
    # 统计有实时数据的可转债
    realtime_count = len(df[df.get('has_realtime_data', False) == True])
    print(f"📈 统计信息:")
    print(f"  总可转债数量: {len(df)} 只")
    print(f"  有实时数据: {realtime_count} 只")
    print(f"  实时数据比例: {realtime_count/len(df)*100:.1f}%")


def analyze_kechuang_beice_bonds():
    """分析科创板北证可转债的特点"""
    
    print(f"\n{'='*80}")
    print("科创板北证可转债分析")
    print(f"{'='*80}")
    
    try:
        # 读取完整的可转债数据
        full_df = pd.read_csv("东方财富终极完整可转债_20250807_004218.csv", encoding='utf-8-sig')
        
        # 筛选科创板和北证的可转债
        kechuang_all = full_df[
            (full_df['正股代码'].astype(str).str.startswith('68')) & 
            (full_df['状态'] == '正常')
        ]
        
        beice_all = full_df[
            (full_df['正股代码'].astype(str).str.startswith('8')) & 
            (~full_df['正股代码'].astype(str).str.startswith('68')) &
            (full_df['状态'] == '正常')
        ]
        
        print(f"📊 全市场科创板北证可转债统计:")
        print(f"  科创板可转债总数: {len(kechuang_all)} 只")
        print(f"  北证可转债总数: {len(beice_all)} 只")
        
        # 显示科创板可转债列表
        if not kechuang_all.empty:
            print(f"\n🚀 全部科创板可转债列表:")
            for i, (_, row) in enumerate(kechuang_all.iterrows(), 1):
                bond_code = row['债券代码']
                bond_name = row['债券简称']
                stock_code = row['正股代码']
                stock_name = row['正股简称']
                rating = row.get('信用评级', '')
                price = row.get('债现价', '')
                
                price_str = f" 价格:{price}" if price and str(price) not in ['', 'nan'] else ""
                rating_str = f" 评级:{rating}" if rating and rating not in ['', 'nan'] else ""
                
                print(f"  {i:2d}. {bond_code} {bond_name} → {stock_code} {stock_name}{price_str}{rating_str}")
        
        # 显示北证可转债列表
        if not beice_all.empty:
            print(f"\n🏛️  全部北证可转债列表:")
            for i, (_, row) in enumerate(beice_all.iterrows(), 1):
                bond_code = row['债券代码']
                bond_name = row['债券简称']
                stock_code = row['正股代码']
                stock_name = row['正股简称']
                rating = row.get('信用评级', '')
                price = row.get('债现价', '')
                
                price_str = f" 价格:{price}" if price and str(price) not in ['', 'nan'] else ""
                rating_str = f" 评级:{rating}" if rating and rating not in ['', 'nan'] else ""
                
                print(f"  {i:2d}. {bond_code} {bond_name} → {stock_code} {stock_name}{price_str}{rating_str}")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")


def main():
    """主函数"""
    print("🚀 科创板北证可转债筛选工具")
    print("=" * 60)
    
    # 1. 从涨停数据中筛选
    result_df = filter_kechuang_beice_bonds()
    
    # 2. 分析全市场科创板北证可转债
    analyze_kechuang_beice_bonds()
    
    if result_df is not None and not result_df.empty:
        print(f"\n✅ 筛选完成！")
        print(f"💡 找到 {len(result_df)} 只科创板/北证股票对应的可转债")
        print(f"🔥 这些都是最近有涨停的科创板/北证股票对应的可转债投资机会")
    else:
        print(f"\n⚠️  未找到科创板/北证涨停股票对应的可转债")


if __name__ == "__main__":
    main()
