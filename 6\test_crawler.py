#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
题材数据爬虫测试脚本
"""

from plate_data_crawler import PlateDataCrawler
import json


def test_single_request():
    """测试单个请求"""
    print("🧪 测试单个请求...")
    
    crawler = PlateDataCrawler()
    
    # 测试获取汽车芯片数据
    raw_data = crawler.get_plate_stocks("汽车芯片")
    
    if raw_data:
        print("✅ 请求成功")
        print(f"📄 响应数据类型: {type(raw_data)}")
        
        # 保存原始响应数据用于分析
        with open("raw_response.json", "w", encoding="utf-8") as f:
            json.dump(raw_data, f, ensure_ascii=False, indent=2)
        print("💾 原始响应已保存到 raw_response.json")
        
        # 尝试解析数据
        stocks = crawler.parse_stock_data(raw_data)
        print(f"📊 解析得到 {len(stocks)} 条股票数据")
        
        if stocks:
            print("📋 第一条股票数据:")
            print(json.dumps(stocks[0], ensure_ascii=False, indent=2))
        
        return True
    else:
        print("❌ 请求失败")
        return False


def test_data_parsing():
    """测试数据解析"""
    print("\n🧪 测试数据解析...")
    
    # 模拟API响应数据进行测试
    mock_data = {
        "code": 200,
        "message": "success",
        "data": [
            {
                "stock_code": "000001",
                "stock_name": "平安银行",
                "current_price": 10.50,
                "change_percent": 2.5
            },
            {
                "stock_code": "000002", 
                "stock_name": "万科A",
                "current_price": 15.20,
                "change_percent": -1.2
            }
        ]
    }
    
    crawler = PlateDataCrawler()
    stocks = crawler.parse_stock_data(mock_data)
    
    print(f"📊 模拟数据解析结果: {len(stocks)} 条记录")
    for stock in stocks:
        print(f"  {stock}")


def test_csv_saving():
    """测试CSV保存功能"""
    print("\n🧪 测试CSV保存...")
    
    # 模拟股票数据
    mock_stocks = [
        {
            "股票代码": "000001",
            "股票名称": "平安银行", 
            "当前价格": 10.50,
            "涨跌幅": 2.5
        },
        {
            "股票代码": "000002",
            "股票名称": "万科A",
            "当前价格": 15.20, 
            "涨跌幅": -1.2
        }
    ]
    
    crawler = PlateDataCrawler()
    filename = crawler.save_to_csv(mock_stocks, "测试题材")
    
    if filename:
        print(f"✅ CSV保存成功: {filename}")
    else:
        print("❌ CSV保存失败")


def test_complete_workflow():
    """测试完整工作流程"""
    print("\n🧪 测试完整工作流程...")
    
    crawler = PlateDataCrawler()
    
    # 测试完整流程
    stocks = crawler.crawl_plate_data("汽车芯片")
    
    if stocks:
        print(f"✅ 完整流程测试成功")
        print(f"📊 获取到 {len(stocks)} 条股票数据")
        return True
    else:
        print("❌ 完整流程测试失败")
        return False


def main():
    """主测试函数"""
    print("🚀 题材数据爬虫测试")
    print("=" * 50)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("单个请求测试", test_single_request()))
    test_data_parsing()
    test_csv_saving() 
    test_results.append(("完整流程测试", test_complete_workflow()))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📋 测试结果汇总:")
    print("=" * 50)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    print(f"\n🎯 总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过!")
    else:
        print("⚠️  部分测试失败，请检查网络连接和API参数")


if __name__ == "__main__":
    main()
