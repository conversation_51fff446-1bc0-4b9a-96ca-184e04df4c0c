"""
贵州茅台(600519)专用查询脚本
自动打开同花顺并查询茅台股票信息
"""
import pyautogui
import time
import tkinter as tk
import subprocess
from datetime import datetime

class MaotaiStockQuery:
    def __init__(self):
        self.软件路径 = r'D:\同花顺软件\同花顺\hexin.exe'
        self.股票代码 = '600519'
        self.股票名称 = '贵州茅台'
        self.等待时间 = 2
        
    def log(self, message):
        """打印带时间戳的日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
    
    def get_screen_info(self):
        """获取屏幕信息"""
        root = tk.Tk()
        screenwidth = root.winfo_screenwidth()
        screenheight = root.winfo_screenheight()
        root.destroy()
        self.log(f"屏幕分辨率: {screenwidth} x {screenheight}")
        return screenwidth, screenheight
    
    def start_app(self):
        """启动同花顺应用"""
        self.log("正在启动同花顺...")
        try:
            subprocess.Popen(self.软件路径)
            self.log("同花顺启动成功")
            return True
        except Exception as e:
            self.log(f"启动失败: {e}")
            return False
    
    def safe_click(self, x, y, wait_before=1, wait_after=1):
        """安全点击（带等待时间）"""
        time.sleep(wait_before)
        pyautogui.click(x=x, y=y)
        self.log(f"点击坐标: ({x}, {y})")
        time.sleep(wait_after)
    
    def safe_key(self, key, wait_before=0.5, wait_after=1):
        """安全按键（带等待时间）"""
        time.sleep(wait_before)
        pyautogui.press(key)
        self.log(f"按键: {key}")
        time.sleep(wait_after)
    
    def input_stock_code(self):
        """输入茅台股票代码"""
        self.log(f"输入股票代码: {self.股票代码} ({self.股票名称})")
        
        # 清空输入框（如果有内容）
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(0.2)
        
        # 输入股票代码
        pyautogui.write(self.股票代码)
        time.sleep(0.5)
        
        # 按回车确认
        pyautogui.press('enter')
        self.log("股票代码输入完成")
    
    def query_maotai(self):
        """查询茅台股票的完整流程"""
        self.log("=== 开始查询贵州茅台股票信息 ===")
        
        # 1. 获取屏幕信息
        self.get_screen_info()
        
        # 2. 启动同花顺
        if not self.start_app():
            return False
        
        # 3. 等待软件完全启动
        self.log("等待软件启动...")
        time.sleep(5)
        
        # 4. 处理可能的启动对话框
        self.log("处理启动界面...")
        self.safe_key('enter', wait_before=1, wait_after=2)
        
        # 5. 点击主界面（激活窗口）
        self.log("激活主窗口...")
        self.safe_click(x=80, y=80, wait_before=1, wait_after=2)
        
        # 6. 再次确认
        self.safe_key('enter', wait_after=2)
        
        # 7. 输入茅台股票代码
        self.input_stock_code()
        time.sleep(2)
        
        # 8. 如果需要，再次输入确认
        self.log("确认股票代码...")
        self.input_stock_code()
        time.sleep(2)
        
        # 9. 打开交易界面（F12）
        self.log("打开交易界面...")
        self.safe_key('f12', wait_after=3)
        
        self.log("=== 茅台股票查询完成 ===")
        return True
    
    def run(self):
        """运行主程序"""
        try:
            self.query_maotai()
        except KeyboardInterrupt:
            self.log("程序被用户中断")
        except Exception as e:
            self.log(f"程序执行出错: {e}")

def main():
    """主函数"""
    print("贵州茅台(600519)股票查询工具")
    print("=" * 40)
    print("功能：自动打开同花顺并查询茅台股票")
    print("注意：请确保同花顺软件已关闭")
    print("按Ctrl+C可随时停止")
    print("=" * 40)
    
    # 确认执行
    confirm = input("是否开始执行？(y/n): ").lower().strip()
    if confirm != 'y':
        print("已取消执行")
        return
    
    # 创建查询实例并运行
    query = MaotaiStockQuery()
    query.run()

if __name__ == "__main__":
    main()
