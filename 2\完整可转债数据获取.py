#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
完整可转债数据获取工具

获取可转债的完整数据，包括基本信息、行情数据、技术指标等
"""

import pandas as pd
import ast
import json
from datetime import datetime
from typing import Dict, List, Optional

try:
    from mootdx.quotes import Quotes
    MOOTDX_AVAILABLE = True
except ImportError:
    print("警告: 无法导入 mootdx 库")
    MOOTDX_AVAILABLE = False


class CompleteBondDataFetcher:
    """完整可转债数据获取器"""
    
    def __init__(self):
        """初始化"""
        self.client = None
        self.connected = False
        
        if MOOTDX_AVAILABLE:
            self._connect()
    
    def _connect(self):
        """连接数据服务器"""
        try:
            print("正在连接数据服务器...")
            self.client = Quotes.factory(market='std', timeout=30)
            self.connected = True
            print("✓ 连接成功")
        except Exception as e:
            print(f"✗ 连接失败: {e}")
            self.connected = False
    
    def parse_raw_data(self, raw_data_str: str) -> Dict:
        """
        解析原始数据字符串
        
        Args:
            raw_data_str: 原始数据字符串
            
        Returns:
            Dict: 解析后的数据
        """
        try:
            if pd.isna(raw_data_str) or raw_data_str == '':
                return {}
            
            # 尝试用 ast.literal_eval 解析
            return ast.literal_eval(raw_data_str)
        except:
            try:
                # 尝试用 json 解析
                return json.loads(raw_data_str)
            except:
                return {}
    
    def fix_bond_prices(self, csv_file: str) -> pd.DataFrame:
        """
        修复可转债价格数据
        
        Args:
            csv_file: CSV文件路径
            
        Returns:
            pd.DataFrame: 修复后的数据
        """
        try:
            # 读取原始数据
            df = pd.read_csv(csv_file, encoding='utf-8-sig')
            print(f"读取到 {len(df)} 条记录")
            
            # 读取完整原始数据
            original_df = pd.read_csv("沪深可转债列表_20250806_225258.csv", encoding='utf-8-sig')
            
            fixed_bonds = []
            
            for _, row in df.iterrows():
                bond_code = row['bond_code']
                bond_name = row['bond_name']
                exchange = row['exchange']
                
                # 从原始数据中找到对应记录
                original_row = original_df[original_df['bond_code'] == bond_code]
                
                if not original_row.empty:
                    raw_data_str = original_row.iloc[0]['raw_data']
                    raw_data = self.parse_raw_data(raw_data_str)
                    
                    fixed_bonds.append({
                        'bond_code': bond_code,
                        'bond_name': bond_name,
                        'exchange': exchange,
                        'pre_close': raw_data.get('pre_close', 0),
                        'volunit': raw_data.get('volunit', 0),
                        'decimal_point': raw_data.get('decimal_point', 0),
                        'raw_data': raw_data
                    })
                else:
                    # 如果找不到原始数据，保持原样
                    fixed_bonds.append({
                        'bond_code': bond_code,
                        'bond_name': bond_name,
                        'exchange': exchange,
                        'pre_close': 0,
                        'volunit': 0,
                        'decimal_point': 0,
                        'raw_data': {}
                    })
            
            result_df = pd.DataFrame(fixed_bonds)
            print(f"修复了 {len(result_df)} 条记录的价格数据")
            
            return result_df
        
        except Exception as e:
            print(f"修复价格数据失败: {e}")
            return pd.DataFrame()
    
    def get_enhanced_bond_data(self, bond_code: str) -> Dict:
        """
        获取增强的可转债数据
        
        Args:
            bond_code: 可转债代码
            
        Returns:
            Dict: 增强数据
        """
        if not self.connected:
            return {'error': '未连接到数据服务器'}
        
        try:
            # 判断市场
            if bond_code.startswith(('110', '113', '118')):
                market = 1  # 上海
            elif bond_code.startswith(('123', '127', '128')):
                market = 0  # 深圳
            else:
                return {'error': f'无效的可转债代码: {bond_code}'}
            
            result = {
                'bond_code': bond_code,
                'market': '上海' if market == 1 else '深圳',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # 1. 获取实时行情
            try:
                quotes = self.client.quotes(symbol=bond_code, market=market)
                if not quotes.empty:
                    quote_data = quotes.iloc[0].to_dict()
                    result['quotes'] = {
                        'price': quote_data.get('price', 0),           # 当前价格
                        'open': quote_data.get('open', 0),             # 开盘价
                        'high': quote_data.get('high', 0),             # 最高价
                        'low': quote_data.get('low', 0),               # 最低价
                        'pre_close': quote_data.get('pre_close', 0),   # 昨收价
                        'volume': quote_data.get('vol', 0),            # 成交量
                        'amount': quote_data.get('amount', 0),         # 成交额
                        'bid1': quote_data.get('bid1', 0),             # 买一价
                        'ask1': quote_data.get('ask1', 0),             # 卖一价
                        'bid1_vol': quote_data.get('bid1_vol', 0),     # 买一量
                        'ask1_vol': quote_data.get('ask1_vol', 0),     # 卖一量
                        'change': quote_data.get('price', 0) - quote_data.get('pre_close', 0),  # 涨跌额
                        'change_pct': ((quote_data.get('price', 0) - quote_data.get('pre_close', 0)) / quote_data.get('pre_close', 1)) * 100 if quote_data.get('pre_close', 0) > 0 else 0  # 涨跌幅
                    }
            except Exception as e:
                result['quotes_error'] = str(e)
            
            # 2. 获取K线数据（最近5天）
            try:
                kline = self.client.bars(symbol=bond_code, market=market, frequency=9, offset=5)
                if not kline.empty:
                    result['kline'] = kline.to_dict('records')
            except Exception as e:
                result['kline_error'] = str(e)
            
            # 3. 获取分时数据
            try:
                minute_data = self.client.minute(symbol=bond_code, market=market)
                if not minute_data.empty:
                    result['minute_data'] = {
                        'count': len(minute_data),
                        'latest_10': minute_data.tail(10).to_dict('records')
                    }
            except Exception as e:
                result['minute_error'] = str(e)
            
            # 4. 获取财务数据（如果有）
            try:
                finance = self.client.finance(symbol=bond_code, market=market)
                if not finance.empty:
                    result['finance'] = finance.iloc[0].to_dict()
            except Exception as e:
                result['finance_error'] = str(e)
            
            return result
        
        except Exception as e:
            return {'error': f'获取数据失败: {str(e)}'}
    
    def get_available_data_types(self) -> Dict:
        """
        获取可用的数据类型说明
        
        Returns:
            Dict: 数据类型说明
        """
        return {
            'basic_info': {
                'description': '基本信息',
                'fields': [
                    'bond_code - 可转债代码',
                    'bond_name - 可转债名称', 
                    'exchange - 交易所',
                    'market - 市场代码',
                    'volunit - 交易单位',
                    'decimal_point - 小数位数'
                ]
            },
            'quotes': {
                'description': '实时行情',
                'fields': [
                    'price - 当前价格',
                    'open - 开盘价',
                    'high - 最高价',
                    'low - 最低价',
                    'pre_close - 昨收价',
                    'volume - 成交量',
                    'amount - 成交额',
                    'bid1/ask1 - 买一/卖一价',
                    'bid1_vol/ask1_vol - 买一/卖一量',
                    'change - 涨跌额',
                    'change_pct - 涨跌幅(%)'
                ]
            },
            'kline': {
                'description': 'K线数据',
                'fields': [
                    'datetime - 时间',
                    'open - 开盘价',
                    'high - 最高价', 
                    'low - 最低价',
                    'close - 收盘价',
                    'volume - 成交量',
                    'amount - 成交额'
                ]
            },
            'minute_data': {
                'description': '分时数据',
                'fields': [
                    'datetime - 时间',
                    'price - 价格',
                    'volume - 成交量',
                    'amount - 成交额'
                ]
            },
            'finance': {
                'description': '财务数据',
                'fields': [
                    'market_cap - 市值',
                    'pe_ratio - 市盈率',
                    'pb_ratio - 市净率',
                    'dividend_yield - 股息率'
                ]
            },
            'technical': {
                'description': '技术指标（可扩展）',
                'fields': [
                    'ma5/ma10/ma20 - 移动平均线',
                    'rsi - 相对强弱指标',
                    'macd - MACD指标',
                    'bollinger_bands - 布林带'
                ]
            }
        }
    
    def save_enhanced_data(self, data: pd.DataFrame, filename: str = None) -> str:
        """保存增强数据"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"完整可转债数据_{timestamp}.csv"
        
        try:
            data.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"✓ 完整数据已保存到: {filename}")
            return filename
        except Exception as e:
            print(f"✗ 保存失败: {e}")
            return ""
    
    def close(self):
        """关闭连接"""
        if self.client:
            try:
                self.client.close()
                print("已关闭连接")
            except:
                pass


def main():
    """主函数"""
    print("完整可转债数据获取工具")
    print("=" * 50)
    
    try:
        # 初始化工具
        fetcher = CompleteBondDataFetcher()
        
        # 1. 修复价格数据
        print("\n1. 修复可转债价格数据")
        print("-" * 30)
        fixed_data = fetcher.fix_bond_prices("真正股票可转债列表_20250806_232359.csv")
        
        if not fixed_data.empty:
            # 显示修复后的前10条数据
            print("\n修复后的前10条数据:")
            display_cols = ['bond_code', 'bond_name', 'exchange', 'pre_close']
            print(fixed_data[display_cols].head(10).to_string(index=False))
            
            # 保存修复后的数据
            output_file = fetcher.save_enhanced_data(fixed_data, "修复价格的可转债列表.csv")
        
        # 2. 展示可获取的数据类型
        print("\n2. 可获取的数据类型")
        print("-" * 30)
        data_types = fetcher.get_available_data_types()
        
        for category, info in data_types.items():
            print(f"\n【{info['description']}】")
            for field in info['fields']:
                print(f"  • {field}")
        
        # 3. 演示获取增强数据
        if fetcher.connected:
            print("\n3. 演示获取增强数据")
            print("-" * 30)
            
            # 测试几个可转债
            test_bonds = ['128136', '110060']  # 立讯转债、天路转债
            
            for bond_code in test_bonds:
                print(f"\n获取 {bond_code} 的完整数据:")
                enhanced_data = fetcher.get_enhanced_bond_data(bond_code)
                
                if 'error' not in enhanced_data:
                    print(f"  市场: {enhanced_data.get('market')}")
                    
                    if 'quotes' in enhanced_data:
                        quotes = enhanced_data['quotes']
                        print(f"  当前价格: {quotes.get('price', 'N/A')}")
                        print(f"  涨跌幅: {quotes.get('change_pct', 'N/A'):.2f}%")
                        print(f"  成交量: {quotes.get('volume', 'N/A')}")
                    
                    if 'kline' in enhanced_data:
                        print(f"  K线数据: {len(enhanced_data['kline'])} 条")
                    
                    if 'minute_data' in enhanced_data:
                        print(f"  分时数据: {enhanced_data['minute_data']['count']} 条")
                else:
                    print(f"  获取失败: {enhanced_data['error']}")
        
        # 关闭连接
        fetcher.close()
        
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
