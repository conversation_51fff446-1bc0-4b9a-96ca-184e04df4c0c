#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
可转债数据分析工具

分析爬取的东方财富可转债数据
"""

import pandas as pd
from datetime import datetime


def analyze_bond_data(csv_file):
    """分析可转债数据"""
    
    print("🔍 可转债数据分析")
    print("=" * 60)
    
    try:
        # 读取数据
        df = pd.read_csv(csv_file, encoding='utf-8-sig')
        print(f"✓ 读取数据: {len(df)} 条记录")
        
        # 基本统计
        print(f"\n📊 基本统计:")
        print(f"  总可转债数量: {len(df)}")
        
        # 状态分布
        if '状态' in df.columns:
            status_counts = df['状态'].value_counts()
            print(f"\n📈 状态分布:")
            for status, count in status_counts.items():
                percentage = count / len(df) * 100
                print(f"  {status}: {count} 只 ({percentage:.1f}%)")
        
        # 市场分布
        if '交易市场' in df.columns:
            market_counts = df['交易市场'].value_counts()
            print(f"\n🏢 市场分布:")
            for market, count in market_counts.items():
                percentage = count / len(df) * 100
                print(f"  {market}: {count} 只 ({percentage:.1f}%)")
        
        # 评级分布（正常状态的可转债）
        normal_bonds = df[df['状态'] == '正常'] if '状态' in df.columns else df
        if '评级' in normal_bonds.columns and not normal_bonds.empty:
            rating_counts = normal_bonds['评级'].value_counts()
            print(f"\n⭐ 评级分布 (正常状态可转债):")
            for rating, count in rating_counts.head(10).items():
                percentage = count / len(normal_bonds) * 100
                print(f"  {rating}: {count} 只 ({percentage:.1f}%)")
        
        # 代码前缀分布（正常状态的可转债）
        if '可转债代码' in normal_bonds.columns and not normal_bonds.empty:
            # 确保代码是字符串类型
            normal_bonds_copy = normal_bonds.copy()
            normal_bonds_copy['可转债代码'] = normal_bonds_copy['可转债代码'].astype(str)
            prefixes = normal_bonds_copy['可转债代码'].str[:3].value_counts()
            print(f"\n🔢 代码前缀分布 (正常状态可转债):")
            for prefix, count in prefixes.items():
                percentage = count / len(normal_bonds) * 100
                print(f"  {prefix}xxx: {count} 只 ({percentage:.1f}%)")
        
        # 正股市场分布（正常状态的可转债）
        if '正股代码' in normal_bonds.columns and not normal_bonds.empty:
            # 根据正股代码判断市场
            if 'normal_bonds_copy' not in locals():
                normal_bonds_copy = normal_bonds.copy()
            normal_bonds_copy['正股代码'] = normal_bonds_copy['正股代码'].astype(str)
            normal_bonds_copy['正股市场'] = normal_bonds_copy['正股代码'].apply(
                lambda x: '上海' if str(x).startswith(('60', '68', '11', '12', '13'))
                else '深圳' if str(x).startswith(('00', '30', '20'))
                else '未知'
            )
            
            stock_market_counts = normal_bonds_copy['正股市场'].value_counts()
            print(f"\n📈 正股市场分布 (正常状态可转债):")
            for market, count in stock_market_counts.items():
                percentage = count / len(normal_bonds) * 100
                print(f"  {market}: {count} 只 ({percentage:.1f}%)")
        
        # 显示一些具体的可转债信息
        print(f"\n🎯 部分正常状态可转债示例:")
        if not normal_bonds.empty:
            sample_cols = ['可转债代码', '可转债简称', '正股代码', '正股简称', '评级', '交易市场']
            available_cols = [col for col in sample_cols if col in normal_bonds.columns]
            
            if available_cols:
                sample_data = normal_bonds[available_cols].head(10)
                for i, (_, row) in enumerate(sample_data.iterrows(), 1):
                    bond_code = row.get('可转债代码', 'N/A')
                    bond_name = row.get('可转债简称', 'N/A')
                    stock_code = row.get('正股代码', 'N/A')
                    stock_name = row.get('正股简称', 'N/A')
                    rating = row.get('评级', 'N/A')
                    market = row.get('交易市场', 'N/A')
                    
                    print(f"  {i:2d}. {bond_code} {bond_name} → {stock_code} {stock_name} ({rating}, {market})")
        
        # 查找特定的可转债
        print(f"\n🔍 查找特定可转债:")
        target_bonds = ['110060', '128136', '123001', '110059', '113001']

        # 确保代码列是字符串类型
        df_copy = df.copy()
        df_copy['可转债代码'] = df_copy['可转债代码'].astype(str)

        for bond_code in target_bonds:
            bond_info = df_copy[df_copy['可转债代码'] == bond_code]
            if not bond_info.empty:
                info = bond_info.iloc[0]
                bond_name = info.get('可转债简称', 'N/A')
                stock_code = info.get('正股代码', 'N/A')
                stock_name = info.get('正股简称', 'N/A')
                rating = info.get('评级', 'N/A')
                status = info.get('状态', 'N/A')
                market = info.get('交易市场', 'N/A')
                
                print(f"  ✓ {bond_code} {bond_name} → {stock_code} {stock_name} ({rating}, {status}, {market})")
            else:
                print(f"  ✗ {bond_code} 未找到")
        
        # 保存正常状态的可转债
        if not normal_bonds.empty:
            output_file = f"正常状态可转债_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            normal_bonds.to_csv(output_file, index=False, encoding='utf-8-sig')
            print(f"\n📁 正常状态可转债已保存到: {output_file}")
            print(f"   包含 {len(normal_bonds)} 只正常交易的可转债")
        
        return {
            'total': len(df),
            'normal': len(normal_bonds),
            'delisted': len(df) - len(normal_bonds),
            'normal_data': normal_bonds
        }
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None


def main():
    """主函数"""
    # 分析最新的可转债数据
    csv_file = "东方财富可转债数据_20250807_003304.csv"
    
    result = analyze_bond_data(csv_file)
    
    if result:
        print(f"\n✅ 分析完成！")
        print(f"📊 数据概览:")
        print(f"  总数量: {result['total']} 只")
        print(f"  正常状态: {result['normal']} 只")
        print(f"  已退市: {result['delisted']} 只")
        print(f"  正常比例: {result['normal']/result['total']*100:.1f}%")
    else:
        print(f"❌ 分析失败")


if __name__ == "__main__":
    main()
