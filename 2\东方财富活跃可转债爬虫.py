#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
东方财富活跃可转债数据爬虫

专门爬取有交易数据的活跃可转债并导出CSV
"""

import requests
import pandas as pd
import json
import time
from datetime import datetime
from typing import Dict, List, Optional
import warnings
warnings.filterwarnings('ignore')


class ActiveBondSpider:
    """活跃可转债爬虫"""
    
    def __init__(self):
        """初始化爬虫"""
        self.session = requests.Session()
        
        # 设置请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://data.eastmoney.com/',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        }
        self.session.headers.update(self.headers)
    
    def get_active_bonds(self) -> List[Dict]:
        """
        获取活跃可转债数据
        
        Returns:
            List[Dict]: 活跃可转债数据
        """
        # 东方财富可转债行情API
        url = "https://push2.eastmoney.com/api/qt/clist/get"
        
        params = {
            'cb': 'jQuery112409748013994908473_1691234567890',
            'pn': '1',
            'pz': '1000',  # 获取1000条数据
            'po': '1',
            'np': '1',
            'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
            'fltt': '2',
            'invt': '2',
            'fid': 'f3',
            'fs': 'b:MK0354',  # 可转债板块
            'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152',
            '_': str(int(time.time() * 1000))
        }
        
        try:
            print("正在获取活跃可转债数据...")
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            # 提取JSON数据
            text = response.text
            start = text.find('(') + 1
            end = text.rfind(')')
            json_str = text[start:end]
            
            data = json.loads(json_str)
            
            if data.get('rc') == 0 and 'data' in data and 'diff' in data['data']:
                bonds_data = data['data']['diff']
                print(f"✓ 获取到 {len(bonds_data)} 只活跃可转债")
                return bonds_data
            else:
                print("✗ 未获取到可转债数据")
                return []
                
        except Exception as e:
            print(f"✗ 获取数据失败: {e}")
            return []
    
    def process_bond_data(self, raw_data: List[Dict]) -> pd.DataFrame:
        """
        处理可转债数据
        
        Args:
            raw_data: 原始数据
            
        Returns:
            pd.DataFrame: 处理后的数据
        """
        if not raw_data:
            return pd.DataFrame()
        
        print("正在处理可转债数据...")
        
        processed_data = []
        
        # 字段映射
        field_mapping = {
            'f12': '可转债代码',
            'f14': '可转债名称',
            'f2': '最新价',
            'f3': '涨跌幅',
            'f4': '涨跌额',
            'f5': '成交量',
            'f6': '成交额',
            'f7': '振幅',
            'f8': '换手率',
            'f9': '市盈率',
            'f10': '量比',
            'f15': '最高价',
            'f16': '最低价',
            'f17': '开盘价',
            'f18': '昨收价',
            'f20': '总市值',
            'f21': '流通市值',
            'f23': '市净率',
            'f24': '涨速',
            'f25': '5分钟涨跌',
            'f22': '涨停价',
            'f11': '跌停价',
            'f62': '主力净流入',
            'f128': '领涨股',
            'f136': '所属行业',
            'f115': '市场类型',
            'f152': '市场'
        }
        
        for item in raw_data:
            # 只处理有价格数据的可转债
            if item.get('f2') and item.get('f2') != '-':
                processed_item = {}
                
                for field_code, field_name in field_mapping.items():
                    value = item.get(field_code)
                    
                    # 数据清洗
                    if value is None or value == '-':
                        processed_item[field_name] = None
                    elif field_code in ['f2', 'f3', 'f4', 'f5', 'f6', 'f7', 'f8', 'f9', 'f10', 
                                      'f15', 'f16', 'f17', 'f18', 'f20', 'f21', 'f23', 'f24', 'f25', 'f22', 'f11', 'f62']:
                        # 数值字段
                        try:
                            processed_item[field_name] = float(value) if value not in [None, '-', ''] else None
                        except (ValueError, TypeError):
                            processed_item[field_name] = None
                    else:
                        processed_item[field_name] = str(value) if value else None
                
                # 添加额外信息
                processed_item['数据获取时间'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                
                # 判断市场
                code = processed_item.get('可转债代码', '')
                if code.startswith(('110', '113', '118')):
                    processed_item['交易市场'] = '上海'
                elif code.startswith(('123', '127', '128')):
                    processed_item['交易市场'] = '深圳'
                else:
                    processed_item['交易市场'] = '未知'
                
                processed_data.append(processed_item)
        
        df = pd.DataFrame(processed_data)
        print(f"✓ 处理完成，共 {len(df)} 只活跃可转债")
        
        return df
    
    def save_to_csv(self, df: pd.DataFrame, filename: str = None) -> str:
        """保存数据到CSV"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"东方财富活跃可转债_{timestamp}.csv"
        
        try:
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"✓ 数据已保存到: {filename}")
            return filename
        except Exception as e:
            print(f"✗ 保存失败: {e}")
            return ""
    
    def display_summary(self, df: pd.DataFrame):
        """显示数据汇总"""
        if df.empty:
            print("无数据")
            return
        
        print(f"\n{'='*80}")
        print(f"活跃可转债数据汇总")
        print(f"{'='*80}")
        print(f"活跃可转债数量: {len(df)}")
        
        # 市场分布
        if '交易市场' in df.columns:
            market_dist = df['交易市场'].value_counts()
            print(f"\n市场分布:")
            for market, count in market_dist.items():
                print(f"  {market}: {count} 只")
        
        # 价格统计
        if '最新价' in df.columns:
            prices = df['最新价'].dropna()
            if not prices.empty:
                print(f"\n价格统计:")
                print(f"  平均价格: {prices.mean():.2f} 元")
                print(f"  最高价格: {prices.max():.2f} 元")
                print(f"  最低价格: {prices.min():.2f} 元")
                print(f"  价格中位数: {prices.median():.2f} 元")
        
        # 涨跌幅统计
        if '涨跌幅' in df.columns:
            changes = df['涨跌幅'].dropna()
            if not changes.empty:
                print(f"\n涨跌幅统计:")
                print(f"  平均涨跌幅: {changes.mean():.2f}%")
                print(f"  最大涨幅: {changes.max():.2f}%")
                print(f"  最大跌幅: {changes.min():.2f}%")
                
                # 涨跌分布
                up_count = len(changes[changes > 0])
                down_count = len(changes[changes < 0])
                flat_count = len(changes[changes == 0])
                print(f"  上涨: {up_count} 只, 下跌: {down_count} 只, 平盘: {flat_count} 只")
        
        # 成交额统计
        if '成交额' in df.columns:
            amounts = df['成交额'].dropna()
            if not amounts.empty:
                print(f"\n成交额统计:")
                print(f"  总成交额: {amounts.sum()/100000000:.2f} 亿元")
                print(f"  平均成交额: {amounts.mean()/10000:.2f} 万元")
                print(f"  最大成交额: {amounts.max()/10000:.2f} 万元")
        
        # 显示涨幅前10
        if '涨跌幅' in df.columns and not df.empty:
            print(f"\n涨幅前10:")
            top_gainers = df.nlargest(10, '涨跌幅')[['可转债代码', '可转债名称', '最新价', '涨跌幅', '成交额']]
            for i, (_, row) in enumerate(top_gainers.iterrows(), 1):
                amount_str = f"{row['成交额']/10000:.0f}万" if pd.notna(row['成交额']) else "N/A"
                print(f"  {i:2d}. {row['可转债代码']} {row['可转债名称']} "
                      f"价格:{row['最新价']:.2f} 涨幅:{row['涨跌幅']:.2f}% 成交额:{amount_str}")
        
        # 显示跌幅前10
        if '涨跌幅' in df.columns and not df.empty:
            print(f"\n跌幅前10:")
            top_losers = df.nsmallest(10, '涨跌幅')[['可转债代码', '可转债名称', '最新价', '涨跌幅', '成交额']]
            for i, (_, row) in enumerate(top_losers.iterrows(), 1):
                amount_str = f"{row['成交额']/10000:.0f}万" if pd.notna(row['成交额']) else "N/A"
                print(f"  {i:2d}. {row['可转债代码']} {row['可转债名称']} "
                      f"价格:{row['最新价']:.2f} 涨幅:{row['涨跌幅']:.2f}% 成交额:{amount_str}")
    
    def run(self) -> str:
        """运行爬虫"""
        print("🚀 东方财富活跃可转债爬虫启动")
        print("=" * 50)
        
        try:
            # 获取数据
            raw_data = self.get_active_bonds()
            
            if not raw_data:
                print("❌ 未获取到数据")
                return ""
            
            # 处理数据
            df = self.process_bond_data(raw_data)
            
            if df.empty:
                print("❌ 无活跃可转债数据")
                return ""
            
            # 显示汇总
            self.display_summary(df)
            
            # 保存数据
            filename = self.save_to_csv(df)
            
            if filename:
                print(f"\n✅ 爬取完成！")
                print(f"📁 数据文件: {filename}")
                print(f"📊 活跃可转债数量: {len(df)} 只")
                return filename
            else:
                print("❌ 数据保存失败")
                return ""
                
        except Exception as e:
            print(f"❌ 爬虫运行失败: {e}")
            import traceback
            traceback.print_exc()
            return ""


def main():
    """主函数"""
    spider = ActiveBondSpider()
    result_file = spider.run()
    
    if result_file:
        print(f"\n🎉 爬虫执行成功！")
        print(f"📁 数据文件: {result_file}")
        print(f"💡 提示: 这些都是当前有交易的活跃可转债")
    else:
        print(f"\n❌ 爬虫执行失败！")


if __name__ == "__main__":
    main()
