"""
可靠的自动买入系统
基于成功经验优化的自动买入功能
"""
import sys
import os
import time
from datetime import datetime
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ths_trader import ThsTrader
from config import get_config

class ReliableAutoBuy:
    """可靠的自动买入系统"""
    
    def __init__(self):
        self.config = get_config()
        self.trader = None
        
    def connect_ths(self):
        """连接同花顺"""
        print("🔗 连接同花顺客户端...")
        self.trader = ThsTrader(self.config)
        
        # 尝试连接
        if self.trader.login():
            print("✅ 同花顺连接成功")
            return True
        else:
            print("❌ 同花顺连接失败")
            return False
    
    def auto_buy_stock(self, stock_code, stock_name, price, amount_hands):
        """
        自动买入股票
        
        Args:
            stock_code: 股票代码
            stock_name: 股票名称  
            price: 买入价格
            amount_hands: 买入手数
        """
        print("=" * 60)
        print("🤖 可靠自动买入系统")
        print("=" * 60)
        
        # 计算参数
        amount_shares = amount_hands * 100
        total_amount = price * amount_shares
        
        print(f"📊 交易参数:")
        print(f"   股票代码: {stock_code}")
        print(f"   股票名称: {stock_name}")
        print(f"   买入价格: {price}元/股")
        print(f"   买入手数: {amount_hands}手")
        print(f"   买入股数: {amount_shares}股")
        print(f"   总金额: {total_amount}元")
        
        # 确认交易
        if not self.config['trade_config']['dry_run']:
            print(f"\n⚠️  实盘交易模式！")
            confirm = input(f"确认买入 {stock_name}({stock_code}) {amount_hands}手？(输入 'YES' 确认): ").strip()
            if confirm != 'YES':
                print("❌ 用户取消交易")
                return False
        
        # 连接同花顺
        if not self.connect_ths():
            return False
        
        # 执行买入
        print(f"\n🛒 执行买入...")
        try:
            success = self.trader.buy_stock(
                stock_code=stock_code,
                stock_name=stock_name,
                amount=amount_hands,  # 手数
                price=price
            )
            
            if success:
                print(f"✅ 买入成功!")
                print(f"   股票: {stock_name}({stock_code})")
                print(f"   价格: {price}元/股")
                print(f"   数量: {amount_shares}股")
                print(f"   金额: {total_amount}元")
                
                # 记录交易日志
                self.log_trade(stock_code, stock_name, price, amount_shares, "买入")
                return True
            else:
                print(f"❌ 买入失败")
                return False
                
        except Exception as e:
            print(f"❌ 买入异常: {e}")
            return False
    
    def log_trade(self, stock_code, stock_name, price, amount, action):
        """记录交易日志"""
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            log_entry = f"{timestamp} - {action} {stock_name}({stock_code}) {amount}股 @{price}元 总额{price * amount}元\n"
            
            with open("reliable_trading_log.txt", "a", encoding="utf-8") as f:
                f.write(log_entry)
            
            print(f"📝 交易日志已记录到 reliable_trading_log.txt")
        except Exception as e:
            print(f"⚠️ 记录日志失败: {e}")

def buy_000528():
    """买入000528（柳工）"""
    system = ReliableAutoBuy()
    
    return system.auto_buy_stock(
        stock_code='000528',
        stock_name='柳工',
        price=8.52,
        amount_hands=1
    )

def buy_custom_stock():
    """自定义买入股票"""
    system = ReliableAutoBuy()
    
    print("📝 请输入股票信息:")
    stock_code = input("股票代码 (如 000528): ").strip()
    stock_name = input("股票名称 (如 柳工): ").strip()
    price = float(input("买入价格 (如 8.52): ").strip())
    amount_hands = int(input("买入手数 (如 1): ").strip())
    
    return system.auto_buy_stock(stock_code, stock_name, price, amount_hands)

def batch_buy_stocks():
    """批量买入股票"""
    system = ReliableAutoBuy()
    
    # 预定义的股票列表
    stocks = [
        {'code': '000528', 'name': '柳工', 'price': 8.52, 'hands': 1},
        # 可以添加更多股票
    ]
    
    print(f"📋 批量买入 {len(stocks)} 只股票:")
    for i, stock in enumerate(stocks, 1):
        print(f"   {i}. {stock['name']}({stock['code']}) {stock['hands']}手 @{stock['price']}元")
    
    confirm = input("\n确认批量买入？(输入 'YES' 确认): ").strip()
    if confirm != 'YES':
        print("❌ 用户取消批量买入")
        return False
    
    # 连接同花顺（只连接一次）
    if not system.connect_ths():
        return False
    
    success_count = 0
    for i, stock in enumerate(stocks, 1):
        print(f"\n--- 买入第 {i}/{len(stocks)} 只股票 ---")
        
        try:
            success = system.trader.buy_stock(
                stock_code=stock['code'],
                stock_name=stock['name'],
                amount=stock['hands'],
                price=stock['price']
            )
            
            if success:
                success_count += 1
                print(f"✅ {stock['name']} 买入成功")
                system.log_trade(stock['code'], stock['name'], stock['price'], 
                                stock['hands'] * 100, "批量买入")
            else:
                print(f"❌ {stock['name']} 买入失败")
                
        except Exception as e:
            print(f"❌ {stock['name']} 买入异常: {e}")
        
        # 买入间隔
        if i < len(stocks):
            time.sleep(2)
    
    print(f"\n📊 批量买入完成: {success_count}/{len(stocks)} 成功")
    return success_count > 0

def main():
    """主函数"""
    print("🤖 可靠自动买入系统")
    print("=" * 50)
    print("基于成功经验优化的自动买入功能")
    print()
    
    print("请选择操作:")
    print("1. 买入000528 (柳工) - 1手")
    print("2. 自定义买入股票")
    print("3. 批量买入股票")
    print("4. 查看交易日志")
    print("5. 退出")
    
    choice = input("\n请输入选择 (1-5): ").strip()
    
    try:
        if choice == '1':
            success = buy_000528()
            
        elif choice == '2':
            success = buy_custom_stock()
            
        elif choice == '3':
            success = batch_buy_stocks()
            
        elif choice == '4':
            # 查看交易日志
            try:
                with open("reliable_trading_log.txt", "r", encoding="utf-8") as f:
                    logs = f.read()
                    if logs:
                        print("\n📋 交易日志:")
                        print("-" * 50)
                        print(logs)
                    else:
                        print("📋 暂无交易日志")
            except FileNotFoundError:
                print("📋 交易日志文件不存在")
            return
            
        elif choice == '5':
            print("👋 程序退出")
            return
            
        else:
            print("❌ 无效选择")
            return
        
        print("\n" + "=" * 50)
        if success:
            print("🎉 自动买入完成！")
            print("建议查看同花顺委托状态确认成交情况")
        else:
            print("❌ 自动买入失败")
            print("请检查错误信息或重试")
        print("=" * 50)
        
    except KeyboardInterrupt:
        print("\n❌ 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")

if __name__ == "__main__":
    main()
