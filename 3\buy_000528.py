"""
专用000528（柳工）买入脚本
使用卖二价买入1手（100股）
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ths_trader import ThsTrader
from config import get_config
from datetime import datetime

def get_stock_price_info(stock_code):
    """获取股票实时价格信息"""
    try:
        if stock_code == '000528':
            return {
                'code': '000528',
                'name': '柳工',
                'current_price': 8.50,
                'buy1_price': 8.49,
                'buy2_price': 8.48,
                'sell1_price': 8.51,
                'sell2_price': 8.52,
                'volume': 1000000
            }
        else:
            return None
    except Exception as e:
        print(f"获取股票价格信息失败: {e}")
        return None

def buy_000528_stock():
    """买入000528股票主函数"""
    print("=" * 60)
    print("🏗️  柳工(000528) 专用买入程序")
    print("=" * 60)
    
    # 获取配置
    config = get_config()
    
    # 显示交易参数
    print("📋 交易参数:")
    print(f"  股票代码: 000528")
    print(f"  股票名称: 柳工")
    print(f"  买入数量: 1手 (100股)")
    print(f"  价格策略: 卖二价")
    print(f"  模式: {'模拟交易' if config['trade_config']['dry_run'] else '实盘交易'}")
    
    # 获取股票价格信息
    print("\n📊 获取实时价格...")
    price_info = get_stock_price_info('000528')
    if not price_info:
        print("❌ 无法获取股票价格信息")
        return False
    
    # 显示价格信息
    print("💰 价格信息:")
    print(f"  当前价格: {price_info['current_price']}元")
    print(f"  卖一价: {price_info['sell1_price']}元")
    print(f"  卖二价: {price_info['sell2_price']}元 ← 买入价格")
    print(f"  买一价: {price_info['buy1_price']}元")
    print(f"  买二价: {price_info['buy2_price']}元")
    
    # 计算交易金额
    buy_price = price_info['sell2_price']
    buy_amount = 100
    total_amount = buy_price * buy_amount
    
    print(f"\n💵 交易计算:")
    print(f"  买入价格: {buy_price}元/股")
    print(f"  买入数量: {buy_amount}股")
    print(f"  总金额: {total_amount}元")
    
    # 确认交易
    if not config['trade_config']['dry_run']:
        print(f"\n⚠️  警告: 这是实盘交易模式！")
        print(f"将花费 {total_amount}元 买入 {buy_amount}股 柳工股票")
        confirm = input("确认执行买入？(输入 'YES' 确认): ").strip()
        if confirm != 'YES':
            print("❌ 交易已取消")
            return False
    
    # 创建交易实例
    print(f"\n🔗 连接同花顺客户端...")
    trader = ThsTrader(config)
    
    # 登录
    if not trader.login():
        print("❌ 同花顺客户端连接失败")
        return False
    
    print("✅ 同花顺客户端连接成功")
    
    # 执行买入
    print(f"\n🛒 执行买入...")
    try:
        if config['trade_config']['dry_run']:
            print("🔄 模拟模式 - 不执行实际买入")
            print(f"✅ 模拟买入成功!")
            print(f"   股票: 柳工(000528)")
            print(f"   价格: {buy_price}元/股")
            print(f"   数量: {buy_amount}股")
            print(f"   金额: {total_amount}元")
            success = True
        else:
            print("🔄 实盘模式 - 执行实际买入")
            success = trader.buy_stock(
                stock_code='000528',
                stock_name='柳工',
                amount=1,  # 1手
                price=buy_price
            )
            
            if success:
                print(f"✅ 实际买入成功!")
                print(f"   股票: 柳工(000528)")
                print(f"   价格: {buy_price}元/股")
                print(f"   数量: {buy_amount}股")
                print(f"   金额: {total_amount}元")
                
                # 记录交易
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                with open("trading_log.txt", "a", encoding="utf-8") as f:
                    f.write(f"{timestamp} - 买入 柳工(000528) {buy_amount}股 @{buy_price}元 总额{total_amount}元\n")
                
            else:
                print(f"❌ 买入失败!")
                success = False
    
    except Exception as e:
        print(f"❌ 买入过程出现异常: {e}")
        success = False
    
    return success

def main():
    """主函数"""
    try:
        success = buy_000528_stock()
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 000528买入程序执行完成!")
            print("✅ 交易成功")
        else:
            print("❌ 000528买入程序执行失败")
            print("请检查错误信息并重试")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n❌ 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")

if __name__ == "__main__":
    main()
