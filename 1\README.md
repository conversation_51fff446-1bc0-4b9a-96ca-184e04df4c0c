# mootdx 自定义板块操作测试工具

这是一套用于测试 mootdx 自定义板块操作功能的工具集，包含完整的 CRUD 操作和测试用例。

## 功能特性

### 1. 自定义板块基础操作
- ✅ 创建自定义板块
- ✅ 查询板块内容
- ✅ 更新板块股票
- ✅ 删除板块

### 2. 股票数据 CRUD 操作
- ✅ **Create**: 创建板块并添加股票
- ✅ **Read**: 查询板块中的股票列表
- ✅ **Update**: 添加/删除/替换板块中的股票
- ✅ **Delete**: 删除整个板块

### 3. 高级功能
- ✅ 股票代码格式验证
- ✅ 批量操作支持
- ✅ 重复股票检测
- ✅ 错误处理和异常捕获
- ✅ 详细的操作日志
- ✅ 单元测试支持

## 文件结构

```
├── test_customize_tool.py    # 主测试工具类
├── stock_crud_tool.py        # 股票 CRUD 操作工具
├── usage_example.py          # 使用示例
├── add_stock_to_block.py     # 交互式添加股票工具
├── add_600789.py             # 直接添加 600789 到测试板块
├── stock_manager.py          # 通用股票板块管理工具
├── 创建板块.py               # 快速演示脚本
└── README.md                 # 说明文档
```

## 安装依赖

```bash
pip install mootdx
```

## 快速开始

### 1. 基础使用

```python
from test_customize_tool import CustomizeTestTool

# 初始化工具（请根据实际情况修改通达信目录）
tool = CustomizeTestTool(tdxdir='C:/new_tdx')

# 创建板块
tool.test_create_block("龙虎榜", ['600036', '600016'])

# 查询板块
stocks = tool.test_search_block("龙虎榜")

# 更新板块
tool.test_update_block("龙虎榜", ['600036', '600016', '600519'])

# 删除板块
tool.test_remove_block("龙虎榜")
```

### 2. 股票 CRUD 操作

```python
from stock_crud_tool import StockCRUDTool

# 初始化 CRUD 工具
crud_tool = StockCRUDTool(tdxdir='C:/new_tdx')

# 创建板块并添加股票
result = crud_tool.create_block_with_stocks(
    "科技股", 
    ['000858', '002415', '300059']
)

# 添加新股票
result = crud_tool.add_stocks_to_block(
    "科技股", 
    ['300750', '688036']
)

# 移除股票
result = crud_tool.remove_stocks_from_block(
    "科技股", 
    ['002415']
)

# 查询板块
result = crud_tool.get_block_stocks("科技股")
print(f"股票列表: {result['stocks']}")

# 删除板块
result = crud_tool.delete_block("科技股")
```

### 3. 批量操作

```python
# 定义批量操作
operations = [
    {
        'type': 'create',
        'block_name': '银行股',
        'stocks': ['600036', '000001', '600016']
    },
    {
        'type': 'add',
        'block_name': '银行股',
        'stocks': ['601398', '601288']
    },
    {
        'type': 'read',
        'block_name': '银行股'
    },
    {
        'type': 'delete',
        'block_name': '银行股'
    }
]

# 执行批量操作
results = crud_tool.batch_operations(operations)
```

## 运行示例

### 1. 快速演示
```bash
python 创建板块.py
```

### 2. 向测试板块添加股票 600789
```bash
# 直接添加
python add_600789.py

# 交互式添加
python add_stock_to_block.py
```

### 3. 通用股票管理工具
```bash
# 添加股票
python stock_manager.py add 测试板块 --stock 600789

# 查看板块
python stock_manager.py list 测试板块

# 移除股票
python stock_manager.py remove 测试板块 --stock 600789

# 替换所有股票
python stock_manager.py replace 测试板块 --stocks 600036 000001 600519

# 删除板块
python stock_manager.py delete 测试板块

# 交互模式
python stock_manager.py
```

### 4. 运行综合测试
```bash
python test_customize_tool.py --comprehensive
```

### 5. 运行单元测试
```bash
python test_customize_tool.py --unittest
```

### 6. 运行使用示例
```bash
python usage_example.py
```

## API 参考

### CustomizeTestTool 类

#### 主要方法

- `test_create_block(name, symbols)`: 测试创建板块
- `test_update_block(name, symbols)`: 测试更新板块
- `test_search_block(name)`: 测试查询板块
- `test_remove_block(name)`: 测试删除板块
- `test_stock_crud_operations(block_name)`: 测试股票 CRUD 操作
- `run_comprehensive_test()`: 运行综合测试

### StockCRUDTool 类

#### 主要方法

- `create_block_with_stocks(block_name, stock_codes)`: 创建板块并添加股票
- `get_block_stocks(block_name)`: 获取板块股票列表
- `add_stocks_to_block(block_name, new_stock_codes)`: 向板块添加股票
- `remove_stocks_from_block(block_name, remove_stock_codes)`: 从板块移除股票
- `replace_block_stocks(block_name, new_stock_codes)`: 替换板块所有股票
- `delete_block(block_name)`: 删除板块
- `batch_operations(operations)`: 批量操作

#### 返回值格式

所有操作方法都返回统一格式的字典：

```python
{
    'success': bool,           # 操作是否成功
    'message': str,            # 操作结果消息
    'stocks': list,            # 股票列表（查询操作）
    'valid_codes': list,       # 有效股票代码
    'invalid_codes': list,     # 无效股票代码
    'added_codes': list,       # 新增股票代码
    'removed_codes': list,     # 移除股票代码
    'duplicate_codes': list,   # 重复股票代码
    'not_found_codes': list,   # 未找到股票代码
    'final_count': int         # 最终股票数量
}
```

## 股票代码验证

工具支持自动验证股票代码格式：

- ✅ 6位数字格式（如：600036）
- ✅ 自动检测市场类型（SH/SZ）
- ✅ 过滤无效代码
- ✅ 重复代码检测

## 错误处理

工具提供完善的错误处理机制：

- 🔍 股票代码格式验证
- 🔍 板块存在性检查
- 🔍 重复操作检测
- 🔍 异常捕获和日志记录
- 🔍 详细的错误信息反馈

## 测试模式

如果没有安装通达信或指定错误的目录，工具会自动切换到模拟模式：

- 使用 Mock 对象模拟 mootdx 功能
- 提供完整的测试流程
- 不会影响实际的通达信数据

## 注意事项

1. **通达信目录**: 请确保指定正确的通达信安装目录
2. **权限问题**: 确保有读写通达信配置文件的权限
3. **备份数据**: 建议在测试前备份通达信的自定义板块数据
4. **股票代码**: 仅支持6位数字格式的股票代码

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！

## 更新日志

### v1.0.0 (2025-08-06)
- 初始版本发布
- 支持完整的 CRUD 操作
- 提供批量操作功能
- 包含完整的测试用例
