#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库使用示例
演示如何使用数据库功能进行数据爬取、查询和分析
"""

from plate_data_crawler import PlateDataCrawler
from database_manager import DatabaseManager
import time
import json


def example_crawl_and_save():
    """示例1: 爬取数据并保存到数据库"""
    print("=" * 60)
    print("示例1: 爬取数据并保存到数据库")
    print("=" * 60)
    
    crawler = PlateDataCrawler()
    
    # 爬取多个题材数据
    plate_names = [
        "汽车芯片",
        "人工智能", 
        "新能源汽车",
        "半导体"
    ]
    
    for plate_name in plate_names:
        print(f"\n🔄 正在爬取: {plate_name}")
        
        # 爬取并保存到数据库
        stocks = crawler.crawl_plate_data(plate_name, save_csv=False, save_db=True)
        
        if stocks:
            print(f"✅ {plate_name}: 成功保存 {len(stocks)} 只股票到数据库")
        else:
            print(f"❌ {plate_name}: 爬取失败")
        
        # 避免请求过于频繁
        time.sleep(2)


def example_query_database():
    """示例2: 查询数据库数据"""
    print("\n" + "=" * 60)
    print("示例2: 查询数据库数据")
    print("=" * 60)
    
    crawler = PlateDataCrawler()
    
    # 查询特定题材的股票
    print(f"\n📊 查询汽车芯片题材股票:")
    auto_chip_stocks = crawler.get_stocks_from_db("汽车芯片")
    
    if auto_chip_stocks:
        print(f"找到 {len(auto_chip_stocks)} 只汽车芯片股票:")
        for i, stock in enumerate(auto_chip_stocks[:5], 1):
            print(f"  {i}. {stock['name']} ({stock['symbol']}) - {stock['industry']}")
    
    # 搜索特定股票
    print(f"\n🔍 搜索包含'科技'的股票:")
    tech_stocks = crawler.search_stocks("科技")
    
    if tech_stocks:
        print(f"找到 {len(tech_stocks)} 只相关股票:")
        for i, stock in enumerate(tech_stocks[:5], 1):
            print(f"  {i}. {stock['name']} ({stock['symbol']}) - {stock['industry']}")
    
    # 查看题材汇总
    print(f"\n📈 题材汇总:")
    plates_summary = crawler.get_plates_summary()
    
    if plates_summary:
        for plate in plates_summary:
            print(f"  {plate['plate_name']}: {plate['stock_count']} 只股票 (最后更新: {plate['last_crawl_time']})")


def example_database_analysis():
    """示例3: 数据库分析"""
    print("\n" + "=" * 60)
    print("示例3: 数据库分析")
    print("=" * 60)
    
    db_manager = DatabaseManager()
    
    # 数据库基本信息
    print(f"\n📊 数据库基本信息:")
    db_info = db_manager.get_database_info()
    
    for table, info in db_info.get('table_info', {}).items():
        print(f"  {table}: {info.get('record_count', 0)} 条记录")
    
    # 题材统计
    print(f"\n📈 题材统计:")
    plate_stats = db_manager.get_plate_statistics()
    
    if plate_stats:
        for stat in plate_stats:
            print(f"  {stat['plate_name']}: {stat['stock_count']} 只股票, {stat['industry_count']} 个行业, {stat['area_count']} 个地区")
    
    # 行业分布
    print(f"\n🏭 行业分布 (前10个):")
    industry_dist = db_manager.get_industry_distribution()
    
    if industry_dist:
        for i, dist in enumerate(industry_dist[:10], 1):
            print(f"  {i}. {dist['industry']}: {dist['count']} 只股票")
    
    # 地区分布
    print(f"\n🌍 地区分布 (前10个):")
    area_dist = db_manager.get_area_distribution()
    
    if area_dist:
        for i, dist in enumerate(area_dist[:10], 1):
            print(f"  {i}. {dist['area']}: {dist['count']} 只股票")


def example_specific_analysis():
    """示例4: 特定题材分析"""
    print("\n" + "=" * 60)
    print("示例4: 特定题材分析 - 汽车芯片")
    print("=" * 60)
    
    db_manager = DatabaseManager()
    
    plate_name = "汽车芯片"
    
    # 该题材的行业分布
    print(f"\n🏭 {plate_name} 行业分布:")
    industry_dist = db_manager.get_industry_distribution(plate_name)
    
    if industry_dist:
        for dist in industry_dist:
            print(f"  {dist['industry']}: {dist['count']} 只股票")
    
    # 该题材的地区分布
    print(f"\n🌍 {plate_name} 地区分布:")
    area_dist = db_manager.get_area_distribution(plate_name)
    
    if area_dist:
        for dist in area_dist:
            print(f"  {dist['area']}: {dist['count']} 只股票")


def example_export_data():
    """示例5: 导出数据"""
    print("\n" + "=" * 60)
    print("示例5: 导出数据到Excel")
    print("=" * 60)
    
    db_manager = DatabaseManager()
    
    # 导出所有数据到Excel
    print(f"\n💾 正在导出数据...")
    excel_file = db_manager.export_to_excel()
    
    if excel_file:
        print(f"✅ 数据已导出到: {excel_file}")
        print(f"📋 Excel文件包含以下工作表:")
        print(f"  - 股票数据: 所有股票的详细信息")
        print(f"  - 题材汇总: 各题材的统计信息")
        print(f"  - 爬取日志: 爬取操作的历史记录")
        print(f"  - 题材统计: 题材的详细统计")
        print(f"  - 行业分布: 各行业的股票分布")
        print(f"  - 地区分布: 各地区的股票分布")
    else:
        print(f"❌ 导出失败")


def example_advanced_query():
    """示例6: 高级查询"""
    print("\n" + "=" * 60)
    print("示例6: 高级查询示例")
    print("=" * 60)
    
    crawler = PlateDataCrawler()
    
    # 查询所有数据
    all_stocks = crawler.get_stocks_from_db()
    
    if all_stocks:
        print(f"\n📊 数据库总计: {len(all_stocks)} 只股票")
        
        # 按题材分组统计
        plate_count = {}
        industry_count = {}
        area_count = {}
        
        for stock in all_stocks:
            # 题材统计
            plate = stock.get('plate_name', 'Unknown')
            plate_count[plate] = plate_count.get(plate, 0) + 1
            
            # 行业统计
            industry = stock.get('industry', 'Unknown')
            industry_count[industry] = industry_count.get(industry, 0) + 1
            
            # 地区统计
            area = stock.get('area', 'Unknown')
            area_count[area] = area_count.get(area, 0) + 1
        
        print(f"\n📈 题材分布:")
        for plate, count in sorted(plate_count.items(), key=lambda x: x[1], reverse=True):
            print(f"  {plate}: {count} 只股票")
        
        print(f"\n🏭 热门行业 (前5个):")
        for industry, count in sorted(industry_count.items(), key=lambda x: x[1], reverse=True)[:5]:
            print(f"  {industry}: {count} 只股票")
        
        print(f"\n🌍 热门地区 (前5个):")
        for area, count in sorted(area_count.items(), key=lambda x: x[1], reverse=True)[:5]:
            print(f"  {area}: {count} 只股票")


def main():
    """主函数"""
    print("🚀 数据库使用示例")
    print("=" * 60)
    
    try:
        # 运行各个示例
        example_crawl_and_save()
        example_query_database()
        example_database_analysis()
        example_specific_analysis()
        example_export_data()
        example_advanced_query()
        
        print("\n" + "=" * 60)
        print("🎉 所有示例运行完成!")
        print("=" * 60)
        
        # 显示数据库文件信息
        import os
        db_path = "plate_stocks.db"
        if os.path.exists(db_path):
            file_size = os.path.getsize(db_path) / 1024 / 1024  # MB
            print(f"\n📁 数据库文件: {db_path}")
            print(f"📊 文件大小: {file_size:.2f} MB")
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")


if __name__ == "__main__":
    main()
