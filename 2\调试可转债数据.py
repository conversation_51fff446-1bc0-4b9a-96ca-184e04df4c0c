#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试可转债数据

检查数据库中是否有可转债价格数据
"""

import pymysql
import pymysql.cursors
import pandas as pd
from datetime import datetime, <PERSON><PERSON><PERSON>


def check_bond_data():
    """检查可转债数据"""
    
    print("🔍 检查数据库中的可转债数据")
    print("=" * 60)
    
    # 数据库配置
    db_config = {
        'host': '***********',
        'port': 3306,
        'user': 'iQuant',
        'password': 'NAAnwaRsb8YGN3F5',
        'database': 'iquant',
        'charset': 'utf8mb4'
    }
    
    try:
        # 连接数据库
        connection = pymysql.connect(**db_config)
        print("✓ 数据库连接成功")
        
        # 1. 查看数据库中所有以1开头的6位代码（可转债通常是1开头）
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        query1 = """
        SELECT DISTINCT symbol 
        FROM iquant_daily_price 
        WHERE symbol REGEXP '^1[0-9]{5}$'
        ORDER BY symbol
        LIMIT 20
        """
        
        cursor.execute(query1)
        bond_symbols = cursor.fetchall()
        
        print(f"\n📊 数据库中以1开头的6位代码 (前20个):")
        for i, row in enumerate(bond_symbols, 1):
            print(f"  {i:2d}. {row['symbol']}")
        
        # 2. 查看数据库中所有以11开头的代码
        query2 = """
        SELECT DISTINCT symbol 
        FROM iquant_daily_price 
        WHERE symbol LIKE '11%'
        ORDER BY symbol
        LIMIT 20
        """
        
        cursor.execute(query2)
        bond_symbols_11 = cursor.fetchall()
        
        print(f"\n📊 数据库中以11开头的代码 (前20个):")
        for i, row in enumerate(bond_symbols_11, 1):
            print(f"  {i:2d}. {row['symbol']}")
        
        # 3. 查看数据库中所有以12开头的代码
        query3 = """
        SELECT DISTINCT symbol 
        FROM iquant_daily_price 
        WHERE symbol LIKE '12%'
        ORDER BY symbol
        LIMIT 20
        """
        
        cursor.execute(query3)
        bond_symbols_12 = cursor.fetchall()
        
        print(f"\n📊 数据库中以12开头的代码 (前20个):")
        for i, row in enumerate(bond_symbols_12, 1):
            print(f"  {i:2d}. {row['symbol']}")
        
        # 4. 检查具体的可转债代码
        test_bonds = ['110060', '118040', '123092', '127049', '113001']
        
        print(f"\n🎯 检查具体可转债代码:")
        for bond_code in test_bonds:
            query4 = """
            SELECT COUNT(*) as count, MIN(day) as min_date, MAX(day) as max_date
            FROM iquant_daily_price 
            WHERE symbol = %s
            """
            
            cursor.execute(query4, (bond_code,))
            result = cursor.fetchone()
            
            if result['count'] > 0:
                print(f"  ✓ {bond_code}: {result['count']} 条记录, {result['min_date']} ~ {result['max_date']}")
            else:
                print(f"  ✗ {bond_code}: 无数据")
        
        # 5. 查看最近30天有数据的可转债代码
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=30)
        
        query5 = """
        SELECT symbol, COUNT(*) as count, MIN(day) as min_date, MAX(day) as max_date,
               AVG(close) as avg_price, MIN(close) as min_price, MAX(close) as max_price
        FROM iquant_daily_price 
        WHERE symbol REGEXP '^1[0-9]{5}$'
        AND day >= %s 
        AND day <= %s
        GROUP BY symbol
        HAVING count >= 10
        ORDER BY count DESC
        LIMIT 20
        """
        
        cursor.execute(query5, (start_date, end_date))
        recent_bonds = cursor.fetchall()
        
        print(f"\n📈 最近30天有数据的可转债 (前20个):")
        print(f"{'代码':<8} {'记录数':<6} {'平均价':<8} {'价格区间':<15} {'日期范围':<20}")
        print("-" * 70)
        
        for bond in recent_bonds:
            price_range = f"{bond['min_price']:.2f}-{bond['max_price']:.2f}"
            date_range = f"{bond['min_date']} ~ {bond['max_date']}"
            print(f"{bond['symbol']:<8} {bond['count']:<6} {bond['avg_price']:<8.2f} "
                  f"{price_range:<15} {date_range:<20}")
        
        # 6. 计算一个示例的ATR
        if recent_bonds:
            sample_bond = recent_bonds[0]['symbol']
            print(f"\n🔍 计算 {sample_bond} 的ATR示例:")
            
            query6 = """
            SELECT day, open, high, low, close, volume
            FROM iquant_daily_price 
            WHERE symbol = %s 
            AND day >= %s 
            AND day <= %s
            ORDER BY day ASC
            """
            
            cursor.execute(query6, (sample_bond, start_date, end_date))
            price_data = cursor.fetchall()
            
            if price_data:
                df = pd.DataFrame(price_data)
                
                # 计算日内振幅
                df['daily_range'] = (df['high'] - df['low']) / df['close'] * 100
                avg_daily_range = df['daily_range'].mean()
                max_daily_range = df['daily_range'].max()
                
                print(f"  数据点数: {len(df)}")
                print(f"  平均日内振幅: {avg_daily_range:.2f}%")
                print(f"  最大日内振幅: {max_daily_range:.2f}%")
                print(f"  价格区间: {df['low'].min():.2f} - {df['high'].max():.2f}")
                
                # 显示前5天数据
                print(f"\n  前5天数据:")
                print(f"  {'日期':<12} {'开盘':<8} {'最高':<8} {'最低':<8} {'收盘':<8} {'振幅%':<8}")
                for _, row in df.head().iterrows():
                    print(f"  {row['day']:<12} {row['open']:<8.2f} {row['high']:<8.2f} "
                          f"{row['low']:<8.2f} {row['close']:<8.2f} {row['daily_range']:<8.2f}")
        
        cursor.close()
        connection.close()
        print("\n✓ 数据库连接已关闭")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    check_bond_data()


if __name__ == "__main__":
    main()
