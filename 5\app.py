"""
基于 Tornado 的同花顺下单接口服务
参考：https://blog.csdn.net/flex8888/article/details/148420964

接口：
- POST /api/queue  批量下单入队（json数组）
- POST /api/search 查询：get_position / get_today_trades / get_today_entrusts / get_balance

运行：
  pip install tornado
  python 5/app.py
"""
import os
import sys
import json
import tornado.ioloop
import tornado.web

# 路径修正，便于直接运行
CUR_DIR = os.path.dirname(os.path.abspath(__file__))
REPO_ROOT = os.path.dirname(CUR_DIR)
sys.path.insert(0, CUR_DIR)
sys.path.insert(0, os.path.join(REPO_ROOT, '3'))

from api_config import CFG
from queue_utils import add_to_queue
from worker import run_loop

from config import get_config
from ths_trader import ThsTrader
from captcha_helper import wait_captcha_and_notify, force_close_captcha


def build_trader():
    """构建带自动回退的 ThsTrader"""
    cfg = get_config()
    exe_path = CFG.get('exe_path')
    if not exe_path or not os.path.exists(exe_path):
        cfg['account_config']['exe_path'] = ''
        print('ℹ️ [search] 未配置或找不到 xiadan.exe，尝试附着已打开的客户端或默认启动')
    else:
        cfg['account_config']['exe_path'] = exe_path
        print(f'ℹ️ [search] 使用配置的 xiadan.exe 路径: {exe_path}')
    attempts = 0
    t = None
    while attempts < 4:
        attempts += 1
        t = ThsTrader(cfg)
        ok = t.login()
        if ok:
            break
        if exe_path:
            print('⚠️ [search] 带路径连接失败，回退为无路径附着模式再试一次...')
            cfg['account_config']['exe_path'] = ''
            t = ThsTrader(cfg)
            if t.login():
                break
        print('⏳ [search] 等待 2 秒后重试连接（可手动打开并登录同花顺）...')
        import time as _t
        _t.sleep(2)
    return t


class BaseHandler(tornado.web.RequestHandler):
    def set_default_headers(self):
        self.set_header('Access-Control-Allow-Origin', '*')
        self.set_header('Access-Control-Allow-Methods', 'GET,POST,OPTIONS')
        self.set_header('Access-Control-Allow-Headers', 'Content-Type')

    def options(self, *args, **kwargs):
        self.set_status(204)
        self.finish()


class QueueHandler(BaseHandler):
    def post(self):
        try:
            items = json.loads(self.request.body.decode('utf-8'))
            if not isinstance(items, list):
                raise ValueError('payload must be a json array')
            add_to_queue(items)
            self.write({'code': 0, 'msg': 'queued', 'size': len(items)})
        except Exception as e:
            self.set_status(400)
            self.write({'code': -1, 'msg': str(e)})


class SearchHandler(BaseHandler):
    def _do_search(self, operate: str):
        trader = build_trader()
        if operate == 'get_position':
            return trader.get_position()
        elif operate == 'get_today_trades':
            return trader.get_today_trades()
        elif operate == 'get_today_entrusts':
            return trader.get_today_entrusts()
        elif operate == 'get_balance':
            return trader.get_balance()
        else:
            raise ValueError('unsupported operate')

    def post(self):
        try:
            data = json.loads(self.request.body.decode('utf-8'))
            operate = data.get('operate')
            # 验证码弹窗提示（若出现）
            try:
                wait_captcha_and_notify(60)
            except Exception:
                pass
            res = self._do_search(operate)
            try:
                force_close_captcha(2, 0.4)
            except Exception:
                pass
            self.write({'code': 0, 'data': res})
        except Exception as e:
            self.set_status(400)
            self.write({'code': -1, 'msg': str(e)})

    def get(self):
        try:
            operate = self.get_argument('operate')
            # 验证码弹窗提示（若出现）
            try:
                wait_captcha_and_notify(60)
            except Exception:
                pass
            res = self._do_search(operate)
            try:
                force_close_captcha(2, 0.4)
            except Exception:
                pass
            self.write({'code': 0, 'data': res})
        except Exception as e:
            self.set_status(400)
            self.write({'code': -1, 'msg': str(e)})


class PositionHandler(BaseHandler):
    def get(self):
        try:
            res = build_trader().get_position()
            self.write({'code': 0, 'data': res})
        except Exception as e:
            self.set_status(400)
            self.write({'code': -1, 'msg': str(e)})

class BalanceHandler(BaseHandler):
    def get(self):
        try:
            res = build_trader().get_balance()
            self.write({'code': 0, 'data': res})
        except Exception as e:
            self.set_status(400)
            self.write({'code': -1, 'msg': str(e)})

class EntrustsHandler(BaseHandler):
    def get(self):
        try:
            res = build_trader().get_today_entrusts()
            self.write({'code': 0, 'data': res})
        except Exception as e:
            self.set_status(400)
            self.write({'code': -1, 'msg': str(e)})


def make_app():
    return tornado.web.Application([
        (r"/api/queue", QueueHandler),
        (r"/api/search", SearchHandler),
        (r"/api/position", PositionHandler),
        (r"/api/balance", BalanceHandler),
        (r"/api/entrusts", EntrustsHandler),
    ])


def main():
    app = make_app()
    app.listen(CFG['port'])
    # 启动后台worker循环（使用线程，避免阻塞IOLoop）
    import threading
    threading.Thread(target=run_loop, daemon=True).start()
    print(f"THS API Service started at http://127.0.0.1:{CFG['port']}")
    tornado.ioloop.IOLoop.current().start()


if __name__ == '__main__':
    main()

