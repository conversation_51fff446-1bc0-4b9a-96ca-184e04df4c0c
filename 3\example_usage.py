"""
同花顺交易接口使用示例
演示如何使用交易接口进行自动交易
"""
import time
import json
from ths_trader import ThsTrader
from config import get_config, get_test_config


def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 获取配置
    config = get_test_config()  # 使用测试配置，不会实际交易
    
    # 创建交易实例
    trader = ThsTrader(config)
    
    # 测试数据源连接
    print("1. 测试数据源连接...")
    data = trader.get_data_source()
    if data:
        print(f"成功获取 {len(data)} 条记录")
        for i, record in enumerate(data[:3]):  # 显示前3条
            print(f"  记录{i+1}: {record['name']}({record['code']}) - {record['bkname']}")
    else:
        print("无法获取数据源数据")
        return
    
    # 检查新记录（第一次运行会将所有记录标记为已知）
    print("\n2. 检查新记录...")
    new_records = trader.check_new_records()
    print(f"发现 {len(new_records)} 条新记录")
    
    # 模拟登录（测试模式下不会真正登录）
    print("\n3. 模拟登录...")
    if config['trade_config']['dry_run']:
        print("测试模式：跳过实际登录")
        trader.is_logged_in = True  # 模拟登录成功
    else:
        success = trader.login()
        print(f"登录结果: {'成功' if success else '失败'}")
    
    # 模拟买入
    print("\n4. 模拟买入...")
    if data:
        test_record = data[0]
        stock_code = test_record['code']
        stock_name = test_record['name']
        
        if config['trade_config']['dry_run']:
            print(f"测试模式：模拟买入 {stock_name}({stock_code})")
        else:
            success = trader.buy_stock(stock_code, stock_name)
            print(f"买入结果: {'成功' if success else '失败'}")


def example_monitoring():
    """监控示例"""
    print("\n=== 监控示例 ===")
    
    config = get_test_config()
    trader = ThsTrader(config)
    
    # 模拟登录
    trader.is_logged_in = True
    
    print("开始监控数据源（运行10秒）...")
    
    # 重写处理新记录方法以便演示
    original_process = trader.process_new_record
    def demo_process_new_record(record):
        print(f"发现新记录: {record['name']}({record['code']}) - {record['bkname']}")
        if config['trade_config']['dry_run']:
            print(f"  测试模式：模拟买入 {record['name']}")
        else:
            original_process(record)
    
    trader.process_new_record = demo_process_new_record
    
    # 开始监控
    trader.start_monitoring(interval=2)
    
    # 运行10秒
    time.sleep(10)
    
    # 停止监控
    trader.stop_monitoring()
    print("监控结束")


def example_account_info():
    """账户信息示例"""
    print("\n=== 账户信息示例 ===")
    
    config = get_config()
    trader = ThsTrader(config)
    
    # 尝试登录
    print("尝试登录同花顺客户端...")
    success = trader.login()
    
    if success:
        print("登录成功！")
        
        # 获取账户信息
        account_info = trader.get_account_info()
        if account_info:
            print("账户信息:")
            print(f"  资金余额: {account_info.get('balance', '未知')}")
            print(f"  持仓情况: {len(account_info.get('position', []))} 只股票")
        
        # 获取今日成交
        today_trades = trader.get_today_trades()
        if today_trades:
            print(f"今日成交: {len(today_trades)} 笔")
        
    else:
        print("登录失败，请检查同花顺客户端是否正常运行")


def example_risk_control():
    """风控示例"""
    print("\n=== 风控示例 ===")
    
    config = get_config()
    
    # 设置风控参数
    config['risk_config']['max_daily_trades'] = 5
    config['risk_config']['blacklist_codes'] = ['000001', '000002']
    config['risk_config']['enable_risk_control'] = True
    
    trader = ThsTrader(config)
    
    # 模拟风控检查
    test_codes = ['603101', '000001', '002471']
    
    for code in test_codes:
        if code in config['risk_config']['blacklist_codes']:
            print(f"股票 {code} 在黑名单中，跳过交易")
        else:
            print(f"股票 {code} 通过风控检查")


def example_custom_strategy():
    """自定义策略示例"""
    print("\n=== 自定义策略示例 ===")
    
    config = get_test_config()
    trader = ThsTrader(config)
    
    # 自定义处理逻辑
    def custom_process_record(record):
        """自定义记录处理逻辑"""
        stock_code = record.get('code')
        stock_name = record.get('name')
        bk_name = record.get('bkname')
        
        print(f"分析股票: {stock_name}({stock_code}) - 板块: {bk_name}")
        
        # 自定义策略逻辑
        if '机器人' in bk_name:
            print(f"  策略匹配: 机器人概念股，建议买入")
            if config['trade_config']['dry_run']:
                print(f"  测试模式: 模拟买入 {stock_name}")
            else:
                trader.buy_stock(stock_code, stock_name)
        elif '新疆' in bk_name:
            print(f"  策略匹配: 新疆板块，谨慎买入")
        else:
            print(f"  策略不匹配: 跳过")
    
    # 替换默认处理方法
    trader.process_new_record = custom_process_record
    
    # 测试自定义策略
    test_data = trader.get_data_source()
    if test_data:
        print("测试自定义策略...")
        for record in test_data[:3]:
            custom_process_record(record)


def interactive_demo():
    """交互式演示"""
    print("\n=== 交互式演示 ===")
    
    config = get_test_config()
    trader = ThsTrader(config)
    
    while True:
        print("\n请选择操作:")
        print("1. 获取数据源")
        print("2. 检查新记录")
        print("3. 开始监控（10秒）")
        print("4. 模拟买入")
        print("5. 查看配置")
        print("0. 退出")
        
        choice = input("请输入选择 (0-5): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            data = trader.get_data_source()
            if data:
                print(f"获取到 {len(data)} 条记录")
                for i, record in enumerate(data[:5]):
                    print(f"  {i+1}. {record['name']}({record['code']}) - {record['bkname']}")
            else:
                print("获取数据失败")
        
        elif choice == '2':
            new_records = trader.check_new_records()
            print(f"发现 {len(new_records)} 条新记录")
            for record in new_records:
                print(f"  新记录: {record['name']}({record['code']})")
        
        elif choice == '3':
            print("开始监控10秒...")
            trader.start_monitoring(interval=2)
            time.sleep(10)
            trader.stop_monitoring()
            print("监控结束")
        
        elif choice == '4':
            data = trader.get_data_source()
            if data:
                record = data[0]
                print(f"模拟买入: {record['name']}({record['code']})")
            else:
                print("无数据可用")
        
        elif choice == '5':
            print("当前配置:")
            print(f"  数据源: {config['data_source_url']}")
            print(f"  测试模式: {config['trade_config']['dry_run']}")
            print(f"  默认买入量: {config['trade_config']['default_amount']}手")
            print(f"  监控间隔: {config['monitor_config']['interval']}秒")
        
        else:
            print("无效选择")


def main():
    """主函数"""
    print("同花顺交易接口使用示例")
    print("=" * 50)
    
    try:
        # 运行各种示例
        example_basic_usage()
        example_monitoring()
        
        # 询问是否运行需要实际登录的示例
        run_login_examples = input("\n是否运行需要登录的示例？(y/n): ").lower() == 'y'
        if run_login_examples:
            example_account_info()
        
        example_risk_control()
        example_custom_strategy()
        
        # 询问是否运行交互式演示
        run_interactive = input("\n是否运行交互式演示？(y/n): ").lower() == 'y'
        if run_interactive:
            interactive_demo()
        
        print("\n示例运行完成！")
        
    except KeyboardInterrupt:
        print("\n用户中断程序")
    except Exception as e:
        print(f"\n程序运行出错: {e}")


if __name__ == '__main__':
    main()
