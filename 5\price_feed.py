"""
简单的行情获取：从新浪接口取卖一价(ask1)
不依赖额外三方库，使用内置 urllib
"""
from urllib import request
import ssl
import re

# 有些环境需要忽略证书校验
ssl._create_default_https_context = ssl._create_unverified_context

SINA_URL = "http://hq.sinajs.cn/list={code}"

def normalize_code(code: str) -> str:
    code = code.strip()
    if code.startswith('6'):
        return 'sh' + code
    else:
        return 'sz' + code

# 解析规则参考新浪返回：
# var hq_str_sz000001="平安银行,开盘,昨收,现价,最高,最低,买一价,卖一价,成交量,成交额,买一量,买一,买二量,买二,买三量,买三,买四量,买四,买五量,买五,卖一量,卖一,卖二量,卖二,...,日期,时间";
# 第8个字段为卖一价(ask1)

def get_sell1_price(code: str, timeout: int = 5):
    try:
        url = SINA_URL.format(code=normalize_code(code))
        # 需要 Referer 才能正常返回
        req = request.Request(url, headers={
            'Referer': 'http://finance.sina.com.cn',
            'User-Agent': 'Mozilla/5.0'
        })
        with request.urlopen(req, timeout=timeout) as resp:
            txt = resp.read().decode('gbk', errors='ignore')
        # 提取引号中的逗号分隔字段
        m = re.search(r'"([^"]+)"', txt)
        if not m:
            return None
        parts = m.group(1).split(',')
        if len(parts) >= 8:
            ask1 = parts[7]
            try:
                return float(ask1)
            except Exception:
                return None
        return None
    except Exception:
        return None

