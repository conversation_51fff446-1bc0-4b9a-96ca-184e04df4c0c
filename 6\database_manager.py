#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理工具
提供数据库查询、统计、导出等功能
"""

import sqlite3
import json
import pandas as pd
from datetime import datetime
from typing import Dict, List, Optional, Any
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DatabaseManager:
    """数据库管理类"""
    
    def __init__(self, db_path: str = "plate_stocks.db"):
        self.db_path = db_path
    
    def get_database_info(self) -> Dict[str, Any]:
        """
        获取数据库基本信息
        
        Returns:
            数据库信息字典
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取表信息
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            info = {
                "database_path": self.db_path,
                "tables": tables,
                "table_info": {}
            }
            
            # 获取每个表的记录数
            for table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                info["table_info"][table] = {"record_count": count}
            
            conn.close()
            return info
            
        except Exception as e:
            logger.error(f"获取数据库信息失败: {e}")
            return {}
    
    def get_plate_statistics(self) -> List[Dict[str, Any]]:
        """
        获取题材统计信息
        
        Returns:
            题材统计列表
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT 
                    plate_name,
                    COUNT(*) as stock_count,
                    MAX(crawl_time) as last_crawl_time,
                    COUNT(DISTINCT industry) as industry_count,
                    COUNT(DISTINCT area) as area_count
                FROM stocks 
                GROUP BY plate_name 
                ORDER BY stock_count DESC
            ''')
            
            columns = [description[0] for description in cursor.description]
            rows = cursor.fetchall()
            
            statistics = [dict(zip(columns, row)) for row in rows]
            
            conn.close()
            return statistics
            
        except Exception as e:
            logger.error(f"获取题材统计失败: {e}")
            return []
    
    def get_industry_distribution(self, plate_name: str = None) -> List[Dict[str, Any]]:
        """
        获取行业分布统计
        
        Args:
            plate_name: 题材名称，为None时统计所有题材
            
        Returns:
            行业分布列表
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            if plate_name:
                cursor.execute('''
                    SELECT industry, COUNT(*) as count
                    FROM stocks 
                    WHERE plate_name = ? AND industry IS NOT NULL
                    GROUP BY industry 
                    ORDER BY count DESC
                ''', (plate_name,))
            else:
                cursor.execute('''
                    SELECT industry, COUNT(*) as count
                    FROM stocks 
                    WHERE industry IS NOT NULL
                    GROUP BY industry 
                    ORDER BY count DESC
                ''')
            
            columns = [description[0] for description in cursor.description]
            rows = cursor.fetchall()
            
            distribution = [dict(zip(columns, row)) for row in rows]
            
            conn.close()
            return distribution
            
        except Exception as e:
            logger.error(f"获取行业分布失败: {e}")
            return []
    
    def get_area_distribution(self, plate_name: str = None) -> List[Dict[str, Any]]:
        """
        获取地区分布统计
        
        Args:
            plate_name: 题材名称，为None时统计所有题材
            
        Returns:
            地区分布列表
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            if plate_name:
                cursor.execute('''
                    SELECT area, COUNT(*) as count
                    FROM stocks 
                    WHERE plate_name = ? AND area IS NOT NULL
                    GROUP BY area 
                    ORDER BY count DESC
                ''', (plate_name,))
            else:
                cursor.execute('''
                    SELECT area, COUNT(*) as count
                    FROM stocks 
                    WHERE area IS NOT NULL
                    GROUP BY area 
                    ORDER BY count DESC
                ''')
            
            columns = [description[0] for description in cursor.description]
            rows = cursor.fetchall()
            
            distribution = [dict(zip(columns, row)) for row in rows]
            
            conn.close()
            return distribution
            
        except Exception as e:
            logger.error(f"获取地区分布失败: {e}")
            return []
    
    def export_to_excel(self, output_file: str = None) -> str:
        """
        导出数据到Excel文件
        
        Args:
            output_file: 输出文件路径
            
        Returns:
            导出的文件路径
        """
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"股票数据导出_{timestamp}.xlsx"
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 创建Excel写入器
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                
                # 导出股票数据
                stocks_df = pd.read_sql_query("SELECT * FROM stocks", conn)
                stocks_df.to_excel(writer, sheet_name='股票数据', index=False)
                
                # 导出题材汇总
                plates_df = pd.read_sql_query("SELECT * FROM plates", conn)
                plates_df.to_excel(writer, sheet_name='题材汇总', index=False)
                
                # 导出爬取日志
                logs_df = pd.read_sql_query("SELECT * FROM crawl_logs ORDER BY crawl_time DESC LIMIT 1000", conn)
                logs_df.to_excel(writer, sheet_name='爬取日志', index=False)
                
                # 导出统计信息
                stats = self.get_plate_statistics()
                if stats:
                    stats_df = pd.DataFrame(stats)
                    stats_df.to_excel(writer, sheet_name='题材统计', index=False)
                
                # 导出行业分布
                industry_dist = self.get_industry_distribution()
                if industry_dist:
                    industry_df = pd.DataFrame(industry_dist)
                    industry_df.to_excel(writer, sheet_name='行业分布', index=False)
                
                # 导出地区分布
                area_dist = self.get_area_distribution()
                if area_dist:
                    area_df = pd.DataFrame(area_dist)
                    area_df.to_excel(writer, sheet_name='地区分布', index=False)
            
            conn.close()
            
            logger.info(f"数据已导出到: {output_file}")
            return output_file
            
        except Exception as e:
            logger.error(f"导出Excel失败: {e}")
            return ""
    
    def clean_old_data(self, days: int = 30) -> int:
        """
        清理旧数据
        
        Args:
            days: 保留最近多少天的数据
            
        Returns:
            删除的记录数
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 删除旧的股票数据
            cursor.execute('''
                DELETE FROM stocks 
                WHERE crawl_time < datetime('now', '-{} days')
            '''.format(days))
            
            deleted_stocks = cursor.rowcount
            
            # 删除旧的爬取日志
            cursor.execute('''
                DELETE FROM crawl_logs 
                WHERE crawl_time < datetime('now', '-{} days')
            '''.format(days))
            
            deleted_logs = cursor.rowcount
            
            conn.commit()
            conn.close()
            
            total_deleted = deleted_stocks + deleted_logs
            logger.info(f"清理完成，删除了 {total_deleted} 条记录")
            
            return total_deleted
            
        except Exception as e:
            logger.error(f"清理数据失败: {e}")
            return 0


def main():
    """主函数 - 数据库管理示例"""
    
    db_manager = DatabaseManager()
    
    print("=" * 60)
    print("数据库管理工具")
    print("=" * 60)
    
    # 显示数据库信息
    db_info = db_manager.get_database_info()
    print(f"\n📊 数据库信息:")
    print(f"数据库路径: {db_info.get('database_path', 'Unknown')}")
    print(f"表数量: {len(db_info.get('tables', []))}")
    
    for table, info in db_info.get('table_info', {}).items():
        print(f"  {table}: {info.get('record_count', 0)} 条记录")
    
    # 显示题材统计
    plate_stats = db_manager.get_plate_statistics()
    if plate_stats:
        print(f"\n📈 题材统计 (前10个):")
        for i, stat in enumerate(plate_stats[:10], 1):
            print(f"  {i}. {stat['plate_name']}: {stat['stock_count']} 只股票")
    
    # 显示行业分布
    industry_dist = db_manager.get_industry_distribution()
    if industry_dist:
        print(f"\n🏭 行业分布 (前10个):")
        for i, dist in enumerate(industry_dist[:10], 1):
            print(f"  {i}. {dist['industry']}: {dist['count']} 只股票")
    
    # 导出数据
    print(f"\n💾 导出数据到Excel...")
    excel_file = db_manager.export_to_excel()
    if excel_file:
        print(f"✅ 导出成功: {excel_file}")
    else:
        print("❌ 导出失败")


if __name__ == "__main__":
    main()
