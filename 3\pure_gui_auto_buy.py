"""
纯GUI自动化买入脚本
不依赖easytrader，直接操作同花顺界面
"""
import pyautogui
import time
import pygetwindow as gw
from datetime import datetime

class PureGUIAutoBuy:
    """纯GUI自动化买入系统"""
    
    def __init__(self):
        # 设置pyautogui参数
        pyautogui.FAILSAFE = True  # 鼠标移到左上角停止
        pyautogui.PAUSE = 0.3      # 每次操作间隔
        
    def find_ths_window(self):
        """查找同花顺窗口"""
        try:
            # 尝试多种窗口标题
            window_titles = ['同花顺', '买入', '委托', '交易', 'xiadan']
            
            for title in window_titles:
                windows = gw.getWindowsWithTitle(title)
                if windows:
                    print(f"✅ 找到窗口: {title}")
                    return windows[0]
            
            # 如果没找到，列出所有窗口
            all_windows = gw.getAllWindows()
            print("当前所有窗口:")
            for i, window in enumerate(all_windows[:10]):  # 只显示前10个
                print(f"  {i+1}. {window.title}")
            
            return None
            
        except Exception as e:
            print(f"❌ 查找窗口失败: {e}")
            return None
    
    def activate_window(self, window):
        """激活窗口"""
        try:
            window.activate()
            time.sleep(1)
            print("✅ 窗口已激活")
            return True
        except Exception as e:
            print(f"❌ 激活窗口失败: {e}")
            return False
    
    def input_stock_info_by_keyboard(self, stock_code, price, amount):
        """使用键盘输入股票信息"""
        print(f"⌨️ 使用键盘输入股票信息...")
        
        try:
            # 方法1: 使用Tab键导航
            print("   使用Tab键导航到证券代码输入框...")
            
            # 按多次Tab键确保到达证券代码输入框
            for i in range(8):
                pyautogui.press('tab')
                time.sleep(0.2)
            
            # 输入股票代码
            print(f"   输入股票代码: {stock_code}")
            pyautogui.hotkey('ctrl', 'a')  # 全选
            time.sleep(0.2)
            pyautogui.write(stock_code)
            time.sleep(0.5)
            pyautogui.press('enter')  # 确认代码
            time.sleep(1.5)  # 等待股票名称加载
            
            # 移动到价格输入框
            print(f"   输入买入价格: {price}")
            pyautogui.press('tab')
            time.sleep(0.3)
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.2)
            pyautogui.write(str(price))
            time.sleep(0.5)
            
            # 移动到数量输入框
            print(f"   输入买入数量: {amount}")
            pyautogui.press('tab')
            time.sleep(0.3)
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.2)
            pyautogui.write(str(amount))
            time.sleep(0.5)
            
            print("✅ 股票信息输入完成")
            return True
            
        except Exception as e:
            print(f"❌ 键盘输入失败: {e}")
            return False
    
    def input_stock_info_by_mouse(self, stock_code, price, amount):
        """使用鼠标点击输入股票信息"""
        print(f"🖱️ 使用鼠标点击输入股票信息...")
        
        try:
            # 根据您的截图，这些是大概的坐标位置
            # 实际使用时可能需要调整
            
            # 点击证券代码输入框
            print(f"   点击证券代码输入框...")
            pyautogui.click(x=320, y=60)  # 证券代码框位置
            time.sleep(0.5)
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.2)
            pyautogui.write(stock_code)
            time.sleep(0.5)
            pyautogui.press('enter')
            time.sleep(1.5)
            
            # 点击买入价格输入框
            print(f"   点击买入价格输入框...")
            pyautogui.click(x=320, y=98)  # 买入价格框位置
            time.sleep(0.5)
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.2)
            pyautogui.write(str(price))
            time.sleep(0.5)
            
            # 点击买入数量输入框
            print(f"   点击买入数量输入框...")
            pyautogui.click(x=320, y=118)  # 买入数量框位置
            time.sleep(0.5)
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.2)
            pyautogui.write(str(amount))
            time.sleep(0.5)
            
            print("✅ 鼠标输入完成")
            return True
            
        except Exception as e:
            print(f"❌ 鼠标输入失败: {e}")
            return False
    
    def click_buy_button(self):
        """点击买入按钮"""
        print("🛒 点击买入按钮...")
        
        try:
            # 方法1: 使用快捷键
            pyautogui.hotkey('alt', 'b')
            time.sleep(1)
            print("✅ 使用快捷键点击买入")
            return True
            
        except Exception as e:
            print(f"⚠️ 快捷键失败: {e}")
            
            try:
                # 方法2: 使用Enter键
                pyautogui.press('enter')
                time.sleep(1)
                print("✅ 使用Enter键确认")
                return True
                
            except Exception as e2:
                print(f"⚠️ Enter键失败: {e2}")
                
                try:
                    # 方法3: 点击买入按钮坐标
                    pyautogui.click(x=340, y=178)  # 买入按钮位置
                    time.sleep(1)
                    print("✅ 点击买入按钮坐标")
                    return True
                    
                except Exception as e3:
                    print(f"❌ 所有方法都失败: {e3}")
                    return False
    
    def auto_buy_stock(self, stock_code, stock_name, price, amount):
        """自动买入股票"""
        print("=" * 60)
        print("🤖 纯GUI自动化买入系统")
        print("=" * 60)
        
        print(f"📊 交易参数:")
        print(f"   股票代码: {stock_code}")
        print(f"   股票名称: {stock_name}")
        print(f"   买入价格: {price}元")
        print(f"   买入数量: {amount}股")
        print(f"   总金额: {price * amount}元")
        
        # 查找同花顺窗口
        print(f"\n🔍 查找同花顺窗口...")
        window = self.find_ths_window()
        
        if window:
            # 激活窗口
            if not self.activate_window(window):
                print("⚠️ 请手动切换到同花顺买入界面")
        else:
            print("⚠️ 未找到同花顺窗口，请确保同花顺已打开买入界面")
        
        input("请确保同花顺买入界面已打开，然后按回车继续...")
        
        # 准备输入
        print(f"\n⏰ 准备自动输入...")
        for i in range(3, 0, -1):
            print(f"   {i}秒后开始...")
            time.sleep(1)
        
        # 尝试键盘输入
        print(f"\n⌨️ 尝试键盘输入...")
        success = self.input_stock_info_by_keyboard(stock_code, price, amount)
        
        if not success:
            print(f"\n🖱️ 键盘输入失败，尝试鼠标输入...")
            success = self.input_stock_info_by_mouse(stock_code, price, amount)
        
        if not success:
            print(f"\n❌ 自动输入失败，请手动输入以下信息:")
            print(f"   证券代码: {stock_code}")
            print(f"   买入价格: {price}")
            print(f"   买入数量: {amount}")
            input("手动输入完成后按回车继续...")
        
        # 确认信息
        print(f"\n🔍 请检查同花顺界面中的信息:")
        print(f"   - 证券代码: {stock_code}")
        print(f"   - 证券名称: {stock_name}")
        print(f"   - 买入价格: {price}")
        print(f"   - 买入数量: {amount}")
        
        confirm = input("\n信息正确请输入 'YES' 继续买入: ").strip()
        
        if confirm == 'YES':
            # 点击买入按钮
            if self.click_buy_button():
                print("✅ 买入操作已执行")
                
                # 记录日志
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                log_entry = f"{timestamp} - GUI自动买入 {stock_name}({stock_code}) {amount}股 @{price}元\n"
                
                with open("gui_auto_buy_log.txt", "a", encoding="utf-8") as f:
                    f.write(log_entry)
                
                print("📝 交易日志已记录")
                return True
            else:
                print("⚠️ 请手动点击买入按钮")
                input("点击完成后按回车确认...")
                return True
        else:
            print("❌ 用户取消买入")
            return False

def main():
    """主函数"""
    print("🤖 纯GUI自动化买入系统")
    print("不依赖easytrader，直接操作同花顺界面")
    print("=" * 50)
    
    # 创建自动买入实例
    auto_buy = PureGUIAutoBuy()
    
    # 买入000528柳工
    success = auto_buy.auto_buy_stock(
        stock_code='000528',
        stock_name='柳工',
        price=8.52,
        amount=100
    )
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 GUI自动买入完成！")
        print("请在同花顺中查看委托状态")
    else:
        print("❌ GUI自动买入失败")
        print("请检查同花顺界面或手动完成买入")
    print("=" * 50)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n❌ 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
