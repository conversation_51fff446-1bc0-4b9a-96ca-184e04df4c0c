#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试涨停正股转债查询功能

使用真实数据库连接测试涨停股票查询
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from 涨停正股转债查询 import LimitUpStockBondQuery


def test_database_connection():
    """测试数据库连接"""
    print("🔍 测试数据库连接...")
    
    # 数据库配置
    db_config = {
        'host': '***********',
        'port': 3306,
        'user': 'iQuant',
        'password': 'NAAnwaRsb8YGN3F5',
        'database': 'iquant',
        'charset': 'utf8mb4'
    }
    
    try:
        # 初始化查询器
        query_tool = LimitUpStockBondQuery(db_config)
        
        # 测试连接
        if query_tool.connect_database():
            print("✅ 数据库连接成功！")
            
            # 测试查询最近5天的涨停数据
            print("\n🔍 测试查询最近5天涨停数据...")
            limit_up_df = query_tool.get_limit_up_stocks_recent_days(days=5)
            
            if not limit_up_df.empty:
                print(f"✅ 查询成功！找到 {len(limit_up_df)} 条涨停记录")
                print(f"涉及股票: {limit_up_df['symbol'].nunique()} 只")
                
                # 显示前5条记录
                print("\n前5条涨停记录:")
                for i, (_, row) in enumerate(limit_up_df.head().iterrows(), 1):
                    print(f"  {i}. {row['symbol']} - {row['day']} "
                          f"收盘:{row['close']:.2f} 涨幅:{row['change_pct']:.2f}%")
                
                # 测试完整的涨停转债查询
                print(f"\n🔍 测试涨停股票转债查询...")
                result = query_tool.get_limit_up_stocks_with_bonds(days=5)
                
                # 显示简化报告
                summary = result['summary']
                print(f"\n📊 查询结果汇总:")
                print(f"  涨停股票总数: {summary['total_limit_up']} 只")
                print(f"  有对应可转债: {summary['with_bonds']} 只")
                print(f"  无对应可转债: {summary['without_bonds']} 只")
                print(f"  对应可转债总数: {summary['total_bonds']} 只")
                
                # 显示有可转债的涨停股票
                if result['stocks_with_bonds']:
                    print(f"\n🎯 有可转债的涨停股票:")
                    for i, stock in enumerate(result['stocks_with_bonds'][:5], 1):
                        bonds_str = ', '.join([f"{b['bond_code']}({b['bond_name']})" 
                                             for b in stock['bonds'][:2]])  # 只显示前2个
                        print(f"  {i}. {stock['symbol']} - {stock['latest_limit_up_date']}")
                        print(f"     可转债: {bonds_str}")
                
                # 导出数据
                if result['stocks_with_bonds']:
                    print(f"\n📁 导出数据...")
                    output_file = query_tool.export_limit_up_bonds(result)
                    if output_file:
                        print(f"✅ 数据已导出: {output_file}")
                
            else:
                print("⚠️  最近5天没有涨停记录")
            
            # 关闭连接
            query_tool.close()
            
        else:
            print("❌ 数据库连接失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_specific_query():
    """测试特定查询"""
    print("\n" + "="*60)
    print("🔍 测试特定股票查询")
    print("="*60)
    
    # 数据库配置
    db_config = {
        'host': '***********',
        'port': 3306,
        'user': 'iQuant',
        'password': 'NAAnwaRsb8YGN3F5',
        'database': 'iquant',
        'charset': 'utf8mb4'
    }
    
    try:
        query_tool = LimitUpStockBondQuery(db_config)
        
        if query_tool.connect_database():
            # 测试查询特定股票的涨停记录
            test_stocks = ['600326', '002475', '600036']  # 天路、立讯、招行
            
            for stock_code in test_stocks:
                print(f"\n查询 {stock_code} 的涨停记录...")
                
                # 查询该股票是否有可转债
                if stock_code in query_tool.bond_stock_mapping:
                    bonds = query_tool.bond_stock_mapping[stock_code]
                    print(f"  对应可转债: {len(bonds)} 只")
                    for bond in bonds:
                        print(f"    • {bond['bond_code']} {bond['bond_name']}")
                else:
                    print(f"  无对应可转债")
            
            query_tool.close()
            
    except Exception as e:
        print(f"❌ 特定查询测试失败: {e}")


def main():
    """主函数"""
    print("🚀 涨停正股转债查询测试")
    print("="*60)
    
    # 测试1: 数据库连接和基本查询
    test_database_connection()
    
    # 测试2: 特定股票查询
    test_specific_query()
    
    print(f"\n✅ 测试完成！")
    print("💡 如果测试成功，说明数据库连接正常，可以正常查询涨停股票对应的可转债")


if __name__ == "__main__":
    main()
