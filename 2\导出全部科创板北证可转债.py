#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
导出全部科创板和北证可转债

从完整的可转债数据中导出所有科创板（68开头）和北证（8开头）股票对应的可转债
不限制涨停条件，导出全部结果
"""

import pandas as pd
from datetime import datetime


def export_all_kechuang_beice_bonds():
    """导出所有科创板和北证可转债"""
    
    print("🚀 导出全部科创板和北证可转债")
    print("=" * 60)
    
    try:
        # 读取完整的可转债数据
        df = pd.read_csv("东方财富终极完整可转债_20250807_004218.csv", encoding='utf-8-sig')
        print(f"✓ 读取完整数据: {len(df)} 条记录")
        
        # 筛选正常状态的可转债
        normal_df = df[df['状态'] == '正常'].copy()
        print(f"✓ 正常状态可转债: {len(normal_df)} 条")
        
        # 筛选科创板股票（68开头）
        kechuang_df = normal_df[
            normal_df['正股代码'].astype(str).str.startswith('68')
        ].copy()
        print(f"✓ 科创板可转债: {len(kechuang_df)} 条")
        
        # 筛选北证股票（8开头，但排除68开头）
        beice_df = normal_df[
            normal_df['正股代码'].astype(str).str.startswith('8') & 
            ~normal_df['正股代码'].astype(str).str.startswith('68')
        ].copy()
        print(f"✓ 北证可转债: {len(beice_df)} 条")
        
        # 合并数据
        combined_df = pd.concat([kechuang_df, beice_df], ignore_index=True)
        print(f"✓ 合计: {len(combined_df)} 条科创板+北证可转债")
        
        if combined_df.empty:
            print("⚠️  没有找到科创板或北证可转债")
            return None
        
        # 添加市场标识
        combined_df['市场类型'] = combined_df['正股代码'].astype(str).apply(
            lambda x: '科创板' if x.startswith('68') else '北证' if x.startswith('8') else '其他'
        )
        
        # 重新整理字段顺序，使其更易读
        output_columns = [
            '债券代码', '债券简称', '正股代码', '正股简称', '市场类型',
            '债现价', '涨跌幅', '涨跌额', '成交量', '成交额', '换手率',
            '最高价', '最低价', '开盘价', '昨收价', '振幅',
            '转股价', '转股价值', '转股溢价率', '纯债价值', '纯债溢价率',
            '信用评级', '发行规模', '上市时间', '到期日期', '剩余年限',
            '申购日期', '申购代码', '股权登记日', '每股配售额',
            '回售触发价', '强赎触发价', '状态', '市场', '交易市场',
            '数据获取时间'
        ]
        
        # 只保留存在的列
        available_columns = [col for col in output_columns if col in combined_df.columns]
        export_df = combined_df[available_columns].copy()
        
        # 显示详细信息
        display_summary(export_df)
        
        # 导出完整数据
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"全部科创板北证可转债_{timestamp}.csv"
        export_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"\n📁 全部数据已导出: {output_file}")
        
        # 分别导出科创板和北证
        kechuang_only = export_df[export_df['市场类型'] == '科创板']
        beice_only = export_df[export_df['市场类型'] == '北证']
        
        if not kechuang_only.empty:
            kechuang_file = f"科创板可转债_{timestamp}.csv"
            kechuang_only.to_csv(kechuang_file, index=False, encoding='utf-8-sig')
            print(f"📁 科创板数据已导出: {kechuang_file}")
        
        if not beice_only.empty:
            beice_file = f"北证可转债_{timestamp}.csv"
            beice_only.to_csv(beice_file, index=False, encoding='utf-8-sig')
            print(f"📁 北证数据已导出: {beice_file}")
        
        return export_df
        
    except Exception as e:
        print(f"❌ 导出失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def display_summary(df):
    """显示汇总信息"""
    
    print(f"\n{'='*80}")
    print("全部科创板北证可转债汇总")
    print(f"{'='*80}")
    
    # 基本统计
    total_count = len(df)
    kechuang_count = len(df[df['市场类型'] == '科创板'])
    beice_count = len(df[df['市场类型'] == '北证'])
    
    print(f"📊 基本统计:")
    print(f"  总数量: {total_count} 只")
    print(f"  科创板: {kechuang_count} 只 ({kechuang_count/total_count*100:.1f}%)")
    print(f"  北证: {beice_count} 只 ({beice_count/total_count*100:.1f}%)")
    
    # 有实时价格的统计
    with_price = df[
        (df['债现价'].notna()) & 
        (df['债现价'] != '') & 
        (df['债现价'].astype(str) != 'nan')
    ]
    print(f"  有实时价格: {len(with_price)} 只 ({len(with_price)/total_count*100:.1f}%)")
    
    # 评级分布
    if '信用评级' in df.columns:
        rating_counts = df['信用评级'].value_counts()
        print(f"\n⭐ 评级分布:")
        for rating, count in rating_counts.head(10).items():
            if pd.notna(rating) and rating != '':
                percentage = count / total_count * 100
                print(f"  {rating}: {count} 只 ({percentage:.1f}%)")
    
    # 价格分布（有价格的）
    if not with_price.empty:
        prices = pd.to_numeric(with_price['债现价'], errors='coerce').dropna()
        if not prices.empty:
            print(f"\n💰 价格分布 (有价格的 {len(prices)} 只):")
            print(f"  平均价格: {prices.mean():.2f} 元")
            print(f"  最高价格: {prices.max():.2f} 元")
            print(f"  最低价格: {prices.min():.2f} 元")
            print(f"  价格中位数: {prices.median():.2f} 元")
            
            # 价格区间分布
            high_price = len(prices[prices >= 200])
            mid_high_price = len(prices[(prices >= 150) & (prices < 200)])
            mid_price = len(prices[(prices >= 120) & (prices < 150)])
            low_price = len(prices[prices < 120])
            
            print(f"\n📈 价格区间分布:")
            print(f"  200元以上: {high_price} 只 ({high_price/len(prices)*100:.1f}%)")
            print(f"  150-200元: {mid_high_price} 只 ({mid_high_price/len(prices)*100:.1f}%)")
            print(f"  120-150元: {mid_price} 只 ({mid_price/len(prices)*100:.1f}%)")
            print(f"  120元以下: {low_price} 只 ({low_price/len(prices)*100:.1f}%)")
    
    # 显示科创板前10只
    kechuang_bonds = df[df['市场类型'] == '科创板']
    if not kechuang_bonds.empty:
        print(f"\n🚀 科创板可转债前10只:")
        for i, (_, row) in enumerate(kechuang_bonds.head(10).iterrows(), 1):
            bond_code = row['债券代码']
            bond_name = row['债券简称']
            stock_code = row['正股代码']
            stock_name = row['正股简称']
            price = row.get('债现价', '')
            rating = row.get('信用评级', '')
            
            price_str = f" 价格:{price}" if price and str(price) not in ['', 'nan'] else ""
            rating_str = f" 评级:{rating}" if rating and str(rating) not in ['', 'nan'] else ""
            
            print(f"  {i:2d}. {bond_code} {bond_name} → {stock_code} {stock_name}{price_str}{rating_str}")
    
    # 显示北证全部
    beice_bonds = df[df['市场类型'] == '北证']
    if not beice_bonds.empty:
        print(f"\n🏛️  北证可转债全部 {len(beice_bonds)} 只:")
        for i, (_, row) in enumerate(beice_bonds.iterrows(), 1):
            bond_code = row['债券代码']
            bond_name = row['债券简称']
            stock_code = row['正股代码']
            stock_name = row['正股简称']
            price = row.get('债现价', '')
            rating = row.get('信用评级', '')
            
            price_str = f" 价格:{price}" if price and str(price) not in ['', 'nan'] else ""
            rating_str = f" 评级:{rating}" if rating and str(rating) not in ['', 'nan'] else ""
            
            print(f"  {i:2d}. {bond_code} {bond_name} → {stock_code} {stock_name}{price_str}{rating_str}")


def create_summary_report(df):
    """创建汇总报告"""
    
    if df is None or df.empty:
        return
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_file = f"科创板北证可转债汇总报告_{timestamp}.md"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("# 科创板北证可转债完整汇总报告\n\n")
        f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # 基本统计
        total_count = len(df)
        kechuang_count = len(df[df['市场类型'] == '科创板'])
        beice_count = len(df[df['市场类型'] == '北证'])
        
        f.write("## 📊 基本统计\n\n")
        f.write(f"- **总数量**: {total_count} 只\n")
        f.write(f"- **科创板**: {kechuang_count} 只 ({kechuang_count/total_count*100:.1f}%)\n")
        f.write(f"- **北证**: {beice_count} 只 ({beice_count/total_count*100:.1f}%)\n\n")
        
        # 科创板详细列表
        kechuang_bonds = df[df['市场类型'] == '科创板']
        if not kechuang_bonds.empty:
            f.write("## 🚀 科创板可转债完整列表\n\n")
            f.write("| 序号 | 债券代码 | 债券简称 | 正股代码 | 正股简称 | 当前价格 | 评级 |\n")
            f.write("|------|----------|----------|----------|----------|----------|------|\n")
            
            for i, (_, row) in enumerate(kechuang_bonds.iterrows(), 1):
                bond_code = row['债券代码']
                bond_name = row['债券简称']
                stock_code = row['正股代码']
                stock_name = row['正股简称']
                price = row.get('债现价', '--')
                rating = row.get('信用评级', '--')
                
                if pd.isna(price) or str(price) == 'nan':
                    price = '--'
                if pd.isna(rating) or str(rating) == 'nan':
                    rating = '--'
                
                f.write(f"| {i} | {bond_code} | {bond_name} | {stock_code} | {stock_name} | {price} | {rating} |\n")
        
        # 北证详细列表
        beice_bonds = df[df['市场类型'] == '北证']
        if not beice_bonds.empty:
            f.write("\n## 🏛️ 北证可转债完整列表\n\n")
            f.write("| 序号 | 债券代码 | 债券简称 | 正股代码 | 正股简称 | 当前价格 | 评级 |\n")
            f.write("|------|----------|----------|----------|----------|----------|------|\n")
            
            for i, (_, row) in enumerate(beice_bonds.iterrows(), 1):
                bond_code = row['债券代码']
                bond_name = row['债券简称']
                stock_code = row['正股代码']
                stock_name = row['正股简称']
                price = row.get('债现价', '--')
                rating = row.get('信用评级', '--')
                
                if pd.isna(price) or str(price) == 'nan':
                    price = '--'
                if pd.isna(rating) or str(rating) == 'nan':
                    rating = '--'
                
                f.write(f"| {i} | {bond_code} | {bond_name} | {stock_code} | {stock_name} | {price} | {rating} |\n")
    
    print(f"📋 汇总报告已生成: {report_file}")


def main():
    """主函数"""
    print("🚀 导出全部科创板北证可转债工具")
    print("=" * 60)
    
    # 导出全部数据
    result_df = export_all_kechuang_beice_bonds()
    
    if result_df is not None and not result_df.empty:
        # 生成汇总报告
        create_summary_report(result_df)
        
        print(f"\n✅ 导出完成！")
        print(f"📊 总计: {len(result_df)} 只科创板/北证可转债")
        print(f"🚀 科创板: {len(result_df[result_df['市场类型'] == '科创板'])} 只")
        print(f"🏛️  北证: {len(result_df[result_df['市场类型'] == '北证'])} 只")
        print(f"💡 已导出完整数据，包含所有字段信息")
    else:
        print(f"\n❌ 导出失败或无数据")


if __name__ == "__main__":
    main()
