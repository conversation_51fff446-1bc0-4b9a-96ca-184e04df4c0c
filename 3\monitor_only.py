"""
仅监控模式
监控数据源并显示买入信号，不连接交易客户端
"""
import sys
import os
import time
import signal
from datetime import datetime
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ths_trader import ThsTrader
from config import get_config

class MonitorOnlySystem:
    """仅监控系统"""
    
    def __init__(self):
        self.config = get_config()
        # 强制设置为测试模式
        self.config['trade_config']['dry_run'] = True
        self.trader = ThsTrader(self.config)
        self.running = False
        self.signal_count = 0
        
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            print("\n收到退出信号，正在安全关闭...")
            self.stop()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def show_banner(self):
        """显示横幅"""
        print("=" * 60)
        print("🔍 同花顺数据源监控系统 (仅监控模式)")
        print("=" * 60)
        print("功能说明:")
        print("✅ 实时监控数据源")
        print("✅ 检测新记录并显示买入信号")
        print("✅ 记录详细日志")
        print("✅ 风控检查")
        print("⚠️  不连接交易客户端（避免连接问题）")
        print("⚠️  不执行实际买入操作")
        print("=" * 60)
    
    def test_data_source(self):
        """测试数据源连接"""
        print("\n📡 测试数据源连接...")
        data = self.trader.get_data_source()
        
        if data:
            print(f"✅ 数据源连接成功，获取到 {len(data)} 条记录")
            print("\n📋 当前数据:")
            for i, record in enumerate(data[:5]):  # 显示前5条
                print(f"  {i+1}. {record['name']}({record['code']}) - {record['bkname']} - {record['addtime']}")
            return True
        else:
            print("❌ 数据源连接失败")
            return False
    
    def custom_process_record(self, record):
        """自定义记录处理（仅监控模式）"""
        stock_code = record.get('code')
        stock_name = record.get('name')
        bk_name = record.get('bkname')
        add_time = record.get('addtime')
        
        self.signal_count += 1
        
        print(f"\n🚨 买入信号 #{self.signal_count}")
        print(f"📊 股票: {stock_name}({stock_code})")
        print(f"🏷️  板块: {bk_name}")
        print(f"⏰ 时间: {add_time}")
        print(f"🕐 检测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 风控检查
        if not self.trader.is_trading_time():
            print("⚠️  当前不在交易时间")
        
        if not self.trader.is_valid_stock_code(stock_code):
            print("⚠️  股票代码格式无效")
        
        # 策略分析
        strategy_result = self.analyze_strategy(record)
        print(f"📈 策略分析: {strategy_result}")
        
        # 模拟买入信息
        default_amount = self.config['trade_config']['default_amount']
        print(f"💰 建议买入: {default_amount}手")
        print(f"🔔 操作建议: 手动在交易软件中买入")
        
        print("-" * 40)
    
    def analyze_strategy(self, record):
        """分析策略"""
        bk_name = record.get('bkname', '')
        
        # 简单策略示例
        if '机器人' in bk_name:
            return "机器人概念股，建议买入"
        elif '新疆' in bk_name:
            return "新疆板块，谨慎买入"
        elif '科技' in bk_name:
            return "科技股，积极买入"
        else:
            return "其他板块，观察"
    
    def start_monitoring(self):
        """开始监控"""
        interval = self.config['monitor_config']['interval']
        
        print(f"\n🔄 开始监控数据源")
        print(f"⏱️  监控间隔: {interval}秒")
        print(f"🎯 监控目标: {self.config['data_source_url']}")
        print("📝 按 Ctrl+C 停止监控")
        print("=" * 60)
        
        # 替换处理方法
        self.trader.process_new_record = self.custom_process_record
        
        self.running = True
        self.trader.start_monitoring(interval)
        
        try:
            # 主循环
            while self.running:
                time.sleep(30)  # 每30秒显示一次状态
                self.show_status()
        except KeyboardInterrupt:
            pass
        finally:
            self.stop()
    
    def show_status(self):
        """显示运行状态"""
        if not self.running:
            return
            
        current_time = datetime.now().strftime('%H:%M:%S')
        print(f"\n[{current_time}] 📊 监控状态:")
        print(f"  🔔 已检测信号: {self.signal_count} 个")
        print(f"  ⏰ 系统运行中...")
        
        # 显示最新数据
        try:
            data = self.trader.get_data_source()
            if data:
                print(f"  📡 数据源状态: 正常 ({len(data)} 条记录)")
            else:
                print(f"  📡 数据源状态: 异常")
        except:
            print(f"  📡 数据源状态: 连接失败")
    
    def stop(self):
        """停止监控"""
        if self.running:
            print("\n🛑 正在停止监控...")
            self.running = False
            self.trader.stop_monitoring()
            print("✅ 监控已停止")
            print(f"📊 本次会话统计:")
            print(f"  🔔 总信号数: {self.signal_count}")
            print(f"  ⏱️  运行时间: 从启动到现在")

def main():
    """主函数"""
    system = MonitorOnlySystem()
    
    # 设置信号处理
    system.setup_signal_handlers()
    
    # 显示横幅
    system.show_banner()
    
    # 测试数据源
    if not system.test_data_source():
        print("\n❌ 数据源测试失败，无法继续")
        return
    
    # 询问是否开始监控
    print(f"\n准备开始监控...")
    print("这将:")
    print("1. 实时监控数据源新记录")
    print("2. 显示买入信号和建议")
    print("3. 记录详细日志")
    print("4. 不执行实际买入操作")
    
    confirm = input("\n是否开始监控？(y/n): ").lower().strip()
    if confirm != 'y':
        print("取消监控")
        return
    
    # 开始监控
    system.start_monitoring()

if __name__ == '__main__':
    main()
