"""
简化自动买入脚本
基于成功的buy_000528.py优化
"""
import sys
import os
from datetime import datetime
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ths_trader import ThsTrader
from config import get_config

def simple_auto_buy(stock_code='000528', price=8.52, hands=1):
    """
    简化自动买入
    
    Args:
        stock_code: 股票代码，默认000528
        price: 买入价格，默认8.52
        hands: 买入手数，默认1手
    """
    print("🚀 简化自动买入系统")
    print("=" * 40)
    
    # 股票信息映射
    stock_names = {
        '000528': '柳工',
        '600519': '贵州茅台',
        '000001': '平安银行',
        '000002': '万科A'
    }
    
    stock_name = stock_names.get(stock_code, '未知股票')
    amount_shares = hands * 100
    total_amount = price * amount_shares
    
    print(f"📊 买入信息:")
    print(f"   {stock_name}({stock_code})")
    print(f"   {price}元/股 × {amount_shares}股")
    print(f"   总金额: {total_amount}元")
    
    # 获取配置并创建交易实例
    config = get_config()
    trader = ThsTrader(config)
    
    print(f"\n🔗 连接同花顺...")
    
    # 登录同花顺
    if trader.login():
        print("✅ 连接成功")
        
        # 执行买入
        print(f"🛒 执行买入...")
        success = trader.buy_stock(stock_code, stock_name, hands, price)
        
        if success:
            print(f"✅ 买入成功！")
            
            # 记录日志
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            log_entry = f"{timestamp} - 买入 {stock_name}({stock_code}) {amount_shares}股 @{price}元\n"
            
            with open("simple_auto_buy_log.txt", "a", encoding="utf-8") as f:
                f.write(log_entry)
            
            return True
        else:
            print(f"❌ 买入失败")
            return False
    else:
        print("❌ 连接失败")
        return False

# 预设的快速买入函数
def buy_000528():
    """快速买入000528柳工"""
    return simple_auto_buy('000528', 8.52, 1)

def buy_600519():
    """快速买入600519茅台"""
    return simple_auto_buy('600519', 1445.0, 1)

def buy_000001():
    """快速买入000001平安银行"""
    return simple_auto_buy('000001', 12.3, 1)

if __name__ == "__main__":
    print("选择快速买入:")
    print("1. 000528 柳工")
    print("2. 600519 茅台") 
    print("3. 000001 平安银行")
    print("4. 自定义")
    
    try:
        choice = input("请选择 (1-4): ").strip()
        
        if choice == '1':
            success = buy_000528()
        elif choice == '2':
            success = buy_600519()
        elif choice == '3':
            success = buy_000001()
        elif choice == '4':
            code = input("股票代码: ").strip()
            price = float(input("买入价格: ").strip())
            hands = int(input("买入手数: ").strip())
            success = simple_auto_buy(code, price, hands)
        else:
            print("无效选择")
            success = False
        
        print("\n" + "=" * 40)
        if success:
            print("🎉 自动买入完成！")
        else:
            print("❌ 自动买入失败")
        print("=" * 40)
        
    except KeyboardInterrupt:
        print("\n程序被中断")
    except Exception as e:
        print(f"程序出错: {e}")
