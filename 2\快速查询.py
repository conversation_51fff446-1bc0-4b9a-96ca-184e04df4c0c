#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
快速查询工具

输入正股代码，快速获取可转债信息
"""

import sys
from 正股查可转债 import StockToBondQuery


def quick_query(stock_code: str):
    """快速查询单个股票的可转债"""
    print(f"正在查询正股 {stock_code} 对应的可转债...")
    
    # 初始化工具
    query_tool = StockToBondQuery()
    
    try:
        # 查询
        result = query_tool.query_bonds_by_stock(stock_code)
        
        # 显示结果
        if result['success']:
            print(f"\n✓ 找到 {len(result['bonds'])} 只可转债:")
            for i, bond in enumerate(result['bonds'], 1):
                print(f"  {i}. {bond['bond_code']} - {bond['bond_name']} ({bond['market']})")
            
            # 显示行情信息
            if result['stock_quotes']:
                print(f"\n正股行情: {result['stock_quotes'].get('price', 'N/A')}")
            
            if result['bonds_quotes']:
                print("可转债行情:")
                for bq in result['bonds_quotes']:
                    if bq.get('price', 0) > 0:
                        print(f"  {bq.get('bond_code')}: {bq.get('price')}")
        else:
            print(f"\n✗ {result['message']}")
    
    except Exception as e:
        print(f"✗ 查询失败: {e}")
    finally:
        query_tool.close()


def batch_query(stock_codes: list):
    """批量查询多个股票的可转债"""
    print(f"正在批量查询 {len(stock_codes)} 只股票的可转债...")
    
    query_tool = StockToBondQuery()
    
    try:
        for i, stock_code in enumerate(stock_codes, 1):
            print(f"\n[{i}/{len(stock_codes)}] 查询 {stock_code}:")
            
            result = query_tool.query_bonds_by_stock(stock_code)
            
            if result['success']:
                bond_codes = [bond['bond_code'] for bond in result['bonds']]
                print(f"  ✓ 可转债: {', '.join(bond_codes)}")
            else:
                print(f"  ✗ 无可转债")
    
    except Exception as e:
        print(f"✗ 批量查询失败: {e}")
    finally:
        query_tool.close()


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python 快速查询.py 002475")
        print("  python 快速查询.py 002475 600036 600519")
        return
    
    stock_codes = sys.argv[1:]
    
    if len(stock_codes) == 1:
        # 单个查询
        quick_query(stock_codes[0])
    else:
        # 批量查询
        batch_query(stock_codes)


if __name__ == "__main__":
    main()
