#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
mootdx 自定义板块操作快速运行脚本

快速测试和演示 mootdx 自定义板块功能
"""

from test_customize_tool import CustomizeTestTool
from stock_crud_tool import StockCRUDTool


def quick_demo():
    """快速演示"""
    print("=" * 50)
    print("mootdx 自定义板块操作快速演示")
    print("=" * 50)

    # 请根据实际情况修改通达信目录
    tdx_dir = "D:/zyb(6.3.5 7.66)M"  # 修改为你的通达信安装目录

    print(f"指定通达信目录: {tdx_dir}")

    import os
    if not os.path.exists(tdx_dir):
        print("目录不存在，使用模拟模式进行演示...")
        tdx_dir = None
    else:
        print("目录存在，使用真实模式...")

    # 1. 基础测试
    print("\n1. 基础板块操作测试")
    tool = CustomizeTestTool(tdxdir=tdx_dir)

    # 创建测试板块
    tool.test_create_block("测试板块", ['600036', '600016', '000001'])

    # 查询板块
    tool.test_search_block("测试板块")

    # 更新板块
    tool.test_update_block("测试板块", ['600789', '600016', '000001', '600519'])

    # 2. CRUD 操作测试
    print("\n2. 股票 CRUD 操作测试")
    crud_tool = StockCRUDTool(tdxdir=tdx_dir)

    # # 创建板块
    # result = crud_tool.create_block_with_stocks("CRUD测试", ['000858', '002415'])
    # print(f"创建结果: {result['message']}")

    # 添加股票
    result = crud_tool.add_stocks_to_block("CRUD测试", ['300059', '600519'])
    print(f"添加结果: {result['message']}")

    # 查询股票
    result = crud_tool.get_block_stocks("CRUD测试")
    if result['success']:
        print(f"当前股票: {result['stocks']}")

    # 清理测试数据
    # print("\n3. 清理测试数据")
    # tool.test_remove_block("测试板块")
    # crud_tool.delete_block("CRUD测试")

    print("\n快速演示完成！")
    print("更多功能请运行: python usage_example.py")


if __name__ == "__main__":
    quick_demo()