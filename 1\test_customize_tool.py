#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
mootdx 自定义板块操作测试工具类

功能：
1. 测试自定义板块的 CRUD 操作
2. 对板块文件里的股票数据进行 CRUD 操作
3. 提供完整的测试用例和错误处理

作者: Test Tool
日期: 2025-08-06
"""

import os
import sys
import unittest
import tempfile
import shutil
from typing import List, Dict, Any, Optional
from unittest.mock import patch, MagicMock

try:
    from mootdx.tools.customize import Customize
except ImportError:
    print("警告: 无法导入 mootdx.tools.customize.Customize")
    print("请确保已安装 mootdx 库: pip install mootdx")
    Customize = None


class CustomizeTestTool:
    """自定义板块操作测试工具类"""
    
    def __init__(self, tdxdir: str = None):
        """
        初始化测试工具

        Args:
            tdxdir: 通达信安装目录，如果为None则使用模拟模式
        """
        self.tdxdir = tdxdir
        self.custom = None
        self.test_blocks = []  # 存储测试过程中创建的板块名称，用于清理
        self.mock_mode = False

        if Customize and tdxdir and self._validate_tdx_dir(tdxdir):
            try:
                self.custom = Customize(tdxdir=self.tdxdir)
                print(f"使用真实模式，通达信目录: {self.tdxdir}")
            except Exception as e:
                print(f"初始化 Customize 失败: {e}")
                print("切换到模拟模式...")
                self.custom = self._create_mock_customize()
                self.mock_mode = True
        else:
            print("使用模拟模式进行演示...")
            self.custom = self._create_mock_customize()
            self.mock_mode = True
    
    def _validate_tdx_dir(self, tdxdir: str) -> bool:
        """验证通达信目录是否有效"""
        if not tdxdir or not os.path.exists(tdxdir):
            return False

        # 检查是否存在必要的子目录
        blocknew_dir = os.path.join(tdxdir, "T0002", "blocknew")
        return os.path.exists(blocknew_dir)
    
    def _create_mock_customize(self) -> MagicMock:
        """创建模拟的 Customize 对象用于测试"""
        mock = MagicMock()

        # 模拟数据存储
        self._mock_blocks = {}

        def mock_create(name, symbol):
            if name in self._mock_blocks:
                return False  # 板块已存在
            self._mock_blocks[name] = symbol[:]
            return True

        def mock_update(name, symbol):
            if name not in self._mock_blocks:
                return False  # 板块不存在
            self._mock_blocks[name] = symbol[:]
            return True

        def mock_search(name):
            return self._mock_blocks.get(name, None)

        def mock_remove(name):
            if name in self._mock_blocks:
                del self._mock_blocks[name]
                return True
            return False

        mock.create.side_effect = mock_create
        mock.update.side_effect = mock_update
        mock.search.side_effect = mock_search
        mock.remove.side_effect = mock_remove

        return mock
    
    def test_create_block(self, name: str, symbols: List[str]) -> bool:
        """
        测试创建自定义板块
        
        Args:
            name: 板块名称
            symbols: 股票代码列表
            
        Returns:
            bool: 创建是否成功
        """
        print(f"\n=== 测试创建板块: {name} ===")
        print(f"股票代码: {symbols}")
        
        try:
            result = self.custom.create(name=name, symbol=symbols)
            if result:
                self.test_blocks.append(name)
                print(f"✓ 板块 '{name}' 创建成功")
            else:
                print(f"✗ 板块 '{name}' 创建失败")
            return result
        except Exception as e:
            print(f"✗ 创建板块时发生错误: {e}")
            return False
    
    def test_update_block(self, name: str, symbols: List[str]) -> bool:
        """
        测试更新自定义板块
        
        Args:
            name: 板块名称
            symbols: 新的股票代码列表
            
        Returns:
            bool: 更新是否成功
        """
        print(f"\n=== 测试更新板块: {name} ===")
        print(f"新股票代码: {symbols}")
        
        try:
            result = self.custom.update(name=name, symbol=symbols)
            if result:
                print(f"✓ 板块 '{name}' 更新成功")
            else:
                print(f"✗ 板块 '{name}' 更新失败")
            return result
        except Exception as e:
            print(f"✗ 更新板块时发生错误: {e}")
            return False
    
    def test_search_block(self, name: str) -> Optional[List[str]]:
        """
        测试查询自定义板块
        
        Args:
            name: 板块名称
            
        Returns:
            List[str]: 板块中的股票代码列表，失败返回None
        """
        print(f"\n=== 测试查询板块: {name} ===")
        
        try:
            result = self.custom.search(name=name)
            if result:
                print(f"✓ 板块 '{name}' 查询成功")
                print(f"包含股票: {result}")
            else:
                print(f"✗ 板块 '{name}' 不存在或查询失败")
            return result
        except Exception as e:
            print(f"✗ 查询板块时发生错误: {e}")
            return None
    
    def test_remove_block(self, name: str) -> bool:
        """
        测试删除自定义板块
        
        Args:
            name: 板块名称
            
        Returns:
            bool: 删除是否成功
        """
        print(f"\n=== 测试删除板块: {name} ===")
        
        try:
            result = self.custom.remove(name=name)
            if result:
                print(f"✓ 板块 '{name}' 删除成功")
                if name in self.test_blocks:
                    self.test_blocks.remove(name)
            else:
                print(f"✗ 板块 '{name}' 删除失败")
            return result
        except Exception as e:
            print(f"✗ 删除板块时发生错误: {e}")
            return False
    
    def test_stock_crud_operations(self, block_name: str):
        """
        测试对板块中股票的 CRUD 操作
        
        Args:
            block_name: 板块名称
        """
        print(f"\n=== 测试板块 '{block_name}' 中股票的 CRUD 操作 ===")
        
        # 1. 创建板块并添加初始股票
        initial_stocks = ['600036', '600016', '000001']
        self.test_create_block(block_name, initial_stocks)
        
        # 2. 查询板块中的股票
        current_stocks = self.test_search_block(block_name)
        
        # 3. 添加新股票（更新操作）
        if current_stocks:
            new_stocks = current_stocks + ['000002', '600519']
            print(f"\n--- 添加新股票到板块 '{block_name}' ---")
            self.test_update_block(block_name, new_stocks)
        
        # 4. 删除部分股票（更新操作）
        updated_stocks = self.test_search_block(block_name)
        if updated_stocks and len(updated_stocks) > 2:
            reduced_stocks = updated_stocks[:-1]  # 删除最后一个股票
            print(f"\n--- 从板块 '{block_name}' 删除股票 ---")
            self.test_update_block(block_name, reduced_stocks)
        
        # 5. 最终查询验证
        final_stocks = self.test_search_block(block_name)
        print(f"\n最终板块 '{block_name}' 包含股票: {final_stocks}")
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("=" * 60)
        print("开始 mootdx 自定义板块操作综合测试")
        print("=" * 60)
        
        # 测试数据
        test_data = [
            {"name": "龙虎榜", "symbols": ['600036', '600016']},
            {"name": "优质股", "symbols": ['000001', '000002', '600519']},
            {"name": "科技股", "symbols": ['000858', '002415', '300059']}
        ]
        
        # 1. 测试创建多个板块
        print("\n1. 测试创建多个板块")
        for data in test_data:
            self.test_create_block(data["name"], data["symbols"])
        
        # 2. 测试查询所有板块
        print("\n2. 测试查询所有板块")
        for data in test_data:
            self.test_search_block(data["name"])
        
        # 3. 测试更新板块
        print("\n3. 测试更新板块")
        self.test_update_block("龙虎榜", ['600036', '600016', '600519'])
        
        # 4. 测试股票 CRUD 操作
        print("\n4. 测试股票 CRUD 操作")
        self.test_stock_crud_operations("测试CRUD板块")
        
        # 5. 测试重复创建（应该失败）
        print("\n5. 测试重复创建板块")
        self.test_create_block("龙虎榜", ['600036'])  # 应该失败
        
        # 6. 测试删除板块
        print("\n6. 测试删除板块")
        for data in test_data:
            self.test_remove_block(data["name"])
        
        # 清理测试创建的板块
        self.cleanup()
        
        print("\n" + "=" * 60)
        print("综合测试完成")
        print("=" * 60)
    
    def cleanup(self):
        """清理测试过程中创建的板块"""
        print(f"\n=== 清理测试数据 ===")
        for block_name in self.test_blocks[:]:  # 使用切片复制避免修改迭代中的列表
            self.test_remove_block(block_name)

        # 如果使用的是临时目录，删除它
        if self.tdxdir and self.tdxdir.startswith(tempfile.gettempdir()):
            try:
                shutil.rmtree(self.tdxdir)
                print(f"删除临时测试目录: {self.tdxdir}")
            except Exception as e:
                print(f"删除临时目录失败: {e}")


class TestCustomizeUnit(unittest.TestCase):
    """单元测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.tool = CustomizeTestTool()
    
    def tearDown(self):
        """测试后清理"""
        self.tool.cleanup()
    
    def test_create_and_search(self):
        """测试创建和查询板块"""
        result = self.tool.test_create_block("测试板块", ['600036', '600016'])
        self.assertTrue(result)
        
        stocks = self.tool.test_search_block("测试板块")
        self.assertIsNotNone(stocks)
    
    def test_update_block(self):
        """测试更新板块"""
        # 先创建
        self.tool.test_create_block("更新测试", ['600036'])
        
        # 再更新
        result = self.tool.test_update_block("更新测试", ['600036', '600016'])
        self.assertTrue(result)
    
    def test_remove_block(self):
        """测试删除板块"""
        # 先创建
        self.tool.test_create_block("删除测试", ['600036'])
        
        # 再删除
        result = self.tool.test_remove_block("删除测试")
        self.assertTrue(result)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='mootdx 自定义板块操作测试工具')
    parser.add_argument('--tdxdir', type=str, help='通达信安装目录')
    parser.add_argument('--unittest', action='store_true', help='运行单元测试')
    parser.add_argument('--comprehensive', action='store_true', help='运行综合测试')
    
    args = parser.parse_args()
    
    if args.unittest:
        # 运行单元测试
        unittest.main(argv=[''], exit=False)
    elif args.comprehensive or not any([args.unittest]):
        # 运行综合测试（默认）
        tool = CustomizeTestTool(tdxdir=args.tdxdir)
        tool.run_comprehensive_test()
    
    print("\n测试完成！")


if __name__ == "__main__":
    main()
