#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
模拟涨停正股转债查询工具

使用测试数据演示查找最近10天有涨停的正股对应的可转债列表
"""

import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List
import random


class MockLimitUpStockBondQuery:
    """模拟涨停正股转债查询器"""
    
    def __init__(self):
        """初始化查询器"""
        self.bond_stock_mapping = {}
        self.test_data = None
        
        # 加载映射关系和测试数据
        self._load_bond_stock_mapping()
        self._generate_test_data()
    
    def _load_bond_stock_mapping(self):
        """加载可转债正股映射关系"""
        try:
            # 从CSV文件加载映射关系
            mapping_df = pd.read_csv("完整可转债正股映射_20250806_234133.csv", encoding='utf-8-sig')
            
            for _, row in mapping_df.iterrows():
                stock_code = str(row['stock_code']).strip()
                bond_code = str(row['bond_code']).strip()
                bond_name = str(row['bond_name']).strip()
                exchange = str(row.get('exchange', '')).strip()
                pre_close = row.get('pre_close', 0)
                
                if stock_code not in self.bond_stock_mapping:
                    self.bond_stock_mapping[stock_code] = []
                
                self.bond_stock_mapping[stock_code].append({
                    'bond_code': bond_code,
                    'bond_name': bond_name,
                    'exchange': exchange,
                    'pre_close': pre_close
                })
            
            print(f"✓ 加载映射关系: {len(self.bond_stock_mapping)} 只正股对应可转债")
            
        except Exception as e:
            print(f"✗ 加载映射关系失败: {e}")
            self.bond_stock_mapping = {}
    
    def _generate_test_data(self):
        """生成测试涨停数据"""
        
        # 一些有可转债的股票代码
        stocks_with_bonds = [
            '600326',  # 西藏天路 -> 110060 天路转债
            '600036',  # 招商银行 -> 110036 招行转债  
            '300058',  # 蓝色光标 -> 123001 蓝标转债
            '600519',  # 贵州茅台 -> 110020 茅台转债
            '000858',  # 五粮液 -> 123006 五粮液转债
            '600000',  # 浦发银行 -> 110059 浦发转债
            '601988',  # 中国银行 -> 113001 中行转债
        ]
        
        # 一些没有可转债的股票代码
        stocks_without_bonds = [
            '000001',  # 平安银行
            '000002',  # 万科A
            '600001',  # 邯郸钢铁
            '600004',  # 白云机场
        ]
        
        all_stocks = stocks_with_bonds + stocks_without_bonds
        
        # 生成最近10天的测试数据
        test_data = []
        end_date = datetime.now().date()
        
        for i in range(10):
            current_date = end_date - timedelta(days=i)
            
            # 随机选择一些股票涨停
            limit_up_stocks = random.sample(all_stocks, random.randint(2, 5))
            
            for stock in limit_up_stocks:
                # 生成涨停数据
                pre_close = round(random.uniform(10, 100), 2)
                close = round(pre_close * 1.1, 2)  # 涨停10%
                
                test_data.append({
                    'symbol': stock,
                    'day': current_date,
                    'open': round(pre_close * random.uniform(1.05, 1.08), 2),
                    'high': close,
                    'low': round(pre_close * random.uniform(1.02, 1.05), 2),
                    'close': close,
                    'pre_close': pre_close,
                    'volume': random.randint(1000000, 50000000),
                    'limit_up': 1,
                    'change_pct': 10.0
                })
        
        self.test_data = pd.DataFrame(test_data)
        print(f"✓ 生成测试数据: {len(self.test_data)} 条涨停记录")
    
    def get_limit_up_stocks_recent_days(self, days: int = 10) -> pd.DataFrame:
        """
        获取最近N天有涨停的正股
        
        Args:
            days: 查询天数
            
        Returns:
            pd.DataFrame: 涨停股票数据
        """
        if self.test_data is None or self.test_data.empty:
            return pd.DataFrame()
        
        # 计算查询日期范围
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days)
        
        # 筛选日期范围内的数据
        filtered_data = self.test_data[
            (pd.to_datetime(self.test_data['day']).dt.date >= start_date) &
            (pd.to_datetime(self.test_data['day']).dt.date <= end_date) &
            (self.test_data['limit_up'] == 1)
        ].copy()
        
        if not filtered_data.empty:
            print(f"✓ 找到 {len(filtered_data)} 条涨停记录，涉及 {filtered_data['symbol'].nunique()} 只股票")
            return filtered_data.sort_values(['day', 'symbol'], ascending=[False, True])
        else:
            print("✗ 未找到涨停记录")
            return pd.DataFrame()
    
    def get_limit_up_stocks_with_bonds(self, days: int = 10) -> Dict:
        """
        获取最近N天有涨停且有对应可转债的正股
        
        Args:
            days: 查询天数
            
        Returns:
            Dict: 涨停股票及对应可转债信息
        """
        # 获取涨停股票
        limit_up_df = self.get_limit_up_stocks_recent_days(days)
        
        if limit_up_df.empty:
            return {
                'summary': {'total_limit_up': 0, 'with_bonds': 0, 'total_bonds': 0},
                'stocks_with_bonds': [],
                'stocks_without_bonds': []
            }
        
        # 分析涨停股票的可转债情况
        stocks_with_bonds = []
        stocks_without_bonds = []
        total_bonds = 0
        
        # 按股票分组
        for symbol, group in limit_up_df.groupby('symbol'):
            # 获取该股票的涨停记录
            limit_up_days = group.sort_values('day', ascending=False)
            latest_record = limit_up_days.iloc[0]
            
            stock_info = {
                'symbol': symbol,
                'latest_limit_up_date': str(latest_record['day']),
                'latest_close': float(latest_record['close']),
                'latest_change_pct': float(latest_record['change_pct']),
                'limit_up_count': len(limit_up_days),
                'limit_up_dates': [str(d) for d in limit_up_days['day'].tolist()],
                'bonds': []
            }
            
            # 查找对应的可转债
            if symbol in self.bond_stock_mapping:
                bonds = self.bond_stock_mapping[symbol]
                stock_info['bonds'] = bonds
                stocks_with_bonds.append(stock_info)
                total_bonds += len(bonds)
            else:
                stocks_without_bonds.append(stock_info)
        
        # 汇总信息
        summary = {
            'total_limit_up': len(limit_up_df['symbol'].unique()),
            'with_bonds': len(stocks_with_bonds),
            'without_bonds': len(stocks_without_bonds),
            'total_bonds': total_bonds,
            'query_days': days,
            'query_date_range': f"{limit_up_df['day'].min()} 到 {limit_up_df['day'].max()}"
        }
        
        return {
            'summary': summary,
            'stocks_with_bonds': stocks_with_bonds,
            'stocks_without_bonds': stocks_without_bonds
        }
    
    def display_limit_up_bonds_report(self, result: Dict):
        """显示涨停股票可转债报告"""
        summary = result['summary']
        stocks_with_bonds = result['stocks_with_bonds']
        stocks_without_bonds = result['stocks_without_bonds']
        
        print(f"\n{'='*80}")
        print(f"最近{summary['query_days']}天涨停正股转债分析报告")
        print(f"{'='*80}")
        print(f"查询时间范围: {summary['query_date_range']}")
        print(f"涨停股票总数: {summary['total_limit_up']} 只")
        print(f"有对应可转债: {summary['with_bonds']} 只")
        print(f"无对应可转债: {summary['without_bonds']} 只")
        print(f"对应可转债总数: {summary['total_bonds']} 只")
        
        if stocks_with_bonds:
            print(f"\n{'='*80}")
            print(f"🎯 有对应可转债的涨停股票 ({len(stocks_with_bonds)} 只)")
            print(f"{'='*80}")
            
            for i, stock in enumerate(stocks_with_bonds, 1):
                print(f"\n{i}. 📈 {stock['symbol']} - 最新涨停: {stock['latest_limit_up_date']}")
                print(f"   💰 收盘价: {stock['latest_close']:.2f} 元")
                print(f"   📊 涨幅: {stock['latest_change_pct']:.2f}%")
                print(f"   🔥 涨停次数: {stock['limit_up_count']} 次")
                if stock['limit_up_count'] > 1:
                    print(f"   📅 涨停日期: {', '.join(stock['limit_up_dates'])}")
                
                print(f"   🎫 对应可转债 ({len(stock['bonds'])} 只):")
                for bond in stock['bonds']:
                    bond_price = bond.get('pre_close', 0)
                    price_str = f" (价格: {bond_price:.2f})" if bond_price > 0 else ""
                    exchange_icon = "🏢" if bond.get('exchange') == '上海' else "🏬" if bond.get('exchange') == '深圳' else "📍"
                    print(f"     • {bond['bond_code']} {bond['bond_name']} {exchange_icon}{bond.get('exchange', 'N/A')}{price_str}")
        
        if stocks_without_bonds:
            print(f"\n{'='*80}")
            print(f"❌ 无对应可转债的涨停股票 ({len(stocks_without_bonds)} 只)")
            print(f"{'='*80}")
            
            for i, stock in enumerate(stocks_without_bonds, 1):
                print(f"{i:2d}. {stock['symbol']} - {stock['latest_limit_up_date']} "
                      f"(收盘: {stock['latest_close']:.2f}, 涨幅: {stock['latest_change_pct']:.2f}%)")
    
    def export_limit_up_bonds(self, result: Dict, filename: str = None) -> str:
        """导出涨停股票可转债数据"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"涨停正股转债列表_{timestamp}.csv"
        
        try:
            export_data = []
            
            for stock in result['stocks_with_bonds']:
                for bond in stock['bonds']:
                    export_data.append({
                        'stock_symbol': stock['symbol'],
                        'latest_limit_up_date': stock['latest_limit_up_date'],
                        'latest_close': stock['latest_close'],
                        'latest_change_pct': stock['latest_change_pct'],
                        'limit_up_count': stock['limit_up_count'],
                        'bond_code': bond['bond_code'],
                        'bond_name': bond['bond_name'],
                        'bond_exchange': bond.get('exchange', ''),
                        'bond_pre_close': bond.get('pre_close', 0)
                    })
            
            if export_data:
                df = pd.DataFrame(export_data)
                df.to_csv(filename, index=False, encoding='utf-8-sig')
                print(f"✓ 数据已导出到: {filename}")
                return filename
            else:
                print("✗ 无数据可导出")
                return ""
                
        except Exception as e:
            print(f"✗ 导出失败: {e}")
            return ""


def main():
    """主函数"""
    print("🚀 模拟涨停正股转债查询工具")
    print("=" * 50)
    
    try:
        # 初始化查询器
        query_tool = MockLimitUpStockBondQuery()
        
        # 查询最近10天涨停股票的可转债
        print("\n🔍 查询最近10天涨停股票对应的可转债...")
        result = query_tool.get_limit_up_stocks_with_bonds(days=10)
        
        # 显示报告
        query_tool.display_limit_up_bonds_report(result)
        
        # 导出数据
        if result['stocks_with_bonds']:
            print(f"\n{'='*80}")
            print("📁 导出数据")
            print(f"{'='*80}")
            output_file = query_tool.export_limit_up_bonds(result)
            if output_file:
                print(f"✅ 涨停股票可转债数据已导出: {output_file}")
        
        print(f"\n{'='*80}")
        print("✨ 查询完成！")
        print(f"{'='*80}")
        
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
