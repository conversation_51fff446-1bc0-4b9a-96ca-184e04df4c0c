import time
import matplotlib.pyplot as plt
import HP_global as g
import HP_tdx as htdx  # 小白量化行情模块
from HP_formula import *  # 小白量化公式函数模块
import HP_plt as hplt   # 小白量化K线绘图模块

是否按金额交易 = True  #False=按股数 ，True=按金额
每笔金额 = 10000  # 金额
每笔数量 = 200  # 股数

最大买股数量 = 3
买股数量 = 0
滑点 = 0.001
止损幅度 = -0.03
global CLOSE, LOW, HIGH, OPEN, VOL, AMOUNT
global C, L, H, O, V, AMO
可用余额 = {}
持仓数量 = {}
成本价 = {}

hq = htdx.TdxInit(ip='**************', port=7709)  ## 初始化通达信
股票池 = r'D:\new_tdx\T0002\blocknew\zxg8.blk'
#股票池 = r'block/沪深3000107592.blk'   
codes = htdx.getzxgfile(股票池)  # 获取自选股
同花顺委托=r"C:\同花顺软件\同花顺\xiadan.exe"

### 插入自己的股票池


print('股票池数量:',len(codes))
for m, c in codes:
    可用余额[c] = 0
    持仓数量[c] = 0
    成本价[c] = 0

import easytrader
ths = easytrader.use('ths')
ths.connect("C:\\同花顺软件\\同花顺\\xiadan.exe")
ths.enable_type_keys_for_editor()
#print(ths.balance)

持仓 = ths.position
# print(持仓)

import pandas as pd

# 原始数据（已修正格式）
data = 持仓

# 创建DataFrame
df = pd.DataFrame(data)

if len(df) > 0:
    # 数据处理建议：
    # 1. 删除无效列
    # df = df.drop(columns=['Unnamed: 19', '市场代码'])

    # 2. 规范市场名称（处理异常值）
    df['交易市场'] = df['交易市场'].str.replace(' ', '')  # 去除空格
    df.loc[df['交易市场'] == '北京Ａ股', '交易市场'] = '北京A股'  # 统一格式

    # 3. 类型转换示例
    numeric_cols = ['成本价', '市价', '盈亏', '市值']
    df[numeric_cols] = df[numeric_cols].astype(float)

    # 查看结果
    print(df.head(2))  # 展示前两行
    print("\nDataFrame结构:")
    print(df.info())

    for i in range(len(df)):
        code = df.证券代码.iloc[i]
        可用余额[code] = int(df.可用余额.iloc[i])
        持仓数量[code] = int(df.可用余额.iloc[i])
        成本价[code] = float(df.成本价.iloc[i])
        m = htdx.get_market(code)
        if (m, code) not in codes:
            # codes.append((m, code))
            codes.insert(0, (m, code))

myblocks = []

while True:
    nowtime = MACHINETIME()
    if nowtime >= 91500 and nowtime <= 92000:
        pass

    # 9点20分钟到9点25分钟，竞价成交金额大于昨天最大分成交金额的百分之十
    if nowtime >= 92000 and nowtime <= 92500:
        pass

    if nowtime >= 93000 and nowtime < 240000:
        for m, c in codes:
            df = htdx.get_security_bars(nCategory=4, nMarket=m, code=c,
                                        nStart=0, nCount=200)  # 获取指定范围的证券K线

            ## 数据规格化
            df.dropna(inplace=True)
            mydf = initmydf(df)
            C = CLOSE = mydf['close']
            L = LOW = mydf['low']
            H = HIGH = mydf['high']
            O = OPEN = mydf['open']
            V = VOL = mydf['volume']
            AMO = AMOUNT = mydf['amount']

            # 自编公式计算
            SHORT = 12
            LONG = 26
            MID = 9
            CLOSE_MA5 = MA(CLOSE, 5)
            HHV10 = HHV(HIGH, 10)
            VOL_MA5 = MA(VOL, 5)
            DIF = EMA(CLOSE, SHORT) - EMA(CLOSE, LONG)
            DEA = EMA(DIF, MID)
            MACD = 2 * (DIF - DEA)
            CROSS_UP = IF(CROSS(DIF, DEA) > 0, 1, 0)
            ENTERLONG = IF(CLOSE >= HHV10, 1, 0) * IF(VOL >= VOL_MA5, 1, 0) * CROSS_UP
            EXITLONG = IF(CROSS(CLOSE_MA5, CLOSE) > 0, 1, 0)

            if ENTERLONG.iloc[-1] > 0 and 买股数量 < 最大买股数量 and 持仓数量[c] == 0:
                买股数量 = 买股数量 + 1
                price2 = round(C.iloc[-1] * (1 + 滑点), 2)
                if 是否按金额交易:
                    amount = 每笔金额 / price2
                    买入数量 = int(amount / 100) * 100
                else:
                    买入数量 = 每笔数量
                print('买入股数:', 买入数量, c, '  价格:', price2)
                ths.buy(c, price=C.iloc[-1], amount=买入数量)
                可用余额[c] = 0
                持仓数量[c] = 买入数量
                成本价[c] = price2
                time.sleep(2)

            if EXITLONG.iloc[-1] > 0 and 可用余额[c] > 0:
                price2 = round(C.iloc[-1] * (1 - 滑点), 2)
                print('卖出:', c, '  价格:', price2, '  数量:', 可用余额[c])
                ths.sell(c, price2, amount=可用余额[c])
                可用余额[c] = 0
                持仓数量[c] = 0
                成本价[c] = 0
                买股数量 = max(0, 买股数量 - 1)


            if 可用余额[c] > 0 and (C.iloc[-1] - 成本价[c]) / (成本价[c] + 0.000001) <= 止损幅度 and 成本价[c] != 0:
                price2 = round(C.iloc[-1] * (1 - 滑点), 2)
                print('止损:', c, '  价格:', price2, '  数量:', 可用余额[c])
                ths.sell(c, price=price2, amount=可用余额[c])
                可用余额[c] = 0

    print('执行完一轮操作。')
    time.sleep(1)

