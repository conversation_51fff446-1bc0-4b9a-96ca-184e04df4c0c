

import time,os
import pandas as pd
import HP_global as g
import HP_tdx as htdx  # 小白量化行情模块
from HP_formula import *  # 小白量化公式函数模块
import HP_plt as hplt   # 小白量化K线绘图模块



最大买股数量 = 3
买股数量 = 0
每笔数量 = 400
止损幅度 = -0.03
每笔金额 = 10000
滑点 = 0.001

if os.path.isfile('day.dat'):
    最大买股数量,买股数量=loadm('day.dat')


global CLOSE, LOW, HIGH, OPEN, VOL, AMOUNT
global C, L, H, O, V, AMO
可用余额 = {}
持仓数量 = {}
成本价 = {}

hq = htdx.TdxInit(ip='**************', port=7709)  ## 初始化通达信

# 股票池 = 'D:\\new_tdx\\T0002\\blocknew\\zxg88.blk'
股票池 = 'block/沪深3000107592.blk'
codes = htdx.getzxgfile(股票池)  # 获取自选股

### 插入自己的股票池

print(len(codes))
for m, c in codes:
    可用余额[c] = 0
    持仓数量[c] = 0
    成本价[c] = 0

import easytrader
ths = easytrader.use('ths')
ths.connect("C:\\同花顺软件\\同花顺\\xiadan.exe")
ths.enable_type_keys_for_editor()
print(ths.balance)
账户信息=ths.balance
可用资金=可用资金=账户信息['可用金额']
持仓 = ths.position
#print(持仓)



# 原始数据（已修正格式）
data = 持仓

# 创建DataFrame
df = pd.DataFrame(data)

if len(df) > 0:
    # 数据处理建议：
    # 1. 删除无效列
    # df = df.drop(columns=['Unnamed: 19', '市场代码'])

    # 2. 规范市场名称（处理异常值）
    df['交易市场'] = df['交易市场'].str.replace(' ', '')  # 去除空格
    df.loc[df['交易市场'] == '北京Ａ股', '交易市场'] = '北京A股'  # 统一格式

    # 3. 类型转换示例
    numeric_cols = ['成本价', '市价', '盈亏', '市值']
    df[numeric_cols] = df[numeric_cols].astype(float)

    # 查看结果
    print(df.head(2))  # 展示前两行
    print("\nDataFrame结构:")
    print(df.info())

    for i in range(len(df)):
        code = df.证券代码.iloc[i]
        可用余额[code] = int(df.可用余额.iloc[i])
        持仓数量[code] = int(df.可用余额.iloc[i])
        成本价[code] = float(df.成本价.iloc[i])
        m = htdx.get_market(code)
        if (m, code) not in codes:
            # codes.append((m, code))
            codes.insert(0, (m, code))

myblocks = []

时间1=time.time()
保存时间=time.time()
while True:
    if (time.time()-时间1)>10*60:
        账户信息=ths.balance
        可用资金=账户信息['可用金额']
        时间1=time.time()
        持仓 = ths.position
        print(可用资金)
        
        # 原始数据（已修正格式）
        data = 持仓
        
        # 创建DataFrame
        df = pd.DataFrame(data)
        
        if len(df) > 0:
            # 数据处理建议：
            # 1. 删除无效列
            # df = df.drop(columns=['Unnamed: 19', '市场代码'])
        
            # 2. 规范市场名称（处理异常值）
            df['交易市场'] = df['交易市场'].str.replace(' ', '')  # 去除空格
            df.loc[df['交易市场'] == '北京Ａ股', '交易市场'] = '北京A股'  # 统一格式
        
            # 3. 类型转换示例
            numeric_cols = ['成本价', '市价', '盈亏', '市值']
            df[numeric_cols] = df[numeric_cols].astype(float)
        
            # 查看结果
            print(df.head(2))  # 展示前两行
            print("\nDataFrame结构:")
            print(df.info())
        
            for i in range(len(df)):
                code = df.证券代码.iloc[i]
                可用余额[code] = int(df.可用余额.iloc[i])
                持仓数量[code] = int(df.可用余额.iloc[i])
                成本价[code] = float(df.成本价.iloc[i])
                m = htdx.get_market(code)
                if (m, code) not in codes:
                    # codes.append((m, code))
                    codes.insert(0, (m, code))
        

    
    nowtime = MACHINETIME()
    if nowtime >= 91500 and nowtime <= 92000:
        pass

    # 9点20分钟到9点25分钟，竞价成交金额大于昨天最大分成交金额的百分之十
    if nowtime >= 92000 and nowtime <= 92500:
        pass

    if nowtime >= 93000 and nowtime < 240000:
        for m, c in codes:
            df = htdx.get_security_bars(nCategory=4, nMarket=m, code=c,
                                        nStart=0, nCount=200)  # 获取指定范围的证券K线

            ## 数据规格化
            df.dropna(inplace=True)
            mydf = initmydf(df)
            C = CLOSE = mydf['close']
            L = LOW = mydf['low']
            H = HIGH = mydf['high']
            O = OPEN = mydf['open']
            V = VOL = mydf['volume']
            AMO = AMOUNT = mydf['amount']

            # 自编公式计算
            DIF = EMA(CLOSE, 12) - EMA(CLOSE, 26)
            DEA = EMA(DIF, 9)
            MACD = (DIF - DEA) * 2
            RSV = (CLOSE - LLV(LOW, 9)) / (HHV(HIGH, 9) - LLV(LOW, 9)) * 100
            K = SMA(RSV, 3, 1)
            D = SMA(K, 3, 1)
            J = 3 * K - 2 * D
            ENTERLONG = IF(MACD > 0, 1, 0) * IF(CROSS(K, D), 1, 0)
            EXITLONG = IF(CROSS(D, K), 1, 0)

            if ENTERLONG.iloc[-1] > 0 and 买股数量 < 最大买股数量 and 持仓数量[c] == 0  and 可用资金>每笔金额 :
                买股数量 = 买股数量 + 1
                price2 = round(C.iloc[-1] * (1 + 滑点), 2)
                amount = 每笔金额 / price2
                买入数量 = int(amount / 100) * 100                
                
                print('买入股数:', 买股数量, c, '  价格:', C.iloc[-1], '  数量:', 买入数量)
                ths.buy(c, price=C.iloc[-1], amount=买入数量)
                可用余额[c] = 0
                持仓数量[c] = 买入数量
                可用资金=可用资金-买入数量*price2

            if EXITLONG.iloc[-1] > 0 and 可用余额[c] > 0:
                print('卖出:', c, '  价格:', C.iloc[-1], '  数量:', 可用余额[c])
                ths.sell(c, price=C.iloc[-1], amount=可用余额[c])
                可用余额[c] = 0
                可用资金=可用资金+可用余额[c]*C.iloc[-1]

            if 可用余额[c] > 0 and (C.iloc[-1] - 成本价[c]) / (成本价[c] + 0.000001) <= 止损幅度 and 成本价[c] != 0:
                print('止损:', c, '  价格:', C.iloc[-1], '  数量:', 可用余额[c])
                ths.sell(c, price=C.iloc[-1], amount=可用余额[c])
                可用余额[c] = 0
                可用资金=可用资金+可用余额[c]*C.iloc[-1]

    print('执行完一轮操作。')
    time.sleep(1)
    if (time.time()-保存时间)>5*60:
        l=[最大买股数量,买股数量]
        savem('day.dat',l)
        保存时间=time.time()
        





