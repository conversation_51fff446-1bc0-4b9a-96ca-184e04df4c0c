#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
题材数据爬虫
从 https://iiiii.pro/sc/search_stock_from_plate 爬取题材相关股票数据
"""

import requests
import json
import pandas as pd
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('plate_crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class PlateDataCrawler:
    """题材数据爬虫类"""
    
    def __init__(self):
        self.base_url = "https://iiiii.pro/sc/search_stock_from_plate"
        self.session = requests.Session()
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Content-Type': 'application/json',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://iiiii.pro/',
            'Origin': 'https://iiiii.pro'
        })
    
    def get_plate_stocks(self, 
                        plate_name: str,
                        uid: str = "681b0a4992d4437dd4e7e936",
                        zoom_str: str = "5TUD1oCMGx4tV7gmQEEhKkG34MNUwa9m",
                        vip_level: int = 0,
                        plate_type: str = "plate") -> Optional[Dict[str, Any]]:
        """
        获取指定题材的股票数据
        
        Args:
            plate_name: 题材名称，如"汽车芯片"
            uid: 用户ID
            zoom_str: 验证字符串
            vip_level: VIP等级
            plate_type: 类型，默认为"plate"
            
        Returns:
            返回API响应数据，失败返回None
        """
        
        payload = {
            "uid": uid,
            "zoom_str": zoom_str,
            "vip_level": vip_level,
            "plate_name": plate_name,
            "type": plate_type
        }
        
        try:
            logger.info(f"正在获取题材 '{plate_name}' 的股票数据...")
            
            response = self.session.post(
                self.base_url,
                json=payload,
                timeout=30
            )
            
            response.raise_for_status()
            
            data = response.json()
            logger.info(f"成功获取题材 '{plate_name}' 的数据")
            
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"请求失败: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            return None
        except Exception as e:
            logger.error(f"未知错误: {e}")
            return None
    
    def parse_stock_data(self, raw_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        解析股票数据
        
        Args:
            raw_data: 原始API响应数据
            
        Returns:
            解析后的股票列表
        """
        stocks = []
        
        try:
            # 根据实际API响应结构调整解析逻辑
            if 'data' in raw_data:
                stock_list = raw_data['data']
                if isinstance(stock_list, list):
                    for stock in stock_list:
                        stocks.append(stock)
                elif isinstance(stock_list, dict):
                    # 如果data是字典，可能包含stocks字段
                    if 'stocks' in stock_list:
                        stocks = stock_list['stocks']
                    else:
                        stocks = [stock_list]
            else:
                # 如果没有data字段，直接使用原始数据
                stocks = raw_data if isinstance(raw_data, list) else [raw_data]
                
        except Exception as e:
            logger.error(f"解析股票数据失败: {e}")
            
        return stocks
    
    def save_to_csv(self, stocks: List[Dict[str, Any]], plate_name: str) -> str:
        """
        保存数据到CSV文件
        
        Args:
            stocks: 股票数据列表
            plate_name: 题材名称
            
        Returns:
            保存的文件路径
        """
        if not stocks:
            logger.warning("没有股票数据可保存")
            return ""
        
        try:
            # 创建DataFrame
            df = pd.DataFrame(stocks)
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{plate_name}_股票数据_{timestamp}.csv"
            
            # 保存到CSV
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            
            logger.info(f"数据已保存到: {filename}")
            logger.info(f"共保存 {len(stocks)} 条股票数据")
            
            return filename
            
        except Exception as e:
            logger.error(f"保存CSV文件失败: {e}")
            return ""
    
    def crawl_plate_data(self, plate_name: str, save_csv: bool = True) -> Optional[List[Dict[str, Any]]]:
        """
        爬取指定题材的完整数据流程
        
        Args:
            plate_name: 题材名称
            save_csv: 是否保存为CSV文件
            
        Returns:
            股票数据列表
        """
        logger.info(f"开始爬取题材 '{plate_name}' 的数据")
        
        # 获取原始数据
        raw_data = self.get_plate_stocks(plate_name)
        if not raw_data:
            logger.error("获取数据失败")
            return None
        
        # 解析股票数据
        stocks = self.parse_stock_data(raw_data)
        if not stocks:
            logger.warning("没有解析到股票数据")
            return []
        
        # 保存到CSV
        if save_csv:
            self.save_to_csv(stocks, plate_name)
        
        logger.info(f"题材 '{plate_name}' 数据爬取完成，共 {len(stocks)} 只股票")
        
        return stocks


def main():
    """主函数 - 使用示例"""
    
    # 创建爬虫实例
    crawler = PlateDataCrawler()
    
    # 要爬取的题材列表
    plate_names = [
        "汽车芯片",
        "人工智能", 
        "新能源汽车",
        "半导体",
        "5G概念"
    ]
    
    all_results = {}
    
    for plate_name in plate_names:
        print(f"\n{'='*50}")
        print(f"正在爬取题材: {plate_name}")
        print(f"{'='*50}")
        
        # 爬取数据
        stocks = crawler.crawl_plate_data(plate_name)
        
        if stocks:
            all_results[plate_name] = stocks
            print(f"✅ {plate_name}: 成功获取 {len(stocks)} 只股票")
        else:
            print(f"❌ {plate_name}: 获取失败")
        
        # 避免请求过于频繁
        time.sleep(2)
    
    # 输出汇总信息
    print(f"\n{'='*50}")
    print("爬取汇总:")
    print(f"{'='*50}")
    
    total_stocks = 0
    for plate_name, stocks in all_results.items():
        count = len(stocks)
        total_stocks += count
        print(f"{plate_name}: {count} 只股票")
    
    print(f"\n总计: {total_stocks} 只股票")
    print(f"成功爬取 {len(all_results)} 个题材")


if __name__ == "__main__":
    main()
