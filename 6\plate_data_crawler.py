#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
题材数据爬虫
从 https://iiiii.pro/sc/search_stock_from_plate 爬取题材相关股票数据
"""

import requests
import json
import pandas as pd
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
import logging
import sqlite3
import os

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('plate_crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class PlateDataCrawler:
    """题材数据爬虫类"""

    def __init__(self, db_path: str = "plate_stocks.db"):
        self.base_url = "https://iiiii.pro/sc/search_stock_from_plate"
        self.session = requests.Session()
        self.db_path = db_path

        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Content-Type': 'application/json',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://iiiii.pro/',
            'Origin': 'https://iiiii.pro'
        })

        # 初始化数据库
        self.init_database()

    def init_database(self):
        """初始化数据库，创建表结构"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 创建股票数据表，字段与JSON数据结构一致
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS stocks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    _id TEXT,
                    ts_code TEXT,
                    act_ent_type TEXT,
                    act_name TEXT,
                    area TEXT,
                    cnspell TEXT,
                    curr_type TEXT,
                    delist_date TEXT,
                    enname TEXT,
                    exchange TEXT,
                    fullname TEXT,
                    industry TEXT,
                    is_hs TEXT,
                    list_date TEXT,
                    list_status TEXT,
                    market TEXT,
                    name TEXT,
                    symbol TEXT,
                    plates TEXT,
                    plates_info TEXT,
                    T3 INTEGER,
                    plate_name TEXT,
                    crawl_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(ts_code, plate_name)
                )
            ''')

            # 创建题材表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS plates (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    plate_name TEXT UNIQUE,
                    stock_count INTEGER DEFAULT 0,
                    last_crawl_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 创建爬取日志表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS crawl_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    plate_name TEXT,
                    status TEXT,
                    stock_count INTEGER,
                    error_message TEXT,
                    crawl_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            conn.commit()
            conn.close()
            logger.info("数据库初始化完成")

        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")

    def get_plate_stocks(self,
                        plate_name: str,
                        uid: str = "681b0a4992d4437dd4e7e936",
                        zoom_str: str = "5TUD1oCMGx4tV7gmQEEhKkG34MNUwa9m",
                        vip_level: int = 0,
                        plate_type: str = "plate") -> Optional[Dict[str, Any]]:
        """
        获取指定题材的股票数据
        
        Args:
            plate_name: 题材名称，如"汽车芯片"
            uid: 用户ID
            zoom_str: 验证字符串
            vip_level: VIP等级
            plate_type: 类型，默认为"plate"
            
        Returns:
            返回API响应数据，失败返回None
        """
        
        payload = {
            "uid": uid,
            "zoom_str": zoom_str,
            "vip_level": vip_level,
            "plate_name": plate_name,
            "type": plate_type
        }
        
        try:
            logger.info(f"正在获取题材 '{plate_name}' 的股票数据...")
            
            response = self.session.post(
                self.base_url,
                json=payload,
                timeout=30
            )
            
            response.raise_for_status()
            
            data = response.json()
            logger.info(f"成功获取题材 '{plate_name}' 的数据")
            
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"请求失败: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            return None
        except Exception as e:
            logger.error(f"未知错误: {e}")
            return None
    
    def parse_stock_data(self, raw_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        解析股票数据
        
        Args:
            raw_data: 原始API响应数据
            
        Returns:
            解析后的股票列表
        """
        stocks = []
        
        try:
            # 根据实际API响应结构调整解析逻辑
            if 'data' in raw_data:
                stock_list = raw_data['data']
                if isinstance(stock_list, list):
                    for stock in stock_list:
                        stocks.append(stock)
                elif isinstance(stock_list, dict):
                    # 如果data是字典，可能包含stocks字段
                    if 'stocks' in stock_list:
                        stocks = stock_list['stocks']
                    else:
                        stocks = [stock_list]
            else:
                # 如果没有data字段，直接使用原始数据
                stocks = raw_data if isinstance(raw_data, list) else [raw_data]
                
        except Exception as e:
            logger.error(f"解析股票数据失败: {e}")
            
        return stocks

    def save_to_database(self, stocks: List[Dict[str, Any]], plate_name: str) -> bool:
        """
        保存数据到数据库

        Args:
            stocks: 股票数据列表
            plate_name: 题材名称

        Returns:
            保存是否成功
        """
        if not stocks:
            logger.warning("没有股票数据可保存到数据库")
            return False

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 保存股票数据
            saved_count = 0
            for stock in stocks:
                try:
                    # 处理plates_info字段，转换为JSON字符串
                    plates_info_str = json.dumps(stock.get('plates_info', {}), ensure_ascii=False)

                    cursor.execute('''
                        INSERT OR REPLACE INTO stocks (
                            _id, ts_code, act_ent_type, act_name, area, cnspell,
                            curr_type, delist_date, enname, exchange, fullname,
                            industry, is_hs, list_date, list_status, market,
                            name, symbol, plates, plates_info, T3, plate_name
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        stock.get('_id'),
                        stock.get('ts_code'),
                        stock.get('act_ent_type'),
                        stock.get('act_name'),
                        stock.get('area'),
                        stock.get('cnspell'),
                        stock.get('curr_type'),
                        stock.get('delist_date'),
                        stock.get('enname'),
                        stock.get('exchange'),
                        stock.get('fullname'),
                        stock.get('industry'),
                        stock.get('is_hs'),
                        stock.get('list_date'),
                        stock.get('list_status'),
                        stock.get('market'),
                        stock.get('name'),
                        stock.get('symbol'),
                        stock.get('plates'),
                        plates_info_str,
                        stock.get('T3'),
                        plate_name
                    ))
                    saved_count += 1
                except Exception as e:
                    logger.warning(f"保存股票数据失败 {stock.get('symbol', 'Unknown')}: {e}")

            # 更新题材表
            cursor.execute('''
                INSERT OR REPLACE INTO plates (plate_name, stock_count, last_crawl_time)
                VALUES (?, ?, CURRENT_TIMESTAMP)
            ''', (plate_name, saved_count))

            # 记录爬取日志
            cursor.execute('''
                INSERT INTO crawl_logs (plate_name, status, stock_count)
                VALUES (?, ?, ?)
            ''', (plate_name, 'SUCCESS', saved_count))

            conn.commit()
            conn.close()

            logger.info(f"成功保存 {saved_count} 条股票数据到数据库")
            return True

        except Exception as e:
            logger.error(f"保存数据到数据库失败: {e}")

            # 记录错误日志
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO crawl_logs (plate_name, status, stock_count, error_message)
                    VALUES (?, ?, ?, ?)
                ''', (plate_name, 'ERROR', 0, str(e)))
                conn.commit()
                conn.close()
            except:
                pass

            return False

    def save_to_csv(self, stocks: List[Dict[str, Any]], plate_name: str) -> str:
        """
        保存数据到CSV文件
        
        Args:
            stocks: 股票数据列表
            plate_name: 题材名称
            
        Returns:
            保存的文件路径
        """
        if not stocks:
            logger.warning("没有股票数据可保存")
            return ""
        
        try:
            # 创建DataFrame
            df = pd.DataFrame(stocks)
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{plate_name}_股票数据_{timestamp}.csv"
            
            # 保存到CSV
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            
            logger.info(f"数据已保存到: {filename}")
            logger.info(f"共保存 {len(stocks)} 条股票数据")
            
            return filename
            
        except Exception as e:
            logger.error(f"保存CSV文件失败: {e}")
            return ""
    
    def crawl_plate_data(self, plate_name: str, save_csv: bool = True, save_db: bool = True) -> Optional[List[Dict[str, Any]]]:
        """
        爬取指定题材的完整数据流程

        Args:
            plate_name: 题材名称
            save_csv: 是否保存为CSV文件
            save_db: 是否保存到数据库

        Returns:
            股票数据列表
        """
        logger.info(f"开始爬取题材 '{plate_name}' 的数据")

        # 获取原始数据
        raw_data = self.get_plate_stocks(plate_name)
        if not raw_data:
            logger.error("获取数据失败")
            return None

        # 解析股票数据
        stocks = self.parse_stock_data(raw_data)
        if not stocks:
            logger.warning("没有解析到股票数据")
            return []

        # 保存到数据库
        if save_db:
            self.save_to_database(stocks, plate_name)

        # 保存到CSV
        if save_csv:
            self.save_to_csv(stocks, plate_name)

        logger.info(f"题材 '{plate_name}' 数据爬取完成，共 {len(stocks)} 只股票")

        return stocks

    def get_stocks_from_db(self, plate_name: str = None) -> List[Dict[str, Any]]:
        """
        从数据库获取股票数据

        Args:
            plate_name: 题材名称，为None时获取所有数据

        Returns:
            股票数据列表
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            if plate_name:
                cursor.execute('''
                    SELECT * FROM stocks WHERE plate_name = ? ORDER BY crawl_time DESC
                ''', (plate_name,))
            else:
                cursor.execute('''
                    SELECT * FROM stocks ORDER BY crawl_time DESC
                ''')

            columns = [description[0] for description in cursor.description]
            rows = cursor.fetchall()

            stocks = []
            for row in rows:
                stock = dict(zip(columns, row))
                # 解析plates_info JSON字符串
                if stock.get('plates_info'):
                    try:
                        stock['plates_info'] = json.loads(stock['plates_info'])
                    except:
                        pass
                stocks.append(stock)

            conn.close()
            return stocks

        except Exception as e:
            logger.error(f"从数据库获取数据失败: {e}")
            return []

    def get_plates_summary(self) -> List[Dict[str, Any]]:
        """
        获取题材汇总信息

        Returns:
            题材汇总列表
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT plate_name, stock_count, last_crawl_time
                FROM plates
                ORDER BY last_crawl_time DESC
            ''')

            columns = [description[0] for description in cursor.description]
            rows = cursor.fetchall()

            plates = [dict(zip(columns, row)) for row in rows]

            conn.close()
            return plates

        except Exception as e:
            logger.error(f"获取题材汇总失败: {e}")
            return []

    def get_crawl_logs(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取爬取日志

        Args:
            limit: 返回记录数限制

        Returns:
            日志记录列表
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM crawl_logs
                ORDER BY crawl_time DESC
                LIMIT ?
            ''', (limit,))

            columns = [description[0] for description in cursor.description]
            rows = cursor.fetchall()

            logs = [dict(zip(columns, row)) for row in rows]

            conn.close()
            return logs

        except Exception as e:
            logger.error(f"获取爬取日志失败: {e}")
            return []

    def search_stocks(self, keyword: str) -> List[Dict[str, Any]]:
        """
        搜索股票

        Args:
            keyword: 搜索关键词

        Returns:
            匹配的股票列表
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM stocks
                WHERE name LIKE ? OR symbol LIKE ? OR fullname LIKE ? OR industry LIKE ?
                ORDER BY crawl_time DESC
            ''', (f'%{keyword}%', f'%{keyword}%', f'%{keyword}%', f'%{keyword}%'))

            columns = [description[0] for description in cursor.description]
            rows = cursor.fetchall()

            stocks = []
            for row in rows:
                stock = dict(zip(columns, row))
                # 解析plates_info JSON字符串
                if stock.get('plates_info'):
                    try:
                        stock['plates_info'] = json.loads(stock['plates_info'])
                    except:
                        pass
                stocks.append(stock)

            conn.close()
            return stocks

        except Exception as e:
            logger.error(f"搜索股票失败: {e}")
            return []


def main():
    """主函数 - 使用示例"""
    
    # 创建爬虫实例
    crawler = PlateDataCrawler()
    
    # 要爬取的题材列表
    plate_names = [
        "汽车芯片",
        "人工智能", 
        "新能源汽车",
        "半导体",
        "5G概念"
    ]
    
    all_results = {}
    
    for plate_name in plate_names:
        print(f"\n{'='*50}")
        print(f"正在爬取题材: {plate_name}")
        print(f"{'='*50}")
        
        # 爬取数据
        stocks = crawler.crawl_plate_data(plate_name)
        
        if stocks:
            all_results[plate_name] = stocks
            print(f"✅ {plate_name}: 成功获取 {len(stocks)} 只股票")
        else:
            print(f"❌ {plate_name}: 获取失败")
        
        # 避免请求过于频繁
        time.sleep(2)
    
    # 输出汇总信息
    print(f"\n{'='*50}")
    print("爬取汇总:")
    print(f"{'='*50}")
    
    total_stocks = 0
    for plate_name, stocks in all_results.items():
        count = len(stocks)
        total_stocks += count
        print(f"{plate_name}: {count} 只股票")
    
    print(f"\n总计: {total_stocks} 只股票")
    print(f"成功爬取 {len(all_results)} 个题材")


if __name__ == "__main__":
    main()
