#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
东方财富可转债数据爬虫

爬取 https://data.eastmoney.com/xg/xg/?mkt=kzz 的可转债数据并导出CSV
"""

import requests
import pandas as pd
import json
import time
from datetime import datetime
from typing import Dict, List, Optional
import warnings
warnings.filterwarnings('ignore')


class EastMoneyBondSpider:
    """东方财富可转债爬虫"""
    
    def __init__(self):
        """初始化爬虫"""
        self.session = requests.Session()
        self.base_url = "https://datacenter-web.eastmoney.com/api/data/v1/get"
        
        # 设置请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://data.eastmoney.com/',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        }
        self.session.headers.update(self.headers)
        
        # 字段映射
        self.field_mapping = {
            'SECURITY_CODE': '可转债代码',
            'SECURITY_NAME_ABBR': '可转债简称',
            'TRADE_MARKET_CODE': '市场',
            'NEWEST_PRICE': '最新价',
            'CHANGE_RATE': '涨跌幅',
            'VOLUME': '成交量',
            'AMOUNT': '成交额',
            'TURNOVERRATE': '换手率',
            'PE_RATIO': '市盈率',
            'PB_RATIO': '市净率',
            'TOTAL_MARKET_CAP': '总市值',
            'MARKET_CAP': '流通市值',
            'OPEN_PRICE': '开盘价',
            'HIGH_PRICE': '最高价',
            'LOW_PRICE': '最低价',
            'PRE_CLOSE_PRICE': '昨收价',
            'AMPLITUDE': '振幅',
            'VOLUME_RATIO': '量比',
            'LISTING_DATE': '上市日期',
            'CONVERT_STOCK_CODE': '正股代码',
            'CONVERT_STOCK_NAME': '正股简称',
            'CONVERT_STOCK_PRICE': '正股价格',
            'CONVERT_PRICE': '转股价',
            'CONVERT_VALUE': '转股价值',
            'CONVERT_PREMIUM_RATIO': '转股溢价率',
            'RESALE_TRIG_PRICE': '回售触发价',
            'REDEEM_PRICE': '强赎触发价',
            'PB_RATIO_CONVERT': '正股PB',
            'RATING': '评级',
            'MATURITY_DATE': '到期日期',
            'REMAIN_YEAR': '剩余年限',
            'INTEREST_RATE_EXPLAIN': '利率说明',
            'NEXT_PAY_DATE': '下次付息日',
            'BOND_VALUE': '纯债价值',
            'BOND_PREMIUM_RATIO': '纯债溢价率',
            'DBLOW': '双低值',
            'YTMRT': '到期收益率'
        }
    
    def get_bond_data(self, page_size: int = 500, page_num: int = 1) -> Dict:
        """
        获取可转债数据
        
        Args:
            page_size: 每页数据量
            page_num: 页码
            
        Returns:
            Dict: API响应数据
        """
        params = {
            'sortColumns': 'SECURITY_CODE',
            'sortTypes': '1',
            'pageSize': str(page_size),
            'pageNumber': str(page_num),
            'reportName': 'RPT_BOND_CB_LIST',
            'columns': 'ALL',
            'quoteColumns': '',
            'js': '',
            'source': 'WEB',
            'client': 'WEB',
            '_': str(int(time.time() * 1000))
        }
        
        try:
            print(f"正在获取第 {page_num} 页数据...")
            response = self.session.get(self.base_url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            if data.get('success'):
                return data
            else:
                print(f"API返回错误: {data.get('message', '未知错误')}")
                return {}
                
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
            return {}
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            return {}
    
    def get_all_bond_data(self) -> List[Dict]:
        """
        获取所有可转债数据
        
        Returns:
            List[Dict]: 所有可转债数据
        """
        all_data = []
        page_num = 1
        page_size = 500
        
        print("开始获取可转债数据...")
        
        while True:
            # 获取当前页数据
            response_data = self.get_bond_data(page_size=page_size, page_num=page_num)
            
            if not response_data or 'result' not in response_data:
                print(f"第 {page_num} 页数据获取失败，停止爬取")
                break
            
            result = response_data['result']
            if not result or 'data' not in result:
                print(f"第 {page_num} 页无数据，停止爬取")
                break
            
            page_data = result['data']
            if not page_data:
                print(f"第 {page_num} 页数据为空，停止爬取")
                break
            
            all_data.extend(page_data)
            print(f"第 {page_num} 页获取成功，本页 {len(page_data)} 条数据")
            
            # 检查是否还有更多数据
            total_count = result.get('count', 0)
            if len(all_data) >= total_count:
                print(f"已获取全部数据，总计 {len(all_data)} 条")
                break
            
            page_num += 1
            time.sleep(1)  # 避免请求过快
        
        print(f"数据获取完成，总计 {len(all_data)} 条可转债数据")
        return all_data
    
    def process_data(self, raw_data: List[Dict]) -> pd.DataFrame:
        """
        处理原始数据
        
        Args:
            raw_data: 原始数据列表
            
        Returns:
            pd.DataFrame: 处理后的数据
        """
        if not raw_data:
            return pd.DataFrame()
        
        print("正在处理数据...")
        
        processed_data = []
        
        for item in raw_data:
            processed_item = {}
            
            # 处理每个字段
            for eng_field, cn_field in self.field_mapping.items():
                value = item.get(eng_field)
                
                # 数据清洗和格式化
                if value is None or value == '-':
                    processed_item[cn_field] = None
                elif eng_field in ['LISTING_DATE', 'MATURITY_DATE', 'NEXT_PAY_DATE']:
                    # 日期格式处理
                    if isinstance(value, str) and len(value) >= 8:
                        try:
                            processed_item[cn_field] = f"{value[:4]}-{value[4:6]}-{value[6:8]}"
                        except:
                            processed_item[cn_field] = value
                    else:
                        processed_item[cn_field] = value
                elif eng_field in ['NEWEST_PRICE', 'CHANGE_RATE', 'VOLUME', 'AMOUNT', 
                                 'TURNOVERRATE', 'PE_RATIO', 'PB_RATIO', 'TOTAL_MARKET_CAP',
                                 'MARKET_CAP', 'CONVERT_STOCK_PRICE', 'CONVERT_PRICE',
                                 'CONVERT_VALUE', 'CONVERT_PREMIUM_RATIO', 'BOND_VALUE',
                                 'BOND_PREMIUM_RATIO', 'YTMRT']:
                    # 数值字段处理
                    try:
                        processed_item[cn_field] = float(value) if value not in [None, '-', ''] else None
                    except (ValueError, TypeError):
                        processed_item[cn_field] = value
                else:
                    processed_item[cn_field] = value
            
            # 添加爬取时间
            processed_item['数据获取时间'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            processed_data.append(processed_item)
        
        df = pd.DataFrame(processed_data)
        print(f"数据处理完成，共 {len(df)} 条记录，{len(df.columns)} 个字段")
        
        return df
    
    def save_to_csv(self, df: pd.DataFrame, filename: str = None) -> str:
        """
        保存数据到CSV文件
        
        Args:
            df: 数据DataFrame
            filename: 文件名
            
        Returns:
            str: 保存的文件路径
        """
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"东方财富可转债数据_{timestamp}.csv"
        
        try:
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"✓ 数据已保存到: {filename}")
            return filename
        except Exception as e:
            print(f"✗ 保存失败: {e}")
            return ""
    
    def display_summary(self, df: pd.DataFrame):
        """显示数据汇总信息"""
        if df.empty:
            print("无数据")
            return
        
        print(f"\n{'='*80}")
        print(f"东方财富可转债数据汇总")
        print(f"{'='*80}")
        print(f"总记录数: {len(df)}")
        print(f"字段数量: {len(df.columns)}")
        
        # 市场分布
        if '市场' in df.columns:
            market_dist = df['市场'].value_counts()
            print(f"\n市场分布:")
            for market, count in market_dist.items():
                market_name = '上海' if market == 'SH' else '深圳' if market == 'SZ' else market
                print(f"  {market_name}: {count} 只")
        
        # 价格统计
        if '最新价' in df.columns:
            prices = df['最新价'].dropna()
            if not prices.empty:
                print(f"\n价格统计:")
                print(f"  平均价格: {prices.mean():.2f}")
                print(f"  最高价格: {prices.max():.2f}")
                print(f"  最低价格: {prices.min():.2f}")
        
        # 转股溢价率统计
        if '转股溢价率' in df.columns:
            premium = df['转股溢价率'].dropna()
            if not premium.empty:
                print(f"\n转股溢价率统计:")
                print(f"  平均溢价率: {premium.mean():.2f}%")
                print(f"  最高溢价率: {premium.max():.2f}%")
                print(f"  最低溢价率: {premium.min():.2f}%")
        
        # 显示前5条数据
        print(f"\n前5条数据预览:")
        display_cols = ['可转债代码', '可转债简称', '最新价', '涨跌幅', '转股溢价率', '正股简称']
        available_cols = [col for col in display_cols if col in df.columns]
        if available_cols:
            print(df[available_cols].head().to_string(index=False))
    
    def run(self) -> str:
        """
        运行爬虫
        
        Returns:
            str: 保存的文件路径
        """
        print("🚀 东方财富可转债数据爬虫启动")
        print("=" * 50)
        
        try:
            # 1. 获取数据
            raw_data = self.get_all_bond_data()
            
            if not raw_data:
                print("❌ 未获取到任何数据")
                return ""
            
            # 2. 处理数据
            df = self.process_data(raw_data)
            
            if df.empty:
                print("❌ 数据处理失败")
                return ""
            
            # 3. 显示汇总
            self.display_summary(df)
            
            # 4. 保存数据
            filename = self.save_to_csv(df)
            
            if filename:
                print(f"\n✅ 爬取完成！数据已保存到: {filename}")
                return filename
            else:
                print("❌ 数据保存失败")
                return ""
                
        except Exception as e:
            print(f"❌ 爬虫运行失败: {e}")
            import traceback
            traceback.print_exc()
            return ""


def main():
    """主函数"""
    spider = EastMoneyBondSpider()
    result_file = spider.run()
    
    if result_file:
        print(f"\n🎉 爬虫执行成功！")
        print(f"📁 数据文件: {result_file}")
        print(f"📊 可以使用Excel或其他工具打开查看数据")
    else:
        print(f"\n❌ 爬虫执行失败！")


if __name__ == "__main__":
    main()
