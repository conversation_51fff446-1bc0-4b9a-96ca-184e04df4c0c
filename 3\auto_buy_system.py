"""
自动买入系统
实现完全自动化的股票买入功能
"""
import sys
import os
import time
import pyautogui
import pygetwindow as gw
from datetime import datetime
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ths_trader import ThsTrader
from config import get_config

class AutoBuySystem:
    """自动买入系统"""
    
    def __init__(self):
        self.config = get_config()
        self.trader = None
        
        # 设置pyautogui参数
        pyautogui.FAILSAFE = True  # 鼠标移到左上角停止
        pyautogui.PAUSE = 0.5      # 每次操作间隔
        
    def find_ths_window(self):
        """查找同花顺窗口"""
        try:
            windows = gw.getWindowsWithTitle('同花顺')
            if not windows:
                windows = gw.getWindowsWithTitle('买入')
            if not windows:
                windows = gw.getWindowsWithTitle('委托')
            
            if windows:
                return windows[0]
            else:
                print("❌ 未找到同花顺窗口")
                return None
        except Exception as e:
            print(f"❌ 查找窗口失败: {e}")
            return None
    
    def activate_ths_window(self):
        """激活同花顺窗口"""
        window = self.find_ths_window()
        if window:
            try:
                window.activate()
                time.sleep(1)
                print("✅ 同花顺窗口已激活")
                return True
            except Exception as e:
                print(f"❌ 激活窗口失败: {e}")
                return False
        return False
    
    def auto_input_stock_info(self, stock_code, price, amount):
        """自动输入股票信息"""
        try:
            print(f"🤖 开始自动输入股票信息...")
            
            # 方法1: 使用Tab键导航
            print("   使用Tab键导航到输入框...")
            
            # 按Tab键几次，确保焦点在证券代码框
            for i in range(3):
                pyautogui.press('tab')
                time.sleep(0.2)
            
            # 输入股票代码
            print(f"   输入股票代码: {stock_code}")
            pyautogui.hotkey('ctrl', 'a')  # 全选
            time.sleep(0.1)
            pyautogui.write(stock_code)
            time.sleep(0.5)
            pyautogui.press('enter')  # 确认代码
            time.sleep(1)
            
            # 移动到价格输入框
            pyautogui.press('tab')
            time.sleep(0.2)
            
            # 输入买入价格
            print(f"   输入买入价格: {price}")
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.1)
            pyautogui.write(str(price))
            time.sleep(0.5)
            
            # 移动到数量输入框
            pyautogui.press('tab')
            time.sleep(0.2)
            
            # 输入买入数量
            print(f"   输入买入数量: {amount}")
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.1)
            pyautogui.write(str(amount))
            time.sleep(0.5)
            
            print("✅ 股票信息输入完成")
            return True
            
        except Exception as e:
            print(f"❌ 自动输入失败: {e}")
            return False
    
    def auto_click_buy_button(self):
        """自动点击买入按钮"""
        try:
            print("🛒 查找并点击买入按钮...")
            
            # 方法1: 使用快捷键
            try:
                pyautogui.hotkey('alt', 'b')  # 尝试买入快捷键
                time.sleep(1)
                print("✅ 使用快捷键点击买入")
                return True
            except:
                pass
            
            # 方法2: 按Enter键（如果焦点在买入按钮上）
            try:
                pyautogui.press('enter')
                time.sleep(1)
                print("✅ 使用Enter键确认买入")
                return True
            except:
                pass
            
            # 方法3: 查找买入按钮图像（需要截图）
            print("⚠️ 请手动点击买入按钮")
            return False
            
        except Exception as e:
            print(f"❌ 点击买入按钮失败: {e}")
            return False
    
    def auto_buy_stock(self, stock_code, stock_name, price, amount):
        """自动买入股票"""
        print("=" * 60)
        print(f"🤖 自动买入系统启动")
        print("=" * 60)
        
        print(f"📊 交易参数:")
        print(f"   股票代码: {stock_code}")
        print(f"   股票名称: {stock_name}")
        print(f"   买入价格: {price}元")
        print(f"   买入数量: {amount}股")
        print(f"   总金额: {price * amount}元")
        
        # 1. 连接同花顺
        print(f"\n🔗 连接同花顺...")
        self.trader = ThsTrader(self.config)
        
        if not self.trader.login():
            print("❌ 同花顺连接失败")
            return False
        
        print("✅ 同花顺连接成功")
        
        # 2. 激活同花顺窗口
        print(f"\n🖥️ 激活同花顺窗口...")
        if not self.activate_ths_window():
            print("⚠️ 请手动切换到同花顺买入界面")
            input("切换完成后按回车继续...")
        
        # 3. 给用户准备时间
        print(f"\n⏰ 准备自动输入...")
        for i in range(3, 0, -1):
            print(f"   {i}秒后开始...")
            time.sleep(1)
        
        # 4. 自动输入股票信息
        if not self.auto_input_stock_info(stock_code, price, amount):
            print("❌ 自动输入失败，请手动输入")
            return False
        
        # 5. 确认信息
        print(f"\n🔍 请确认同花顺界面中的信息:")
        print(f"   - 证券代码: {stock_code}")
        print(f"   - 证券名称: {stock_name}")
        print(f"   - 买入价格: {price}")
        print(f"   - 买入数量: {amount}")
        
        confirm = input("\n信息正确请输入 'YES' 继续: ").strip()
        
        if confirm != 'YES':
            print("❌ 用户取消买入")
            return False
        
        # 6. 自动点击买入按钮
        if not self.auto_click_buy_button():
            print("⚠️ 请手动点击买入按钮")
            manual_confirm = input("点击完成后按回车确认: ")
        
        print("✅ 买入操作已执行")
        
        # 7. 记录交易
        self.log_trade(stock_code, stock_name, price, amount)
        
        return True
    
    def log_trade(self, stock_code, stock_name, price, amount):
        """记录交易日志"""
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            log_entry = f"{timestamp} - 自动买入 {stock_name}({stock_code}) {amount}股 @{price}元 总额{price * amount}元\n"
            
            with open("auto_trading_log.txt", "a", encoding="utf-8") as f:
                f.write(log_entry)
            
            print(f"📝 交易日志已记录")
        except Exception as e:
            print(f"⚠️ 记录日志失败: {e}")

def auto_buy_000528():
    """自动买入000528"""
    system = AutoBuySystem()
    
    # 股票参数
    stock_code = '000528'
    stock_name = '柳工'
    price = 8.52  # 卖二价
    amount = 100  # 1手
    
    return system.auto_buy_stock(stock_code, stock_name, price, amount)

def auto_buy_custom():
    """自定义自动买入"""
    system = AutoBuySystem()
    
    print("📝 请输入买入参数:")
    stock_code = input("股票代码: ").strip()
    stock_name = input("股票名称: ").strip()
    price = float(input("买入价格: ").strip())
    amount = int(input("买入数量: ").strip())
    
    return system.auto_buy_stock(stock_code, stock_name, price, amount)

def main():
    """主函数"""
    print("🤖 自动买入系统")
    print("=" * 50)
    
    print("请选择操作:")
    print("1. 自动买入000528 (柳工)")
    print("2. 自定义自动买入")
    print("3. 退出")
    
    choice = input("\n请输入选择 (1-3): ").strip()
    
    try:
        if choice == '1':
            success = auto_buy_000528()
        elif choice == '2':
            success = auto_buy_custom()
        elif choice == '3':
            print("👋 程序退出")
            return
        else:
            print("❌ 无效选择")
            return
        
        print("\n" + "=" * 50)
        if success:
            print("🎉 自动买入完成！")
            print("请在同花顺中查看委托状态")
        else:
            print("❌ 自动买入失败")
            print("请检查错误信息或手动完成买入")
        print("=" * 50)
        
    except KeyboardInterrupt:
        print("\n❌ 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")

if __name__ == "__main__":
    main()
