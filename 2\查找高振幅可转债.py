#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
查找高振幅可转债

查找最近30天内振幅较大的可转债，支持多种振幅计算方式
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict

# 导入数据库连接模块
try:
    import pymysql
    import pymysql.cursors
    PYMYSQL_AVAILABLE = True
except ImportError:
    PYMYSQL_AVAILABLE = False


class HighVolatilityBondFinder:
    """高振幅可转债查找器"""
    
    def __init__(self, db_config):
        """初始化"""
        self.db_config = db_config
        self.connection = None
        
        # 加载可转债列表
        self._load_bond_list()
    
    def _load_bond_list(self):
        """加载可转债列表"""
        try:
            # 从完整数据中加载可转债代码
            df = pd.read_csv("../东方财富终极完整可转债_20250807_004218.csv", encoding='utf-8-sig')
            
            # 筛选正常状态的可转债
            normal_bonds = df[df['状态'] == '正常']
            
            # 提取6位可转债代码
            self.bond_codes = []
            for _, row in normal_bonds.iterrows():
                bond_code = str(row['债券代码']).strip()
                if len(bond_code) == 6 and bond_code.isdigit():
                    self.bond_codes.append(bond_code)
            
            print(f"✓ 加载可转债代码: {len(self.bond_codes)} 只")
            
        except Exception as e:
            print(f"✗ 加载可转债列表失败: {e}")
            self.bond_codes = []
    
    def connect_database(self):
        """连接数据库"""
        if not PYMYSQL_AVAILABLE:
            print("✗ 数据库连接失败: 未安装pymysql")
            return False
        
        try:
            import pymysql
            self.connection = pymysql.connect(**self.db_config)
            print(f"✓ 数据库连接成功")
            return True
        except Exception as e:
            print(f"✗ 数据库连接失败: {e}")
            return False
    
    def get_bond_price_data(self, bond_code: str, days: int = 30) -> pd.DataFrame:
        """获取可转债价格数据"""
        
        # 计算日期范围
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days)
        
        # 构建查询SQL
        query = """
        SELECT symbol, day, open, high, low, close, volume
        FROM iquant_daily_price 
        WHERE symbol = %s 
        AND day >= %s 
        AND day <= %s
        ORDER BY day ASC
        """
        
        try:
            # 执行查询
            import pymysql.cursors
            cursor = self.connection.cursor(pymysql.cursors.DictCursor)
            cursor.execute(query, (bond_code, start_date, end_date))
            results = cursor.fetchall()
            cursor.close()
            
            if results:
                df = pd.DataFrame(results)
                return df
            else:
                return pd.DataFrame()
                
        except Exception as e:
            # print(f"✗ 查询 {bond_code} 价格数据失败: {e}")
            return pd.DataFrame()
    
    def calculate_volatility_metrics(self, df: pd.DataFrame) -> Dict:
        """计算多种波动率指标"""
        
        if len(df) < 5:
            return {}
        
        try:
            # 1. 日内振幅平均值
            df['daily_range'] = (df['high'] - df['low']) / df['close'] * 100
            avg_daily_range = df['daily_range'].mean()
            
            # 2. 收盘价标准差
            close_std = df['close'].std()
            close_mean = df['close'].mean()
            close_cv = (close_std / close_mean) * 100 if close_mean > 0 else 0
            
            # 3. 最大单日振幅
            max_daily_range = df['daily_range'].max()
            
            # 4. 价格区间振幅
            price_min = df['low'].min()
            price_max = df['high'].max()
            price_range = (price_max - price_min) / df['close'].iloc[-1] * 100
            
            # 5. 简化ATR (最近14天)
            if len(df) >= 14:
                recent_df = df.tail(14).copy()
                recent_df['prev_close'] = recent_df['close'].shift(1)
                recent_df['tr1'] = recent_df['high'] - recent_df['low']
                recent_df['tr2'] = abs(recent_df['high'] - recent_df['prev_close'])
                recent_df['tr3'] = abs(recent_df['low'] - recent_df['prev_close'])
                recent_df['true_range'] = recent_df[['tr1', 'tr2', 'tr3']].max(axis=1)
                atr = recent_df['true_range'].mean()
                atr_percentage = (atr / df['close'].iloc[-1]) * 100
            else:
                atr_percentage = avg_daily_range
            
            return {
                'avg_daily_range': avg_daily_range,
                'close_cv': close_cv,
                'max_daily_range': max_daily_range,
                'price_range': price_range,
                'atr_percentage': atr_percentage,
                'data_points': len(df),
                'latest_close': df['close'].iloc[-1],
                'latest_date': df['day'].iloc[-1]
            }
            
        except Exception as e:
            print(f"✗ 计算波动率失败: {e}")
            return {}
    
    def find_high_volatility_bonds(self, days: int = 30, threshold: float = 10.0, metric: str = 'atr_percentage') -> List[Dict]:
        """查找高波动率可转债"""
        
        print(f"🔍 查找最近{days}天{metric}>{threshold}%的可转债...")
        
        high_vol_bonds = []
        processed_count = 0
        
        for bond_code in self.bond_codes:
            processed_count += 1
            
            if processed_count % 50 == 0:
                print(f"  已处理: {processed_count}/{len(self.bond_codes)}")
            
            # 获取价格数据
            price_df = self.get_bond_price_data(bond_code, days)
            
            if price_df.empty:
                continue
            
            # 计算波动率指标
            metrics = self.calculate_volatility_metrics(price_df)
            
            if not metrics:
                continue
            
            # 检查是否满足阈值
            if metrics.get(metric, 0) >= threshold:
                bond_info = {
                    'bond_code': bond_code,
                    **metrics
                }
                high_vol_bonds.append(bond_info)
        
        print(f"✓ 处理完成，找到 {len(high_vol_bonds)} 只高波动率可转债")
        return high_vol_bonds
    
    def export_bond_codes(self, high_vol_bonds: List[Dict], metric: str = 'atr_percentage') -> str:
        """导出可转债代码（空格间隔）"""
        
        if not high_vol_bonds:
            print("⚠️  没有找到符合条件的可转债")
            return ""
        
        # 按指定指标降序排列
        sorted_bonds = sorted(high_vol_bonds, key=lambda x: x.get(metric, 0), reverse=True)
        
        # 提取6位代码
        bond_codes = [bond['bond_code'] for bond in sorted_bonds]
        
        # 用空格连接
        codes_string = ' '.join(bond_codes)
        
        # 保存到文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"高振幅可转债代码_{timestamp}.txt"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(codes_string)
        
        print(f"📁 可转债代码已导出: {output_file}")
        print(f"📋 代码列表: {codes_string}")
        
        return codes_string
    
    def display_detailed_results(self, high_vol_bonds: List[Dict], metric: str = 'atr_percentage'):
        """显示详细结果"""
        
        if not high_vol_bonds:
            return
        
        print(f"\n{'='*100}")
        print(f"高波动率可转债详细信息 (按{metric}排序)")
        print(f"{'='*100}")
        
        # 按指定指标降序排列
        sorted_bonds = sorted(high_vol_bonds, key=lambda x: x.get(metric, 0), reverse=True)
        
        print(f"📊 统计信息:")
        print(f"  符合条件数量: {len(sorted_bonds)} 只")
        if sorted_bonds:
            values = [b.get(metric, 0) for b in sorted_bonds]
            print(f"  平均{metric}: {np.mean(values):.2f}%")
            print(f"  最高{metric}: {max(values):.2f}%")
            print(f"  最低{metric}: {min(values):.2f}%")
        
        print(f"\n📋 详细列表:")
        print(f"{'排名':<4} {'代码':<8} {'ATR%':<8} {'日振幅%':<8} {'价格CV%':<8} {'区间振幅%':<10} {'最新价':<8} {'数据点':<6}")
        print("-" * 100)
        
        for i, bond in enumerate(sorted_bonds, 1):
            print(f"{i:<4} {bond['bond_code']:<8} {bond.get('atr_percentage', 0):<8.2f} "
                  f"{bond.get('avg_daily_range', 0):<8.2f} {bond.get('close_cv', 0):<8.2f} "
                  f"{bond.get('price_range', 0):<10.2f} {bond.get('latest_close', 0):<8.2f} "
                  f"{bond.get('data_points', 0):<6}")
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            print("✓ 数据库连接已关闭")


def main():
    """主函数"""
    print("🚀 高振幅可转债查找工具")
    print("=" * 60)
    
    # 数据库配置
    db_config = {
        'host': '***********',
        'port': 3306,
        'user': 'iQuant',
        'password': 'NAAnwaRsb8YGN3F5',
        'database': 'iquant',
        'charset': 'utf8mb4'
    }
    
    try:
        # 初始化查找器
        finder = HighVolatilityBondFinder(db_config)
        
        if not finder.bond_codes:
            print("❌ 未加载到可转债代码")
            return
        
        # 连接数据库
        if not finder.connect_database():
            print("❌ 数据库连接失败")
            return
        
        # 尝试不同的阈值
        thresholds = [20.0, 15.0, 10.0, 8.0, 5.0]
        
        for threshold in thresholds:
            print(f"\n{'='*60}")
            print(f"尝试ATR阈值: {threshold}%")
            print(f"{'='*60}")
            
            # 查找高波动率可转债
            high_vol_bonds = finder.find_high_volatility_bonds(days=30, threshold=threshold, metric='atr_percentage')
            
            if high_vol_bonds:
                # 显示详细结果
                finder.display_detailed_results(high_vol_bonds, 'atr_percentage')
                
                # 导出代码
                codes_string = finder.export_bond_codes(high_vol_bonds, 'atr_percentage')
                
                print(f"\n✅ 查找完成！")
                print(f"📊 找到 {len(high_vol_bonds)} 只ATR>{threshold}%的可转债")
                print(f"📋 代码: {codes_string}")
                break
            else:
                print(f"⚠️  未找到ATR>{threshold}%的可转债")
        
        # 关闭连接
        finder.close()
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
