"""
简单的F12+F1操作测试
验证同花顺的F12+F1快捷键功能
"""
import pyautogui
import time

def simple_f12_f1_test():
    """简单测试F12+F1操作"""
    print("🧪 简单F12+F1操作测试")
    print("=" * 40)
    
    # 设置pyautogui参数
    pyautogui.FAILSAFE = True
    pyautogui.PAUSE = 0.5
    
    print("📋 测试步骤:")
    print("1. 确保同花顺主界面已打开")
    print("2. 程序将按F12打开买入输入口")
    print("3. 然后按F1进入买入输入界面")
    print("4. 最后输入测试数据")
    
    input("\n准备就绪后按回车开始...")
    
    print("\n⏰ 倒计时开始...")
    for i in range(3, 0, -1):
        print(f"   {i}秒后开始...")
        time.sleep(1)
    
    try:
        print("\n🔑 步骤1: 按F12...")
        pyautogui.press('f12')
        time.sleep(2)
        print("✅ 已按F12")
        
        print("🔑 步骤2: 按F1...")
        pyautogui.press('f1')
        time.sleep(2)
        print("✅ 已按F1")
        
        print("🔤 步骤3: 输入测试股票代码 000528...")
        pyautogui.write('000528')
        time.sleep(1)
        print("✅ 已输入股票代码")
        
        print("⏭️ 步骤4: 按Tab移动到下一个输入框...")
        pyautogui.press('tab')
        time.sleep(1)
        print("✅ 已按Tab")
        
        print("💰 步骤5: 输入测试价格 8.52...")
        pyautogui.write('8.52')
        time.sleep(1)
        print("✅ 已输入价格")
        
        print("⏭️ 步骤6: 按Tab移动到下一个输入框...")
        pyautogui.press('tab')
        time.sleep(1)
        print("✅ 已按Tab")
        
        print("📦 步骤7: 输入测试数量 100...")
        pyautogui.write('100')
        time.sleep(1)
        print("✅ 已输入数量")
        
        print("\n🔍 请检查同花顺界面:")
        print("   - 是否显示股票代码: 000528")
        print("   - 是否显示价格: 8.52")
        print("   - 是否显示数量: 100")
        
        result = input("\n信息是否正确显示？(y/n): ").lower().strip()
        
        if result == 'y':
            print("🎉 F12+F1操作测试成功！")
            print("✅ 所有信息都正确输入")
            
            confirm_buy = input("\n是否按回车测试确认买入？(y/n): ").lower().strip()
            
            if confirm_buy == 'y':
                print("🛒 按回车确认...")
                pyautogui.press('enter')
                time.sleep(2)
                print("✅ 已按回车")
                
                # 可能有确认对话框
                print("🔄 如有确认对话框，再次按回车...")
                pyautogui.press('enter')
                time.sleep(1)
                print("✅ 测试完成")
            else:
                print("⚠️ 跳过确认买入测试")
            
            return True
        else:
            print("❌ F12+F1操作测试失败")
            print("💡 可能的问题:")
            print("   1. F12没有打开买入输入口")
            print("   2. F1没有进入买入界面")
            print("   3. 输入框焦点不正确")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程异常: {e}")
        return False

def manual_f12_f1_guide():
    """手动F12+F1操作指导"""
    print("📋 手动F12+F1操作指导")
    print("=" * 40)
    
    print("请按照以下步骤手动操作:")
    print("1. 确保同花顺主界面已打开")
    print("2. 按F12键打开买入输入口")
    print("3. 按F1键进入买入输入界面")
    print("4. 输入股票代码: 000528")
    print("5. 按Tab键移动到价格输入框")
    print("6. 输入买入价格: 8.52")
    print("7. 按Tab键移动到数量输入框")
    print("8. 输入买入数量: 100")
    print("9. 按回车键确认买入")
    
    input("\n手动操作完成后按回车确认...")
    
    result = input("手动操作是否成功？(y/n): ").lower().strip()
    
    if result == 'y':
        print("🎉 手动F12+F1操作成功！")
        print("✅ 现在可以使用自动化脚本")
        return True
    else:
        print("❌ 手动操作失败")
        print("💡 建议:")
        print("   1. 检查同花顺版本是否支持F12+F1")
        print("   2. 确认快捷键设置是否正确")
        print("   3. 尝试重启同花顺")
        return False

def main():
    """主函数"""
    print("🎯 F12+F1操作测试工具")
    print("=" * 50)
    
    print("请选择测试方式:")
    print("1. 自动F12+F1测试")
    print("2. 手动操作指导")
    print("3. 退出")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    try:
        if choice == '1':
            success = simple_f12_f1_test()
        elif choice == '2':
            success = manual_f12_f1_guide()
        elif choice == '3':
            print("👋 程序退出")
            return
        else:
            print("❌ 无效选择")
            return
        
        print("\n" + "=" * 50)
        if success:
            print("🎉 F12+F1测试成功！")
            print("✅ 可以使用完整的自动买入功能")
        else:
            print("❌ F12+F1测试失败")
            print("🔧 需要检查同花顺设置或手动操作")
        print("=" * 50)
        
    except KeyboardInterrupt:
        print("\n❌ 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")

if __name__ == "__main__":
    main()
