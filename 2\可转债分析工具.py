#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
可转债数据分析工具

功能：
1. 可转债行情监控
2. 可转债价值分析
3. 可转债筛选工具
4. 数据导出功能

作者: Bond Analysis Tool
日期: 2025-08-06
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict
import json
import os
from 获取可转债数据 import ConvertibleBondData


class BondAnalyzer:
    """可转债分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.bond_tool = ConvertibleBondData()
        self.data_cache = {}

        # 确保使用真实数据
        if hasattr(self.bond_tool, 'mock_mode') and self.bond_tool.mock_mode:
            raise ConnectionError("无法连接到真实数据服务器，分析工具需要真实数据")
        
    def get_bond_list(self) -> List[str]:
        """获取常见可转债代码列表"""
        # 常见的可转债代码
        bond_codes = [
            # 上海可转债
            '110001', '110003', '110007', '110011', '110020',
            '110030', '110031', '110032', '110033', '110034',
            '110035', '110036', '110037', '110038', '110039',
            '110040', '110041', '110042', '110043', '110044',
            '110045', '110046', '110047', '110048', '110049',
            '110050', '110051', '110052', '110053', '110054',
            '110055', '110056', '110057', '110058', '110059',
            '113001', '113002', '113003', '113004', '113005',
            '113006', '113007', '113008', '113009', '113010',
            '113011', '113012', '113013', '113014', '113015',
            '113016', '113017', '113018', '113019', '113020',
            '113021', '113022', '113023', '113024', '113025',
            '113026', '113027', '113028', '113029', '113030',
            '113031', '113032', '113033', '113034', '113035',
            '113036', '113037', '113038', '113039', '113040',
            '113041', '113042', '113043', '113044', '113045',
            '113046', '113047', '113048', '113049', '113050',
            
            # 深圳可转债
            '123001', '123002', '123003', '123004', '123005',
            '123006', '123007', '123008', '123009', '123010',
            '123011', '123012', '123013', '123014', '123015',
            '123016', '123017', '123018', '123019', '123020',
            '127001', '127002', '127003', '127004', '127005',
            '127006', '127007', '127008', '127009', '127010',
            '127011', '127012', '127013', '127014', '127015',
            '128001', '128002', '128003', '128004', '128005',
            '128006', '128007', '128008', '128009', '128010',
            '128011', '128012', '128013', '128014', '128015',
            '128016', '128017', '128018', '128019', '128020',
            '128021', '128022', '128023', '128024', '128025',
            '128026', '128027', '128028', '128029', '128030',
            '128031', '128032', '128033', '128034', '128035',
            '128036', '128037', '128038', '128039', '128040',
            '128041', '128042', '128043', '128044', '128045',
            '128046', '128047', '128048', '128049', '128050',
            '128051', '128052', '128053', '128054', '128055',
            '128056', '128057', '128058', '128059', '128060',
            '128061', '128062', '128063', '128064', '128065',
            '128066', '128067', '128068', '128069', '128070',
            '128071', '128072', '128073', '128074', '128075',
            '128076', '128077', '128078', '128079', '128080',
            '128081', '128082', '128083', '128084', '128085',
            '128086', '128087', '128088', '128089', '128090',
            '128091', '128092', '128093', '128094', '128095',
            '128096', '128097', '128098', '128099', '128100',
            '128101', '128102', '128103', '128104', '128105',
            '128106', '128107', '128108', '128109', '128110',
            '128111', '128112', '128113', '128114', '128115',
            '128116', '128117', '128118', '128119', '128120',
            '128121', '128122', '128123', '128124', '128125',
            '128126', '128127', '128128', '128129', '128130',
            '128131', '128132', '128133', '128134', '128135',
            '128136', '128137', '128138', '128139', '128140',
        ]
        
        return bond_codes
    
    def monitor_bonds(self, codes: List[str] = None, save_to_file: bool = True) -> pd.DataFrame:
        """
        监控可转债行情
        
        Args:
            codes: 要监控的可转债代码列表，为空则监控所有
            save_to_file: 是否保存到文件
            
        Returns:
            pd.DataFrame: 监控结果
        """
        if codes is None:
            codes = self.get_bond_list()[:20]  # 限制数量避免请求过多
        
        print(f"开始监控 {len(codes)} 只可转债...")
        
        # 获取行情数据
        quotes = self.bond_tool.get_bond_quotes(codes)
        
        if quotes.empty:
            print("未获取到行情数据")
            return pd.DataFrame()
        
        # 数据处理和分析
        if 'price' in quotes.columns:
            # 计算涨跌幅
            if 'last_close' in quotes.columns:
                quotes['change_pct'] = ((quotes['price'] - quotes['last_close']) / quotes['last_close'] * 100).round(2)
            
            # 添加时间戳
            quotes['update_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # 筛选有效数据
            valid_quotes = quotes[quotes['price'] > 0].copy()
            
            if not valid_quotes.empty:
                # 排序
                valid_quotes = valid_quotes.sort_values('change_pct', ascending=False)
                
                # 保存到文件
                if save_to_file:
                    filename = f"可转债行情_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                    valid_quotes.to_csv(filename, index=False, encoding='utf-8-sig')
                    print(f"数据已保存到: {filename}")
                
                return valid_quotes
        
        return quotes
    
    def analyze_bond_value(self, code: str) -> Dict:
        """
        分析单只可转债价值
        
        Args:
            code: 可转债代码
            
        Returns:
            Dict: 分析结果
        """
        print(f"分析可转债 {code} 的投资价值...")
        
        # 获取基本信息
        info = self.bond_tool.get_bond_info(code)
        
        # 获取历史K线
        kline = self.bond_tool.get_bond_kline(code, frequency='1d', count=30)
        
        analysis = {
            'bond_code': code,
            'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'basic_info': info.to_dict('records')[0] if not info.empty else {},
            'price_analysis': {},
            'risk_analysis': {},
            'recommendation': ''
        }
        
        if not kline.empty and 'close' in kline.columns:
            prices = kline['close'].dropna()
            
            if len(prices) > 0:
                current_price = prices.iloc[-1]
                
                # 价格分析
                analysis['price_analysis'] = {
                    'current_price': current_price,
                    'avg_30d': prices.mean().round(2),
                    'max_30d': prices.max(),
                    'min_30d': prices.min(),
                    'volatility': prices.std().round(2),
                    'price_position': ((current_price - prices.min()) / (prices.max() - prices.min()) * 100).round(2)
                }
                
                # 风险分析
                analysis['risk_analysis'] = {
                    'price_volatility': 'high' if prices.std() > 5 else 'medium' if prices.std() > 2 else 'low',
                    'trend': 'up' if prices.iloc[-1] > prices.iloc[-5] else 'down',
                    'support_level': prices.min(),
                    'resistance_level': prices.max()
                }
                
                # 投资建议
                if current_price < 100:
                    analysis['recommendation'] = '价格较低，可考虑买入'
                elif current_price > 130:
                    analysis['recommendation'] = '价格较高，建议谨慎'
                else:
                    analysis['recommendation'] = '价格适中，可持续关注'
        
        return analysis
    
    def screen_bonds(self, criteria: Dict = None) -> pd.DataFrame:
        """
        筛选可转债
        
        Args:
            criteria: 筛选条件
            
        Returns:
            pd.DataFrame: 筛选结果
        """
        if criteria is None:
            criteria = {
                'min_price': 90,
                'max_price': 120,
                'min_volume': 1000
            }
        
        print("开始筛选可转债...")
        
        # 获取所有可转债数据
        all_codes = self.get_bond_list()[:50]  # 限制数量
        quotes = self.bond_tool.get_bond_quotes(all_codes)
        
        if quotes.empty:
            return pd.DataFrame()
        
        # 应用筛选条件
        filtered = quotes.copy()
        
        if 'min_price' in criteria and 'price' in filtered.columns:
            filtered = filtered[filtered['price'] >= criteria['min_price']]
        
        if 'max_price' in criteria and 'price' in filtered.columns:
            filtered = filtered[filtered['price'] <= criteria['max_price']]
        
        if 'min_volume' in criteria and 'vol' in filtered.columns:
            filtered = filtered[filtered['vol'] >= criteria['min_volume']]
        
        # 只保留有效数据
        if 'price' in filtered.columns:
            filtered = filtered[filtered['price'] > 0]
        
        print(f"筛选出 {len(filtered)} 只符合条件的可转债")
        
        return filtered
    
    def export_data(self, data: pd.DataFrame, filename: str = None) -> str:
        """
        导出数据到文件
        
        Args:
            data: 要导出的数据
            filename: 文件名
            
        Returns:
            str: 导出的文件路径
        """
        if filename is None:
            filename = f"可转债数据_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        try:
            # 导出到Excel
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                data.to_excel(writer, sheet_name='可转债数据', index=False)
            
            print(f"数据已导出到: {filename}")
            return filename
            
        except Exception as e:
            # 如果Excel导出失败，尝试CSV
            csv_filename = filename.replace('.xlsx', '.csv')
            data.to_csv(csv_filename, index=False, encoding='utf-8-sig')
            print(f"数据已导出到: {csv_filename}")
            return csv_filename
    
    def close(self):
        """关闭连接"""
        self.bond_tool.close()


def main():
    """主程序"""
    print("=" * 60)
    print("可转债数据分析工具")
    print("=" * 60)
    
    analyzer = BondAnalyzer()
    
    try:
        # 1. 监控热门可转债
        print("\n1. 监控热门可转债行情")
        hot_bonds = ['110001', '113001', '123001', '128013', '128136']
        quotes = analyzer.monitor_bonds(hot_bonds)
        
        if not quotes.empty:
            print("监控结果:")
            print(quotes[['bond_code', 'market', 'price', 'change_pct']].to_string(index=False))
        
        # 2. 分析单只可转债
        print(f"\n2. 分析可转债价值 (以 {hot_bonds[0]} 为例)")
        analysis = analyzer.analyze_bond_value(hot_bonds[0])
        print(f"分析结果: {analysis['recommendation']}")
        
        # 3. 筛选可转债
        print(f"\n3. 筛选符合条件的可转债")
        criteria = {'min_price': 95, 'max_price': 115}
        filtered = analyzer.screen_bonds(criteria)
        
        if not filtered.empty:
            print(f"筛选出 {len(filtered)} 只可转债")
        
        # 4. 导出数据
        if not quotes.empty:
            print(f"\n4. 导出数据")
            filename = analyzer.export_data(quotes)
            print(f"数据已导出: {filename}")
    
    finally:
        analyzer.close()
    
    print(f"\n分析完成！")


if __name__ == "__main__":
    main()
