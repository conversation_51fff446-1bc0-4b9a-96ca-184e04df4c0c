#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
筛选真正的股票可转债

从获取的列表中排除国债、企业债、ETF等，只保留真正的股票可转债
"""

import pandas as pd
import re
from datetime import datetime


class RealConvertibleBondFilter:
    """真正可转债筛选器"""
    
    def __init__(self):
        """初始化筛选器"""
        # 需要排除的名称关键词
        self.exclude_keywords = [
            # 国债/企业债
            '贴债', '国债', '企业债', '公司债', '地方债',
            '铁道', '电力', '石化', '中石油', '中石化',
            '18', '19', '20', '21', '22', '23', '24', '25',  # 年份标识
            
            # ETF和基金
            'ETF', 'etf', 'LOF', 'lof',
            'CX转债', '转债ETF', '可转债ETF',
            
            # 指数
            '上证转债', '深证转债', '国证转债',
            '上证', '深证', '国证', '中证',
            
            # 其他
            '回购', 'GC', 'R-', 'IB',
            '质押', '融资', '融券'
        ]
        
        # 需要排除的代码前缀
        self.exclude_prefixes = [
            '000', '399',  # 指数
            '880', '881', '882', '883', '884', '885',  # 板块指数
            '511', '512', '513', '515', '516', '518', '519',  # ETF基金
            '159', '161', '162', '163', '164', '165', '166', '167', '168', '169',  # ETF基金
            '150', '151',  # 分级基金
            '200', '201', '202', '203', '204',  # 回购
            '131', '132', '133', '134', '135', '136', '137', '138', '139',  # 国债
            '100', '101', '102', '103', '104', '105', '106', '107', '108', '109',  # 国债
            '143', '144',  # 企业债
            '751', '753', '754', '755', '756', '757', '758', '759'  # 申购代码
        ]
        
        # 真正可转债的代码前缀
        self.valid_prefixes = [
            '110', '113', '118',  # 上海可转债
            '123', '127', '128'   # 深圳可转债
        ]
    
    def is_real_convertible_bond(self, code: str, name: str) -> bool:
        """
        判断是否为真正的股票可转债
        
        Args:
            code: 证券代码
            name: 证券名称
            
        Returns:
            bool: 是否为真正的股票可转债
        """
        if not code or not name:
            return False
        
        # 1. 检查代码前缀
        if not any(code.startswith(prefix) for prefix in self.valid_prefixes):
            return False
        
        # 2. 排除特定前缀
        if any(code.startswith(prefix) for prefix in self.exclude_prefixes):
            return False
        
        # 3. 检查名称关键词
        name_clean = name.strip().replace('\x00', '')
        
        # 排除包含特定关键词的
        for keyword in self.exclude_keywords:
            if keyword in name_clean:
                return False
        
        # 4. 检查是否包含"转债"
        if '转债' not in name_clean:
            return False
        
        # 5. 排除年份标识的债券（如"18铁道22"）
        if re.search(r'\d{2}[^\d]*\d{2}', name_clean):
            return False
        
        # 6. 排除纯数字命名的债券
        if re.search(r'^[0-9]+$', name_clean.replace('转债', '')):
            return False
        
        return True
    
    def filter_bonds_from_csv(self, csv_file: str) -> pd.DataFrame:
        """
        从CSV文件中筛选真正的可转债
        
        Args:
            csv_file: CSV文件路径
            
        Returns:
            pd.DataFrame: 筛选后的可转债列表
        """
        try:
            # 读取CSV文件
            df = pd.read_csv(csv_file, encoding='utf-8-sig')
            print(f"读取到 {len(df)} 条记录")
            
            # 筛选真正的可转债
            real_bonds = []
            
            for _, row in df.iterrows():
                code = str(row.get('bond_code', ''))
                name = str(row.get('bond_name', ''))
                
                if self.is_real_convertible_bond(code, name):
                    real_bonds.append({
                        'bond_code': code,
                        'bond_name': name,
                        'exchange': row.get('exchange', ''),
                        'pre_close': row.get('raw_data', {}).get('pre_close', 0) if isinstance(row.get('raw_data'), dict) else 0
                    })
            
            result_df = pd.DataFrame(real_bonds)
            print(f"筛选出 {len(result_df)} 只真正的股票可转债")
            
            return result_df
        
        except Exception as e:
            print(f"筛选失败: {e}")
            return pd.DataFrame()
    
    def display_summary(self, bonds_df: pd.DataFrame):
        """显示筛选结果汇总"""
        if bonds_df.empty:
            print("无数据")
            return
        
        print(f"\n{'='*60}")
        print(f"真正股票可转债汇总")
        print(f"{'='*60}")
        print(f"总数量: {len(bonds_df)} 只")
        
        # 按交易所分组
        if 'exchange' in bonds_df.columns:
            exchange_counts = bonds_df['exchange'].value_counts()
            for exchange, count in exchange_counts.items():
                print(f"{exchange}市场: {count} 只")
        
        # 按代码前缀分组
        if 'bond_code' in bonds_df.columns:
            print(f"\n按代码前缀分布:")
            prefixes = bonds_df['bond_code'].str[:3].value_counts()
            for prefix, count in prefixes.items():
                print(f"  {prefix}xxx: {count} 只")
        
        # 显示前20只
        print(f"\n前20只真正可转债:")
        display_cols = ['bond_code', 'bond_name', 'exchange']
        available_cols = [col for col in display_cols if col in bonds_df.columns]
        
        if available_cols:
            print(bonds_df[available_cols].head(20).to_string(index=False))
    
    def save_real_bonds(self, bonds_df: pd.DataFrame, filename: str = None) -> str:
        """
        保存真正的可转债列表
        
        Args:
            bonds_df: 可转债DataFrame
            filename: 文件名
            
        Returns:
            str: 保存的文件路径
        """
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"真正股票可转债列表_{timestamp}.csv"
        
        try:
            bonds_df.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"✓ 真正可转债列表已保存到: {filename}")
            return filename
        except Exception as e:
            print(f"✗ 保存失败: {e}")
            return ""


def main():
    """主函数"""
    print("筛选真正的股票可转债")
    print("=" * 50)
    
    # 输入文件
    input_file = "沪深可转债列表_20250806_225258.csv"
    
    try:
        # 初始化筛选器
        filter_tool = RealConvertibleBondFilter()
        
        # 筛选真正的可转债
        real_bonds = filter_tool.filter_bonds_from_csv(input_file)
        
        if not real_bonds.empty:
            # 显示汇总信息
            filter_tool.display_summary(real_bonds)
            
            # 保存结果
            output_file = filter_tool.save_real_bonds(real_bonds)
            
            print(f"\n筛选完成！")
            print(f"原始数据: {input_file}")
            print(f"筛选结果: {output_file}")
        else:
            print("未筛选出任何真正的可转债")
    
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
