"""
调试买入问题
分析为什么买入没有触发
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ths_trader import ThsTrader
from config import get_config
from datetime import datetime
import time

def debug_buy_issue():
    """调试买入问题"""
    print("=" * 60)
    print("🔧 买入问题调试")
    print("=" * 60)
    
    # 1. 检查配置
    print("1️⃣ 检查配置...")
    config = get_config()
    print(f"   dry_run模式: {config['trade_config']['dry_run']}")
    print(f"   同花顺路径: {config['account_config']['exe_path']}")
    print(f"   文件是否存在: {os.path.exists(config['account_config']['exe_path'])}")
    
    # 2. 检查交易时间
    print("\n2️⃣ 检查交易时间...")
    trader = ThsTrader(config)
    is_trading_time = trader.is_trading_time()
    print(f"   当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"   是否交易时间: {is_trading_time}")
    
    if not is_trading_time:
        print("   ⚠️ 当前不在交易时间内！")
        print("   交易时间: 9:30-11:30, 13:00-15:00 (工作日)")
    
    # 3. 检查股票代码
    print("\n3️⃣ 检查股票代码...")
    stock_code = '000528'
    is_valid = trader.is_valid_stock_code(stock_code)
    print(f"   股票代码: {stock_code}")
    print(f"   代码有效性: {is_valid}")
    
    # 4. 模拟买入流程
    print("\n4️⃣ 模拟买入流程...")
    
    # 检查买入条件
    print("   检查买入条件:")
    
    # 交易时间检查
    if not is_trading_time:
        print("   ❌ 交易时间检查失败")
        print("   → 这可能是买入没有触发的主要原因！")
    else:
        print("   ✅ 交易时间检查通过")
    
    # 股票代码检查
    if not is_valid:
        print("   ❌ 股票代码检查失败")
    else:
        print("   ✅ 股票代码检查通过")
    
    # 5. 尝试连接同花顺
    print("\n5️⃣ 尝试连接同花顺...")
    try:
        # 不实际连接，只是检查easytrader模块
        import easytrader
        print("   ✅ easytrader模块正常")
        
        # 检查是否有同花顺进程
        import psutil
        ths_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            try:
                if proc.info['name'] and ('同花顺' in proc.info['name'] or 'xiadan' in proc.info['name'].lower()):
                    ths_processes.append(proc.info)
                elif proc.info['exe'] and 'xiadan' in proc.info['exe'].lower():
                    ths_processes.append(proc.info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if ths_processes:
            print(f"   ✅ 发现同花顺进程: {len(ths_processes)} 个")
            for proc in ths_processes:
                print(f"      PID: {proc['pid']}, 名称: {proc['name']}")
        else:
            print("   ⚠️ 未发现同花顺进程")
            
    except Exception as e:
        print(f"   ❌ 检查失败: {e}")
    
    # 6. 分析可能的原因
    print("\n6️⃣ 可能的原因分析:")
    
    reasons = []
    
    if not is_trading_time:
        reasons.append("🕐 当前不在交易时间内")
    
    if not os.path.exists(config['account_config']['exe_path']):
        reasons.append("📁 同花顺客户端路径不正确")
    
    if config['trade_config']['dry_run']:
        reasons.append("🔄 仍在模拟模式（这不是问题，只是说明）")
    
    if not ths_processes:
        reasons.append("💻 同花顺客户端未运行或连接失败")
    
    if reasons:
        for i, reason in enumerate(reasons, 1):
            print(f"   {i}. {reason}")
    else:
        print("   🤔 未发现明显问题，可能是其他原因")
    
    # 7. 解决建议
    print("\n7️⃣ 解决建议:")
    
    if not is_trading_time:
        print("   📅 等待交易时间:")
        print("      - 上午: 9:30-11:30")
        print("      - 下午: 13:00-15:00")
        print("      - 仅工作日有效")
    
    print("   🔧 其他建议:")
    print("      - 手动启动同花顺客户端并登录")
    print("      - 确保账户资金充足")
    print("      - 检查000528是否停牌")
    print("      - 尝试使用32位Python")
    print("      - 以管理员权限运行程序")

def main():
    """主函数"""
    try:
        debug_buy_issue()
        
        print("\n" + "=" * 60)
        print("🔧 调试完成")
        print("\n💡 建议:")
        print("1. 如果是交易时间问题，请在交易时间内重试")
        print("2. 如果是连接问题，请手动启动同花顺后重试")
        print("3. 可以先使用模拟模式测试流程")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 调试过程出错: {e}")

if __name__ == "__main__":
    main()
