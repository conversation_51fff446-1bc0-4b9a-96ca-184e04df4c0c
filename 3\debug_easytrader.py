"""
调试easytrader API
查看可用的方法和属性
"""
import easytrader

def debug_easytrader():
    """调试easytrader"""
    print("=== EasyTrader API 调试 ===")
    
    try:
        # 创建同花顺交易实例
        trader = easytrader.use('ths')
        print(f"✅ 成功创建trader实例: {type(trader)}")
        
        # 查看所有可用方法
        methods = [method for method in dir(trader) if not method.startswith('_')]
        print(f"\n📋 可用方法 ({len(methods)} 个):")
        for i, method in enumerate(methods, 1):
            print(f"  {i:2d}. {method}")
        
        # 检查常用方法
        common_methods = ['prepare', 'connect', 'login', 'buy', 'sell', 'balance', 'position']
        print(f"\n🔍 常用方法检查:")
        for method in common_methods:
            has_method = hasattr(trader, method)
            status = "✅" if has_method else "❌"
            print(f"  {status} {method}")
        
        # 尝试查看文档
        if hasattr(trader, '__doc__') and trader.__doc__:
            print(f"\n📖 文档:")
            print(trader.__doc__)
        
        # 查看版本信息
        print(f"\n📦 EasyTrader 版本: {easytrader.__version__ if hasattr(easytrader, '__version__') else '未知'}")
        
        return trader
        
    except Exception as e:
        print(f"❌ 创建trader失败: {e}")
        return None

def test_methods(trader):
    """测试各种方法"""
    if not trader:
        return
    
    print(f"\n=== 方法测试 ===")
    
    # 测试prepare方法
    if hasattr(trader, 'prepare'):
        print("✅ 找到 prepare 方法")
        try:
            # 获取prepare方法的签名
            import inspect
            sig = inspect.signature(trader.prepare)
            print(f"  prepare 方法签名: {sig}")
        except Exception as e:
            print(f"  无法获取prepare方法签名: {e}")
    
    # 测试connect方法
    if hasattr(trader, 'connect'):
        print("✅ 找到 connect 方法")
        try:
            import inspect
            sig = inspect.signature(trader.connect)
            print(f"  connect 方法签名: {sig}")
        except Exception as e:
            print(f"  无法获取connect方法签名: {e}")
    
    # 查看其他可能的登录方法
    login_methods = [method for method in dir(trader) if 'login' in method.lower() or 'connect' in method.lower() or 'prepare' in method.lower()]
    if login_methods:
        print(f"\n🔐 可能的登录相关方法:")
        for method in login_methods:
            print(f"  - {method}")

def main():
    """主函数"""
    print("EasyTrader API 调试工具")
    print("=" * 50)
    
    trader = debug_easytrader()
    test_methods(trader)
    
    print("\n" + "=" * 50)
    print("调试完成")

if __name__ == '__main__':
    main()
