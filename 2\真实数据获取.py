#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
真实可转债数据获取工具

专门从真实接口获取可转债和正股数据，不使用模拟数据
"""

import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional, Tuple

try:
    from mootdx.quotes import Quotes
    MOOTDX_AVAILABLE = True
except ImportError:
    print("错误: 无法导入 mootdx 库")
    print("请安装: pip install mootdx")
    MOOTDX_AVAILABLE = False
    exit(1)


class RealDataFetcher:
    """真实数据获取器"""
    
    def __init__(self, market='std', timeout=30):
        """
        初始化数据获取器
        
        Args:
            market: 市场类型
            timeout: 超时时间
        """
        self.market = market
        self.timeout = timeout
        self.client = None
        self.connected = False
        
        self._connect()
    
    def _connect(self):
        """连接到数据服务器"""
        try:
            print("正在连接到数据服务器...")
            # 尝试简单连接方式
            self.client = Quotes.factory(
                market=self.market,
                timeout=self.timeout
            )
            self.connected = True
            print("✓ 成功连接到数据服务器")
        except Exception as e:
            print(f"✗ 连接失败: {e}")
            try:
                # 尝试备用连接方式
                print("尝试备用连接方式...")
                self.client = Quotes.factory(market='std')
                self.connected = True
                print("✓ 备用连接成功")
            except Exception as e2:
                print(f"✗ 备用连接也失败: {e2}")
                self.connected = False
                print("警告: 无法连接到数据服务器，将使用离线模式")
    
    def get_bond_quotes(self, bond_code: str) -> Dict:
        """
        获取可转债实时行情

        Args:
            bond_code: 可转债代码

        Returns:
            Dict: 行情数据
        """
        if not self.connected:
            return {'error': '未连接到数据服务器'}
        
        # 判断市场
        if bond_code.startswith(('110', '113', '118')):
            market = 1  # 上海
        elif bond_code.startswith(('123', '127', '128')):
            market = 0  # 深圳
        else:
            raise ValueError(f"无效的可转债代码: {bond_code}")
        
        try:
            quotes = self.client.quotes(symbol=bond_code, market=market)
            if not quotes.empty:
                data = quotes.iloc[0].to_dict()
                data['bond_code'] = bond_code
                data['market'] = '上海' if market == 1 else '深圳'
                data['timestamp'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                return data
            else:
                return {'error': f'未获取到可转债 {bond_code} 的行情数据'}
        except Exception as e:
            return {'error': f'获取可转债 {bond_code} 行情失败: {str(e)}'}
    
    def get_stock_quotes(self, stock_code: str) -> Dict:
        """
        获取正股实时行情

        Args:
            stock_code: 正股代码

        Returns:
            Dict: 行情数据
        """
        if not self.connected:
            return {'error': '未连接到数据服务器'}
        
        # 判断市场
        if stock_code.startswith(('60', '68', '11', '12', '13')):
            market = 1  # 上海
        elif stock_code.startswith(('00', '30', '20')):
            market = 0  # 深圳
        else:
            raise ValueError(f"无效的股票代码: {stock_code}")
        
        try:
            quotes = self.client.quotes(symbol=stock_code, market=market)
            if not quotes.empty:
                data = quotes.iloc[0].to_dict()
                data['stock_code'] = stock_code
                data['market'] = '上海' if market == 1 else '深圳'
                data['timestamp'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                return data
            else:
                return {'error': f'未获取到股票 {stock_code} 的行情数据'}
        except Exception as e:
            return {'error': f'获取股票 {stock_code} 行情失败: {str(e)}'}
    
    def get_bond_kline(self, bond_code: str, frequency='1d', count=100) -> pd.DataFrame:
        """
        获取可转债K线数据
        
        Args:
            bond_code: 可转债代码
            frequency: 数据频率
            count: 数据条数
            
        Returns:
            pd.DataFrame: K线数据
        """
        if not self.connected:
            raise ConnectionError("未连接到数据服务器")
        
        # 判断市场
        if bond_code.startswith(('110', '113', '118')):
            market = 1  # 上海
        elif bond_code.startswith(('123', '127', '128')):
            market = 0  # 深圳
        else:
            raise ValueError(f"无效的可转债代码: {bond_code}")
        
        # 转换频率参数
        freq_map = {
            '1m': 8, '5m': 0, '15m': 1, '30m': 2,
            '1h': 3, '1d': 9, 'daily': 9
        }
        freq_code = freq_map.get(frequency, 9)
        
        try:
            kline = self.client.bars(
                symbol=bond_code,
                market=market,
                frequency=freq_code,
                offset=count
            )
            
            if not kline.empty:
                kline['bond_code'] = bond_code
                kline['market'] = '上海' if market == 1 else '深圳'
                return kline
            else:
                print(f"未获取到可转债 {bond_code} 的K线数据")
                return pd.DataFrame()
        except Exception as e:
            print(f"获取可转债 {bond_code} K线数据失败: {e}")
            return pd.DataFrame()
    
    def get_stock_kline(self, stock_code: str, frequency='1d', count=100) -> pd.DataFrame:
        """
        获取正股K线数据
        
        Args:
            stock_code: 正股代码
            frequency: 数据频率
            count: 数据条数
            
        Returns:
            pd.DataFrame: K线数据
        """
        if not self.connected:
            raise ConnectionError("未连接到数据服务器")
        
        # 判断市场
        if stock_code.startswith(('60', '68', '11', '12', '13')):
            market = 1  # 上海
        elif stock_code.startswith(('00', '30', '20')):
            market = 0  # 深圳
        else:
            raise ValueError(f"无效的股票代码: {stock_code}")
        
        # 转换频率参数
        freq_map = {
            '1m': 8, '5m': 0, '15m': 1, '30m': 2,
            '1h': 3, '1d': 9, 'daily': 9
        }
        freq_code = freq_map.get(frequency, 9)
        
        try:
            kline = self.client.bars(
                symbol=stock_code,
                market=market,
                frequency=freq_code,
                offset=count
            )
            
            if not kline.empty:
                kline['stock_code'] = stock_code
                kline['market'] = '上海' if market == 1 else '深圳'
                return kline
            else:
                print(f"未获取到股票 {stock_code} 的K线数据")
                return pd.DataFrame()
        except Exception as e:
            print(f"获取股票 {stock_code} K线数据失败: {e}")
            return pd.DataFrame()
    
    def batch_get_quotes(self, codes: List[str], code_type='bond') -> pd.DataFrame:
        """
        批量获取行情数据
        
        Args:
            codes: 代码列表
            code_type: 代码类型，'bond' 或 'stock'
            
        Returns:
            pd.DataFrame: 批量行情数据
        """
        results = []
        
        print(f"开始批量获取 {len(codes)} 个{code_type}的行情数据...")
        
        for i, code in enumerate(codes, 1):
            print(f"进度: {i}/{len(codes)} - {code}")
            
            try:
                if code_type == 'bond':
                    data = self.get_bond_quotes(code)
                else:
                    data = self.get_stock_quotes(code)
                
                if 'error' not in data:
                    results.append(data)
                else:
                    print(f"  {data['error']}")
            except Exception as e:
                print(f"  获取 {code} 数据失败: {e}")
        
        if results:
            df = pd.DataFrame(results)
            print(f"成功获取 {len(results)} 条数据")
            return df
        else:
            print("未获取到任何数据")
            return pd.DataFrame()
    
    def close(self):
        """关闭连接"""
        if self.client:
            try:
                self.client.close()
                print("已关闭数据连接")
            except:
                pass
            self.connected = False


def demo_real_data():
    """演示真实数据获取"""
    print("=" * 60)
    print("真实可转债数据获取演示")
    print("=" * 60)
    
    try:
        # 初始化数据获取器
        fetcher = RealDataFetcher()
        
        # 测试可转债代码
        test_bonds = ['128136', '110001', '123001']
        test_stocks = ['002475', '601988', '300058']
        
        print("\n1. 获取可转债实时行情")
        print("-" * 40)
        for bond_code in test_bonds[:2]:  # 只测试前2个
            print(f"\n获取 {bond_code} 行情:")
            quotes = fetcher.get_bond_quotes(bond_code)
            if 'error' not in quotes:
                print(f"  价格: {quotes.get('price', 'N/A')}")
                print(f"  成交量: {quotes.get('vol', 'N/A')}")
                print(f"  时间: {quotes.get('timestamp', 'N/A')}")
            else:
                print(f"  {quotes['error']}")
        
        print("\n2. 获取正股实时行情")
        print("-" * 40)
        for stock_code in test_stocks[:2]:  # 只测试前2个
            print(f"\n获取 {stock_code} 行情:")
            quotes = fetcher.get_stock_quotes(stock_code)
            if 'error' not in quotes:
                print(f"  价格: {quotes.get('price', 'N/A')}")
                print(f"  成交量: {quotes.get('vol', 'N/A')}")
                print(f"  时间: {quotes.get('timestamp', 'N/A')}")
            else:
                print(f"  {quotes['error']}")
        
        print("\n3. 获取K线数据")
        print("-" * 40)
        bond_code = test_bonds[0]
        print(f"获取 {bond_code} 日K线数据 (最近5天):")
        kline = fetcher.get_bond_kline(bond_code, frequency='1d', count=5)
        if not kline.empty:
            print(f"获取到 {len(kline)} 条K线数据")
            if 'close' in kline.columns:
                print(f"最新收盘价: {kline['close'].iloc[-1]}")
        else:
            print("未获取到K线数据")
        
        print("\n4. 批量获取行情")
        print("-" * 40)
        batch_quotes = fetcher.batch_get_quotes(test_bonds, 'bond')
        if not batch_quotes.empty:
            print(f"批量获取成功，共 {len(batch_quotes)} 条数据")
            if 'price' in batch_quotes.columns:
                valid_prices = batch_quotes[batch_quotes['price'] > 0]
                print(f"有效价格数据: {len(valid_prices)} 条")
        
        # 关闭连接
        fetcher.close()
        
    except ConnectionError as e:
        print(f"连接错误: {e}")
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n演示完成！")


if __name__ == "__main__":
    demo_real_data()
