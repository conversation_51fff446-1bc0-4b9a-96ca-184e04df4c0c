#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试可转债正股映射关系

不依赖数据库连接，只测试映射关系加载和查询功能
"""

import pandas as pd
from datetime import datetime, timedelta


class TestBondStockMapping:
    """测试可转债正股映射关系"""
    
    def __init__(self):
        """初始化"""
        self.bond_stock_mapping = {}
        self._load_bond_stock_mapping()
    
    def _load_bond_stock_mapping(self):
        """加载可转债正股映射关系"""
        try:
            # 从CSV文件加载映射关系
            mapping_df = pd.read_csv("2/完整可转债正股映射_20250806_234133.csv", encoding='utf-8-sig')
            
            for _, row in mapping_df.iterrows():
                stock_code = str(row['stock_code']).strip()
                bond_code = str(row['bond_code']).strip()
                bond_name = str(row['bond_name']).strip()
                exchange = str(row.get('exchange', '')).strip()
                pre_close = row.get('pre_close', 0)
                
                if stock_code not in self.bond_stock_mapping:
                    self.bond_stock_mapping[stock_code] = []
                
                self.bond_stock_mapping[stock_code].append({
                    'bond_code': bond_code,
                    'bond_name': bond_name,
                    'exchange': exchange,
                    'pre_close': pre_close
                })
            
            print(f"✓ 加载映射关系: {len(self.bond_stock_mapping)} 只正股对应可转债")
            
        except Exception as e:
            print(f"✗ 加载映射关系失败: {e}")
            self.bond_stock_mapping = {}
    
    def test_specific_stocks(self):
        """测试特定股票的映射关系"""
        print(f"\n{'='*60}")
        print("测试特定股票的可转债映射")
        print(f"{'='*60}")
        
        # 测试股票列表
        test_stocks = [
            ('600326', '西藏天路'),
            ('002475', '立讯精密'),
            ('600036', '招商银行'),
            ('600519', '贵州茅台'),
            ('000858', '五粮液'),
            ('600000', '浦发银行'),
            ('601988', '中国银行'),
            ('300058', '蓝色光标'),
            ('002142', '宁波银行'),
            ('600104', '上汽集团')
        ]
        
        found_count = 0
        total_bonds = 0
        
        for stock_code, stock_name in test_stocks:
            print(f"\n🔍 查询 {stock_code} ({stock_name}):")
            
            if stock_code in self.bond_stock_mapping:
                bonds = self.bond_stock_mapping[stock_code]
                found_count += 1
                total_bonds += len(bonds)
                
                print(f"  ✅ 找到 {len(bonds)} 只对应可转债:")
                for i, bond in enumerate(bonds, 1):
                    price_str = f" (价格: {bond['pre_close']:.2f})" if bond['pre_close'] > 0 else ""
                    print(f"    {i}. {bond['bond_code']} {bond['bond_name']} "
                          f"({bond['exchange']}){price_str}")
            else:
                print(f"  ❌ 未找到对应可转债")
        
        print(f"\n📊 测试结果汇总:")
        print(f"  测试股票数量: {len(test_stocks)}")
        print(f"  有可转债的股票: {found_count}")
        print(f"  总可转债数量: {total_bonds}")
        print(f"  覆盖率: {found_count/len(test_stocks)*100:.1f}%")
    
    def test_bond_to_stock_mapping(self):
        """测试可转债到正股的反向映射"""
        print(f"\n{'='*60}")
        print("测试可转债到正股的反向映射")
        print(f"{'='*60}")
        
        # 创建反向映射
        bond_to_stock = {}
        for stock_code, bonds in self.bond_stock_mapping.items():
            for bond in bonds:
                bond_to_stock[bond['bond_code']] = {
                    'stock_code': stock_code,
                    'bond_name': bond['bond_name'],
                    'exchange': bond['exchange']
                }
        
        print(f"✓ 建立反向映射: {len(bond_to_stock)} 只可转债")
        
        # 测试可转债列表
        test_bonds = [
            '110060',  # 天路转债
            '128136',  # 立讯转债
            '110036',  # 招行转债
            '123001',  # 蓝标转债
            '110059',  # 浦发转债
            '113001',  # 中行转债
            '123006',  # 五粮液转债
            '110020',  # 茅台转债
        ]
        
        found_count = 0
        
        for bond_code in test_bonds:
            print(f"\n🔍 查询 {bond_code}:")
            
            if bond_code in bond_to_stock:
                info = bond_to_stock[bond_code]
                found_count += 1
                print(f"  ✅ {info['bond_name']} → {info['stock_code']} ({info['exchange']})")
            else:
                print(f"  ❌ 未找到对应正股")
        
        print(f"\n📊 反向映射测试结果:")
        print(f"  测试可转债数量: {len(test_bonds)}")
        print(f"  找到正股的可转债: {found_count}")
        print(f"  覆盖率: {found_count/len(test_bonds)*100:.1f}%")
    
    def analyze_mapping_statistics(self):
        """分析映射关系统计"""
        print(f"\n{'='*60}")
        print("映射关系统计分析")
        print(f"{'='*60}")
        
        # 统计信息
        total_stocks = len(self.bond_stock_mapping)
        total_bonds = sum(len(bonds) for bonds in self.bond_stock_mapping.values())
        
        # 按交易所分组
        sh_bonds = 0  # 上海
        sz_bonds = 0  # 深圳
        
        # 按可转债数量分组
        single_bond_stocks = 0  # 只有1只可转债的正股
        multi_bond_stocks = 0   # 有多只可转债的正股
        
        for stock_code, bonds in self.bond_stock_mapping.items():
            if len(bonds) == 1:
                single_bond_stocks += 1
            else:
                multi_bond_stocks += 1
            
            for bond in bonds:
                if bond['exchange'] == '上海':
                    sh_bonds += 1
                elif bond['exchange'] == '深圳':
                    sz_bonds += 1
        
        print(f"📊 基本统计:")
        print(f"  正股数量: {total_stocks}")
        print(f"  可转债数量: {total_bonds}")
        print(f"  平均每只正股对应: {total_bonds/total_stocks:.1f} 只可转债")
        
        print(f"\n📊 交易所分布:")
        print(f"  上海可转债: {sh_bonds} 只 ({sh_bonds/total_bonds*100:.1f}%)")
        print(f"  深圳可转债: {sz_bonds} 只 ({sz_bonds/total_bonds*100:.1f}%)")
        
        print(f"\n📊 正股分布:")
        print(f"  单一可转债正股: {single_bond_stocks} 只 ({single_bond_stocks/total_stocks*100:.1f}%)")
        print(f"  多只可转债正股: {multi_bond_stocks} 只 ({multi_bond_stocks/total_stocks*100:.1f}%)")
        
        # 显示有多只可转债的正股
        if multi_bond_stocks > 0:
            print(f"\n🎯 有多只可转债的正股 (前10只):")
            multi_stocks = [(stock, bonds) for stock, bonds in self.bond_stock_mapping.items() if len(bonds) > 1]
            multi_stocks.sort(key=lambda x: len(x[1]), reverse=True)
            
            for i, (stock_code, bonds) in enumerate(multi_stocks[:10], 1):
                bond_names = [f"{b['bond_code']}({b['bond_name']})" for b in bonds]
                print(f"  {i:2d}. {stock_code}: {len(bonds)} 只可转债")
                print(f"      {', '.join(bond_names)}")


def main():
    """主函数"""
    print("🚀 可转债正股映射关系测试")
    print("="*60)
    
    try:
        # 初始化测试器
        tester = TestBondStockMapping()
        
        if not tester.bond_stock_mapping:
            print("❌ 映射关系加载失败，无法进行测试")
            return
        
        # 测试1: 特定股票查询
        tester.test_specific_stocks()
        
        # 测试2: 反向映射查询
        tester.test_bond_to_stock_mapping()
        
        # 测试3: 统计分析
        tester.analyze_mapping_statistics()
        
        print(f"\n✅ 所有测试完成！")
        print("💡 映射关系正常，可以用于涨停股票转债查询")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
