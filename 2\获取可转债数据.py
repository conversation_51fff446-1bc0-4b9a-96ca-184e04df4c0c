#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
获取沪深可转债数据行情和基本信息

功能：
1. 获取可转债实时行情数据
2. 获取可转债历史K线数据
3. 获取可转债基本信息
4. 支持沪深两市可转债数据

作者: Bond Data Tool
日期: 2025-08-06
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Union
import re
import time

try:
    from mootdx.quotes import Quotes
    MOOTDX_AVAILABLE = True
except ImportError:
    print("警告: 无法导入 mootdx 库")
    print("请安装: pip install mootdx")
    MOOTDX_AVAILABLE = False


class ConvertibleBondData:
    """可转债数据获取工具类"""

    def __init__(self, market='std', timeout=30, verbose=1):
        """
        初始化可转债数据工具

        Args:
            market: 市场类型，'std' 标准市场，'ext' 扩展市场
            timeout: 连接超时时间
            verbose: 日志级别
        """
        self.market = market
        self.timeout = timeout
        self.verbose = verbose
        self.client = None
        self.mock_mode = False

        if not MOOTDX_AVAILABLE:
            raise ImportError("mootdx 库未安装，请运行: pip install mootdx")

        try:
            self.client = Quotes.factory(
                market=market,
                timeout=timeout,
                verbose=verbose
            )
            print(f"✓ 成功连接到 {market} 市场服务器")
        except Exception as e:
            print(f"✗ 连接服务器失败: {e}")
            raise ConnectionError(f"无法连接到数据服务器: {e}")



    def is_convertible_bond(self, code: str) -> bool:
        """
        判断是否为可转债代码

        Args:
            code: 股票代码

        Returns:
            bool: 是否为可转债
        """
        if not code or len(code) != 6:
            return False

        # 上海可转债：110xxx, 113xxx, 118xxx
        # 深圳可转债：123xxx, 127xxx, 128xxx
        bond_prefixes = ['110', '113', '118', '123', '127', '128']
        return code[:3] in bond_prefixes

    def get_bond_market(self, code: str) -> int:
        """
        获取可转债所属市场

        Args:
            code: 可转债代码

        Returns:
            int: 市场代码，1=上海，0=深圳
        """
        if code.startswith(('110', '113', '118')):
            return 1  # 上海
        elif code.startswith(('123', '127', '128')):
            return 0  # 深圳
        else:
            return -1  # 未知

    def get_bond_quotes(self, codes: Union[str, List[str]]) -> pd.DataFrame:
        """
        获取可转债实时行情

        Args:
            codes: 可转债代码或代码列表

        Returns:
            pd.DataFrame: 行情数据
        """
        if isinstance(codes, str):
            codes = [codes]

        # 过滤有效的可转债代码
        valid_codes = [code for code in codes if self.is_convertible_bond(code)]

        if not valid_codes:
            print("没有有效的可转债代码")
            return pd.DataFrame()

        try:
            # 按市场分组获取数据
            sh_codes = [code for code in valid_codes if self.get_bond_market(code) == 1]
            sz_codes = [code for code in valid_codes if self.get_bond_market(code) == 0]

            all_data = []

            # 获取上海可转债数据
            if sh_codes:
                for code in sh_codes:
                    try:
                        data = self.client.quotes(symbol=code, market=1)
                        if not data.empty:
                            data['market'] = '上海'
                            data['bond_code'] = code
                            all_data.append(data)
                    except Exception as e:
                        print(f"获取上海可转债 {code} 数据失败: {e}")

            # 获取深圳可转债数据
            if sz_codes:
                for code in sz_codes:
                    try:
                        data = self.client.quotes(symbol=code, market=0)
                        if not data.empty:
                            data['market'] = '深圳'
                            data['bond_code'] = code
                            all_data.append(data)
                    except Exception as e:
                        print(f"获取深圳可转债 {code} 数据失败: {e}")

            if all_data:
                result = pd.concat(all_data, ignore_index=True)
                return result
            else:
                print("未获取到任何可转债数据")
                return pd.DataFrame()

        except Exception as e:
            print(f"获取可转债行情数据失败: {e}")
            return pd.DataFrame()



    def get_bond_kline(self, code: str, frequency='1d', count=100) -> pd.DataFrame:
        """
        获取可转债K线数据

        Args:
            code: 可转债代码
            frequency: 数据频率，'1m', '5m', '15m', '30m', '1h', '1d'
            count: 获取数据条数

        Returns:
            pd.DataFrame: K线数据
        """
        if not self.is_convertible_bond(code):
            print(f"无效的可转债代码: {code}")
            return pd.DataFrame()

        try:
            market = self.get_bond_market(code)
            if market == -1:
                print(f"无法识别可转债市场: {code}")
                return pd.DataFrame()

            # 转换频率参数
            freq_map = {
                '1m': 8, '5m': 0, '15m': 1, '30m': 2,
                '1h': 3, '1d': 9, 'daily': 9
            }

            freq_code = freq_map.get(frequency, 9)

            data = self.client.bars(
                symbol=code,
                market=market,
                frequency=freq_code,
                offset=count
            )

            if not data.empty:
                data['bond_code'] = code
                data['market'] = '上海' if market == 1 else '深圳'
                # 重新排序列
                columns = ['bond_code', 'market', 'datetime', 'open', 'high',
                          'low', 'close', 'volume', 'amount']
                data = data.reindex(columns=[col for col in columns if col in data.columns])

            return data

        except Exception as e:
            print(f"获取可转债K线数据失败: {e}")
            return pd.DataFrame()



    def get_bond_info(self, codes: Union[str, List[str]]) -> pd.DataFrame:
        """
        获取可转债基本信息

        Args:
            codes: 可转债代码或代码列表

        Returns:
            pd.DataFrame: 基本信息
        """
        if isinstance(codes, str):
            codes = [codes]

        valid_codes = [code for code in codes if self.is_convertible_bond(code)]

        if not valid_codes:
            return pd.DataFrame()

        # 这里可以扩展获取更详细的可转债信息
        # 如转股价格、转股比例、到期日等
        info_data = []

        for code in valid_codes:
            market = self.get_bond_market(code)
            market_name = '上海' if market == 1 else '深圳' if market == 0 else '未知'

            # 获取真实行情数据作为当前价格
            current_price = None
            try:
                quotes = self.get_bond_quotes(code)
                if not quotes.empty and 'price' in quotes.columns:
                    current_price = quotes.iloc[0]['price']
            except:
                pass

            info_data.append({
                'bond_code': code,
                'bond_name': f'转债{code}',
                'market': market_name,
                'market_code': market,
                'current_price': current_price,
                'conversion_price': None,  # 需要从其他接口获取
                'conversion_ratio': None,  # 需要从其他接口获取
                'maturity_date': None,     # 需要从其他接口获取
                'issue_date': None,        # 需要从其他接口获取
                'bond_rating': None,       # 需要从其他接口获取
                'interest_rate': None,     # 需要从其他接口获取
            })

        return pd.DataFrame(info_data)

    def search_bonds(self, keyword: str = None) -> pd.DataFrame:
        """
        搜索可转债

        Args:
            keyword: 搜索关键词

        Returns:
            pd.DataFrame: 搜索结果
        """
        # 真实模式下的搜索逻辑
        # 这里可以实现基于真实数据的搜索功能
        print("搜索功能需要基于真实数据源实现")
        return pd.DataFrame()

    def get_all_bonds_quotes(self) -> pd.DataFrame:
        """
        获取所有可转债行情

        Returns:
            pd.DataFrame: 所有可转债行情数据
        """
        # 真实模式下需要先获取所有可转债代码列表
        # 然后批量获取行情数据
        print("获取所有可转债行情功能需要基于真实数据源实现")
        return pd.DataFrame()

    def close(self):
        """关闭连接"""
        if self.client:
            try:
                self.client.close()
                print("已关闭数据连接")
            except:
                pass


def demo_convertible_bond_data():
    """演示可转债数据获取功能"""
    print("=" * 60)
    print("可转债数据获取演示")
    print("=" * 60)

    # 初始化工具
    bond_tool = ConvertibleBondData()

    # 测试可转债代码
    test_codes = ['110001', '113001', '123001', '128013']

    print(f"\n1. 测试可转债代码识别")
    for code in test_codes:
        is_bond = bond_tool.is_convertible_bond(code)
        market = bond_tool.get_bond_market(code)
        market_name = '上海' if market == 1 else '深圳' if market == 0 else '未知'
        print(f"  {code}: 可转债={is_bond}, 市场={market_name}")

    print(f"\n2. 获取可转债实时行情")
    quotes = bond_tool.get_bond_quotes(test_codes)
    if not quotes.empty:
        print(quotes.to_string(index=False))
    else:
        print("  未获取到行情数据")

    print(f"\n3. 获取可转债K线数据 (以 {test_codes[0]} 为例)")
    kline = bond_tool.get_bond_kline(test_codes[0], frequency='1d', count=5)
    if not kline.empty:
        print(kline.to_string(index=False))
    else:
        print("  未获取到K线数据")

    print(f"\n4. 获取可转债基本信息")
    info = bond_tool.get_bond_info(test_codes)
    if not info.empty:
        print(info.to_string(index=False))
    else:
        print("  未获取到基本信息")

    print(f"\n5. 搜索可转债")
    search_result = bond_tool.search_bonds("转债")
    if not search_result.empty:
        print(search_result.to_string(index=False))
    else:
        print("  未找到相关可转债")

    # 关闭连接
    bond_tool.close()

    print(f"\n演示完成！")


def interactive_bond_tool():
    """交互式可转债工具"""
    print("=" * 60)
    print("交互式可转债数据工具")
    print("=" * 60)

    bond_tool = ConvertibleBondData()

    while True:
        print("\n可用功能:")
        print("1. 获取可转债实时行情")
        print("2. 获取可转债K线数据")
        print("3. 获取可转债基本信息")
        print("4. 搜索可转债")
        print("5. 获取所有可转债行情")
        print("6. 退出")

        choice = input("\n请选择功能 (1-6): ").strip()

        if choice == '1':
            codes_input = input("请输入可转债代码 (多个用空格分隔): ").strip()
            if codes_input:
                codes = codes_input.split()
                quotes = bond_tool.get_bond_quotes(codes)
                if not quotes.empty:
                    print("\n实时行情:")
                    print(quotes.to_string(index=False))
                else:
                    print("未获取到行情数据")

        elif choice == '2':
            code = input("请输入可转债代码: ").strip()
            frequency = input("请输入数据频率 (1m/5m/15m/30m/1h/1d, 默认1d): ").strip() or '1d'
            count = input("请输入获取条数 (默认100): ").strip()
            count = int(count) if count.isdigit() else 100

            if code:
                kline = bond_tool.get_bond_kline(code, frequency=frequency, count=count)
                if not kline.empty:
                    print(f"\n{code} K线数据:")
                    print(kline.to_string(index=False))
                else:
                    print("未获取到K线数据")

        elif choice == '3':
            codes_input = input("请输入可转债代码 (多个用空格分隔): ").strip()
            if codes_input:
                codes = codes_input.split()
                info = bond_tool.get_bond_info(codes)
                if not info.empty:
                    print("\n基本信息:")
                    print(info.to_string(index=False))
                else:
                    print("未获取到基本信息")

        elif choice == '4':
            keyword = input("请输入搜索关键词 (可为空): ").strip()
            search_result = bond_tool.search_bonds(keyword if keyword else None)
            if not search_result.empty:
                print("\n搜索结果:")
                print(search_result.to_string(index=False))
            else:
                print("未找到相关可转债")

        elif choice == '5':
            all_quotes = bond_tool.get_all_bonds_quotes()
            if not all_quotes.empty:
                print("\n所有可转债行情:")
                print(all_quotes.to_string(index=False))
            else:
                print("未获取到行情数据")

        elif choice == '6':
            print("退出程序")
            bond_tool.close()
            break

        else:
            print("无效选择，请重新输入")


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == '--interactive':
        interactive_bond_tool()
    else:
        demo_convertible_bond_data()