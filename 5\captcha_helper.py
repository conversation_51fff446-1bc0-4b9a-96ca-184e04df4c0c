"""
同花顺验证码弹窗处理助手
- 检测验证码弹窗（“提示”对话框，包含“请输入验证码”）
- 将焦点切到弹窗，提示用户在弹窗中输入验证码并点击“确定”
- 等待弹窗关闭后返回

说明：不做自动OCR识别，避免额外依赖；在本机交互输入更稳。
"""
import time
from typing import Optional

try:
    from pywinauto import Desktop, mouse
    from pywinauto.keyboard import send_keys
    PYW_OK = True
except Exception:
    PYW_OK = False


def _get_edit_text(dlg):
    try:
        edit = dlg.child_window(control_type="Edit").wrapper_object()
        # UIA EditWrapper: get_value() 更稳；否则用 window_text()
        if hasattr(edit, 'get_value'):
            return (edit.get_value() or '').strip()
        return (edit.window_text() or '').strip()
    except Exception:
        return ''


def wait_captcha_and_notify(max_wait: int = 90) -> bool:
    """检测验证码弹窗并尽力辅助关闭
    行为：
    - 发现弹窗后聚焦并提示用户输入
    - 轮询编辑框内容，发现长度>=4时自动点击“确定”（或回车）
    - 最终等待弹窗关闭
    返回 True 表示期间检测到弹窗并已关闭；False 表示未检测到弹窗。
    """
    if not PYW_OK:
        return False

    deadline = time.time() + max_wait
    found = False

    while time.time() < deadline:
        try:
            desk = Desktop(backend="uia")
            # 寻找标题包含“提示/验证码”的对话框
            for w in desk.windows():
                title = (w.window_text() or '').strip()
                if not title:
                    continue
                if ('提示' in title) or ('验证码' in title):
                    found = True
                    try:
                        w.set_focus()
                    except Exception:
                        pass
                    print("\n================ 验证码提示 ================")
                    print("请在同花顺弹出的验证码对话框里：")
                    print("1) 在输入框输入验证码  2) 程序会自动点击“确定” 或 回车")
                    remain = int(max(0, deadline - time.time()))
                    print(f"(等待你完成，超时 {remain}s)")

                    last_enter = 0
                    # 轮询：读取 Edit 文本；若长度>=4 则自动点击确定
                    while time.time() < deadline:
                        try:
                            if not w.exists():
                                print("验证码对话框已关闭，继续...\n")
                                return True

                            txt = _get_edit_text(w)
                            if txt and len(txt) >= 4:
                                # 找“确定/确认/OK/Yes/是”等按钮
                                try:
                                    ok_btn = w.child_window(title_re="确定|确定\(&O\)|确定\(&Y\)|确认|确认\(&Y\)|OK|Yes|是|好", control_type="Button").wrapper_object()
                                    ok_btn.click_input()
                                except Exception:
                                    # 尝试点击任意按钮（第一个）
                                    try:
                                        any_btn = w.child_window(control_type="Button").wrapper_object()
                                        any_btn.click_input()
                                    except Exception:
                                        # 发送多组快捷键兜底
                                        for keys in ('{ENTER}', '%o', '%y', '{ESC}'):
                                            try:
                                                send_keys(keys)
                                                time.sleep(0.1)
                                            except Exception:
                                                pass
                                time.sleep(0.8)
                                # 仍未关闭则尝试坐标点击（底部中间）
                                try:
                                    rect = w.rectangle()
                                    x = int((rect.left + rect.right) / 2)
                                    y = int(rect.bottom - 35)
                                    mouse.click(button='left', coords=(x, y))
                                    time.sleep(0.6)
                                except Exception:
                                    pass
                                # 再次检查是否关闭
                                if not w.exists():
                                    print("已自动确认验证码，继续...\n")
                                    return True

                            # 每3秒尝试回车一次兜底
                            if time.time() - last_enter > 3:
                                try:
                                    send_keys('{ENTER}')
                                    last_enter = time.time()
                                except Exception:
                                    pass
                        except Exception:
                            return True
                        time.sleep(0.5)
        except Exception:
            pass
        time.sleep(0.8)

    return found


def force_close_captcha(scans: int = 3, interval: float = 0.5) -> bool:
    """再次尝试强制关闭验证码对话框（收尾用）
    - 扫描多次，若发现包含“提示/验证码”的对话框，点击“确定”或发送回车/ESC
    - 返回 True 表示期间至少关闭过一次
    """
    if not PYW_OK:
        return False

    closed = False
    for _ in range(max(1, scans)):
        try:
            for backend in ("uia", "win32"):
                try:
                    desk = Desktop(backend=backend)
                except Exception:
                    continue
                for w in desk.windows():
                    title = (w.window_text() or '').strip()
                    if not title:
                        continue
                    if ('提示' in title) or ('验证码' in title):
                        try:
                            w.set_focus()
                        except Exception:
                            pass
                        # 尝试点击“确定/确认/OK/Yes/是”等按钮
                        try:
                            ok_btn = w.child_window(title_re="确定|确定\(&O\)|确定\(&Y\)|确认|确认\(&Y\)|OK|Yes|是|好", control_type="Button").wrapper_object()
                            ok_btn.click_input()
                            time.sleep(0.3)
                        except Exception:
                            # 尝试点击任意按钮（第一个）
                            try:
                                any_btn = w.child_window(control_type="Button").wrapper_object()
                                any_btn.click_input()
                                time.sleep(0.2)
                            except Exception:
                                pass
                            # 回车 / Alt+O / Alt+Y / ESC 兜底
                            for keys in ('{ENTER}', '%o', '%y', '{ESC}'):
                                try:
                                    send_keys(keys)
                                    time.sleep(0.2)
                                except Exception:
                                    pass
                            # 坐标兜底点击（底部中间）
                            try:
                                rect = w.rectangle()
                                x = int((rect.left + rect.right) / 2)
                                y = int(rect.bottom - 35)
                                mouse.click(button='left', coords=(x, y))
                                time.sleep(0.2)
                            except Exception:
                                pass
                        # 检查是否关闭
                        try:
                            if not w.exists():
                                closed = True
                        except Exception:
                            closed = True
        except Exception:
            pass
        time.sleep(interval)
    return closed

