"""
HP_plt.py - 小白量化绘图模块替代实现
提供K线图和技术指标绘图功能
"""
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
import pandas as pd
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def plot_kline(df, title="K线图", figsize=(12, 8)):
    """
    绘制K线图
    
    Args:
        df: 包含OHLC数据的DataFrame
        title: 图表标题
        figsize: 图表大小
    """
    fig, ax = plt.subplots(figsize=figsize)
    
    # 确保数据列存在
    required_cols = ['open', 'high', 'low', 'close']
    for col in required_cols:
        if col not in df.columns:
            print(f"缺少必要列: {col}")
            return
    
    # 准备数据
    dates = df.index if 'datetime' not in df.columns else df['datetime']
    opens = df['open']
    highs = df['high']
    lows = df['low']
    closes = df['close']
    
    # 绘制K线
    for i in range(len(df)):
        date = i
        open_price = opens.iloc[i]
        high_price = highs.iloc[i]
        low_price = lows.iloc[i]
        close_price = closes.iloc[i]
        
        # 判断涨跌
        color = 'red' if close_price >= open_price else 'green'
        
        # 绘制影线
        ax.plot([date, date], [low_price, high_price], color='black', linewidth=1)
        
        # 绘制实体
        height = abs(close_price - open_price)
        bottom = min(open_price, close_price)
        
        rect = Rectangle((date - 0.3, bottom), 0.6, height, 
                        facecolor=color, edgecolor='black', alpha=0.8)
        ax.add_patch(rect)
    
    ax.set_title(title)
    ax.set_xlabel('时间')
    ax.set_ylabel('价格')
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

def plot_with_indicators(df, indicators=None, title="股价与技术指标", figsize=(12, 10)):
    """
    绘制股价和技术指标
    
    Args:
        df: 包含价格数据的DataFrame
        indicators: 技术指标字典 {'指标名': Series}
        title: 图表标题
        figsize: 图表大小
    """
    if indicators is None:
        indicators = {}
    
    # 创建子图
    fig, axes = plt.subplots(2, 1, figsize=figsize, height_ratios=[3, 1])
    
    # 主图：价格
    ax1 = axes[0]
    if 'close' in df.columns:
        ax1.plot(df.index, df['close'], label='收盘价', linewidth=2)
    
    # 添加技术指标到主图
    main_indicators = ['MA5', 'MA10', 'MA20', 'EMA12', 'EMA26', 'BOLL_UPPER', 'BOLL_MIDDLE', 'BOLL_LOWER']
    for name, series in indicators.items():
        if name in main_indicators:
            ax1.plot(df.index, series, label=name, alpha=0.7)
    
    ax1.set_title(title)
    ax1.set_ylabel('价格')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 副图：其他指标
    ax2 = axes[1]
    sub_indicators = ['MACD', 'RSI', 'KDJ_K', 'KDJ_D', 'KDJ_J']
    for name, series in indicators.items():
        if name in sub_indicators:
            ax2.plot(df.index, series, label=name, alpha=0.7)
    
    ax2.set_xlabel('时间')
    ax2.set_ylabel('指标值')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

def plot_macd(df, dif, dea, macd, title="MACD指标", figsize=(12, 6)):
    """
    绘制MACD指标
    
    Args:
        df: 价格数据
        dif: DIF线
        dea: DEA线
        macd: MACD柱状图
        title: 图表标题
        figsize: 图表大小
    """
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=figsize, height_ratios=[2, 1])
    
    # 上图：价格
    if 'close' in df.columns:
        ax1.plot(df.index, df['close'], label='收盘价', color='black')
    ax1.set_title(title)
    ax1.set_ylabel('价格')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 下图：MACD
    ax2.plot(df.index, dif, label='DIF', color='blue')
    ax2.plot(df.index, dea, label='DEA', color='red')
    
    # MACD柱状图
    colors = ['red' if x >= 0 else 'green' for x in macd]
    ax2.bar(df.index, macd, color=colors, alpha=0.6, label='MACD')
    
    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    ax2.set_xlabel('时间')
    ax2.set_ylabel('MACD')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

def plot_kdj(df, k, d, j, title="KDJ指标", figsize=(12, 6)):
    """
    绘制KDJ指标
    
    Args:
        df: 价格数据
        k: K线
        d: D线
        j: J线
        title: 图表标题
        figsize: 图表大小
    """
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=figsize, height_ratios=[2, 1])
    
    # 上图：价格
    if 'close' in df.columns:
        ax1.plot(df.index, df['close'], label='收盘价', color='black')
    ax1.set_title(title)
    ax1.set_ylabel('价格')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 下图：KDJ
    ax2.plot(df.index, k, label='K', color='blue')
    ax2.plot(df.index, d, label='D', color='red')
    ax2.plot(df.index, j, label='J', color='green')
    
    # 添加超买超卖线
    ax2.axhline(y=80, color='red', linestyle='--', alpha=0.5, label='超买线')
    ax2.axhline(y=20, color='green', linestyle='--', alpha=0.5, label='超卖线')
    ax2.axhline(y=50, color='black', linestyle='-', alpha=0.3)
    
    ax2.set_xlabel('时间')
    ax2.set_ylabel('KDJ')
    ax2.set_ylim(0, 100)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

def plot_volume(df, title="成交量", figsize=(12, 4)):
    """
    绘制成交量
    
    Args:
        df: 包含成交量数据的DataFrame
        title: 图表标题
        figsize: 图表大小
    """
    if 'volume' not in df.columns:
        print("缺少成交量数据")
        return
    
    fig, ax = plt.subplots(figsize=figsize)
    
    # 绘制成交量柱状图
    colors = []
    if 'close' in df.columns and 'open' in df.columns:
        colors = ['red' if close >= open else 'green' 
                 for close, open in zip(df['close'], df['open'])]
    else:
        colors = 'blue'
    
    ax.bar(df.index, df['volume'], color=colors, alpha=0.6)
    
    ax.set_title(title)
    ax.set_xlabel('时间')
    ax.set_ylabel('成交量')
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

def save_plot(filename, dpi=300):
    """
    保存当前图表
    
    Args:
        filename: 文件名
        dpi: 分辨率
    """
    plt.savefig(filename, dpi=dpi, bbox_inches='tight')
    print(f"图表已保存: {filename}")

if __name__ == "__main__":
    # 测试代码
    print("HP_plt 模块测试")
    
    # 创建测试数据
    dates = pd.date_range('2024-01-01', periods=50, freq='D')
    np.random.seed(42)
    
    # 模拟股价数据
    close_prices = 100 + np.cumsum(np.random.randn(50) * 0.5)
    high_prices = close_prices + np.random.rand(50) * 2
    low_prices = close_prices - np.random.rand(50) * 2
    open_prices = close_prices + np.random.randn(50) * 0.3
    volumes = np.random.randint(1000, 10000, 50)
    
    df = pd.DataFrame({
        'datetime': dates,
        'open': open_prices,
        'high': high_prices,
        'low': low_prices,
        'close': close_prices,
        'volume': volumes
    })
    
    df.set_index('datetime', inplace=True)
    
    print("测试数据创建完成")
    print("可以调用以下函数进行绘图测试:")
    print("- plot_kline(df)")
    print("- plot_volume(df)")
    print("- plot_with_indicators(df, indicators)")
    
    # 如果需要实际显示图表，取消下面的注释
    # plot_volume(df)
    print("绘图模块测试完成")
