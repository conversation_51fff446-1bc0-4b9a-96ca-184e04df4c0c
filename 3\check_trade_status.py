"""
检查交易状态脚本
验证买入是否真正执行
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ths_trader import ThsTrader
from config import get_config
from datetime import datetime

def check_trade_status():
    """检查交易状态"""
    print("=" * 60)
    print("🔍 交易状态检查")
    print("=" * 60)
    
    # 获取配置
    config = get_config()
    
    # 创建交易实例
    print("🔗 连接同花顺客户端...")
    trader = ThsTrader(config)
    
    # 登录
    if not trader.login():
        print("❌ 同花顺客户端连接失败")
        return False
    
    print("✅ 同花顺客户端连接成功")
    
    # 检查账户信息
    print("\n💰 检查账户信息...")
    try:
        account_info = trader.get_account_info()
        if account_info:
            balance = account_info.get('balance')
            position = account_info.get('position')
            
            print("📊 账户状态:")
            if balance:
                print("  资金信息:")
                if isinstance(balance, dict):
                    for key, value in balance.items():
                        print(f"    {key}: {value}")
                else:
                    print(f"    {balance}")
            else:
                print("  ⚠️ 无法获取资金信息")
            
            if position:
                print(f"  📈 持仓数量: {len(position)} 只股票")
                
                # 检查是否有000528
                has_000528 = False
                for pos in position:
                    if isinstance(pos, dict):
                        code = pos.get('证券代码', pos.get('code', ''))
                        name = pos.get('证券名称', pos.get('name', ''))
                        amount = pos.get('股票余额', pos.get('amount', 0))
                        
                        print(f"    {name}({code}): {amount}股")
                        
                        if code == '000528':
                            has_000528 = True
                            print(f"    ✅ 找到000528持仓: {amount}股")
                    else:
                        print(f"    {pos}")
                
                if not has_000528:
                    print("    ⚠️ 未找到000528持仓")
            else:
                print("  📈 当前无持仓")
        else:
            print("⚠️ 无法获取账户信息")
    except Exception as e:
        print(f"❌ 获取账户信息失败: {e}")
    
    # 检查今日成交
    print("\n📋 检查今日成交...")
    try:
        today_trades = trader.get_today_trades()
        if today_trades:
            print(f"✅ 今日成交: {len(today_trades)} 笔")
            
            # 检查是否有000528的成交
            has_000528_trade = False
            for trade in today_trades:
                if isinstance(trade, dict):
                    code = trade.get('证券代码', trade.get('code', ''))
                    name = trade.get('证券名称', trade.get('name', ''))
                    direction = trade.get('买卖标志', trade.get('direction', ''))
                    amount = trade.get('成交数量', trade.get('amount', 0))
                    price = trade.get('成交价格', trade.get('price', 0))
                    time = trade.get('成交时间', trade.get('time', ''))
                    
                    print(f"    {time} {direction} {name}({code}) {amount}股 @{price}元")
                    
                    if code == '000528':
                        has_000528_trade = True
                        print(f"    ✅ 找到000528成交记录")
                else:
                    print(f"    {trade}")
            
            if not has_000528_trade:
                print("    ⚠️ 未找到000528成交记录")
        else:
            print("📋 今日无成交记录")
    except Exception as e:
        print(f"❌ 获取今日成交失败: {e}")
    
    # 检查委托状态
    print("\n📝 检查委托状态...")
    try:
        # 尝试获取委托信息
        if hasattr(trader.trader, 'entrust'):
            entrust = trader.trader.entrust
            if entrust:
                print(f"✅ 委托记录: {len(entrust)} 条")
                for order in entrust:
                    if isinstance(order, dict):
                        code = order.get('证券代码', order.get('code', ''))
                        name = order.get('证券名称', order.get('name', ''))
                        direction = order.get('买卖标志', order.get('direction', ''))
                        amount = order.get('委托数量', order.get('amount', 0))
                        price = order.get('委托价格', order.get('price', 0))
                        status = order.get('备注', order.get('status', ''))
                        
                        print(f"    {direction} {name}({code}) {amount}股 @{price}元 状态:{status}")
                        
                        if code == '000528':
                            print(f"    ✅ 找到000528委托记录")
                    else:
                        print(f"    {order}")
            else:
                print("📝 无委托记录")
        else:
            print("⚠️ 无法获取委托信息")
    except Exception as e:
        print(f"❌ 获取委托信息失败: {e}")
    
    return True

def main():
    """主函数"""
    try:
        check_trade_status()
        
        print("\n" + "=" * 60)
        print("🔍 交易状态检查完成")
        print("\n💡 如果没有找到000528的记录，可能的原因:")
        print("1. 买入价格过高，未能成交")
        print("2. 资金不足")
        print("3. 股票停牌或其他限制")
        print("4. 交易时间限制")
        print("5. easytrader连接问题")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n❌ 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")

if __name__ == "__main__":
    main()
