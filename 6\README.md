# 题材数据爬虫

这是一个用于爬取股票题材数据的Python爬虫工具，可以从 `https://iiiii.pro/sc/search_stock_from_plate` 获取指定题材的股票信息。

## 功能特点

- 🎯 支持爬取指定题材的股票数据
- 📊 自动解析和格式化数据
- 💾 支持保存为CSV格式
- 🗄️ **SQLite数据库存储**
- 🔍 **数据库查询和搜索**
- 📈 **数据统计和分析**
- 📋 **Excel数据导出**
- 🔄 支持批量爬取多个题材
- ⚙️ 可配置的参数设置
- 📝 详细的日志记录
- 🛡️ 完善的错误处理

## 文件结构

```
6/
├── plate_data_crawler.py    # 主爬虫类
├── database_manager.py      # 数据库管理工具
├── database_example.py      # 数据库使用示例
├── config.py               # 配置文件
├── example_usage.py        # 使用示例
├── test_crawler.py         # 测试脚本
├── README.md              # 说明文档
├── requirements.txt       # 依赖包列表
└── plate_stocks.db        # SQLite数据库文件(运行后生成)
```

## 安装依赖

```bash
pip install requests pandas openpyxl
```

或者使用requirements.txt：

```bash
pip install -r requirements.txt
```

## 快速开始

### 1. 基本使用

```python
from plate_data_crawler import PlateDataCrawler

# 创建爬虫实例
crawler = PlateDataCrawler()

# 爬取汽车芯片题材数据
stocks = crawler.crawl_plate_data("汽车芯片")

if stocks:
    print(f"成功获取 {len(stocks)} 只股票数据")
else:
    print("获取数据失败")
```

### 2. 自定义参数

```python
# 使用自定义参数
raw_data = crawler.get_plate_stocks(
    plate_name="人工智能",
    uid="your_uid",
    zoom_str="your_zoom_str",
    vip_level=0
)
```

### 3. 数据库存储

```python
# 爬取数据并保存到数据库
stocks = crawler.crawl_plate_data("汽车芯片", save_csv=False, save_db=True)

# 从数据库查询数据
stocks = crawler.get_stocks_from_db("汽车芯片")

# 搜索股票
tech_stocks = crawler.search_stocks("科技")

# 获取题材汇总
plates_summary = crawler.get_plates_summary()
```

### 4. 数据库管理

```python
from database_manager import DatabaseManager

db_manager = DatabaseManager()

# 获取统计信息
plate_stats = db_manager.get_plate_statistics()
industry_dist = db_manager.get_industry_distribution()
area_dist = db_manager.get_area_distribution()

# 导出到Excel
excel_file = db_manager.export_to_excel()
```

### 5. 批量爬取

```python
# 批量爬取多个题材
plate_names = ["汽车芯片", "人工智能", "新能源汽车"]

for plate_name in plate_names:
    stocks = crawler.crawl_plate_data(plate_name)
    print(f"{plate_name}: {len(stocks) if stocks else 0} 只股票")
```

## API参数说明

### 请求参数

| 参数名 | 类型 | 必填 | 说明 | 默认值 |
|--------|------|------|------|--------|
| uid | string | 是 | 用户ID | "681b0a4992d4437dd4e7e936" |
| zoom_str | string | 是 | 验证字符串 | "5TUD1oCMGx4tV7gmQEEhKkG34MNUwa9m" |
| vip_level | int | 是 | VIP等级 | 0 |
| plate_name | string | 是 | 题材名称 | - |
| type | string | 是 | 类型 | "plate" |

### 示例请求

```json
{
    "uid": "681b0a4992d4437dd4e7e936",
    "zoom_str": "5TUD1oCMGx4tV7gmQEEhKkG34MNUwa9m",
    "vip_level": 0,
    "plate_name": "汽车芯片",
    "type": "plate"
}
```

## 热门题材列表

配置文件中包含了100+个热门题材，包括：

- **科技类**: 人工智能、5G概念、半导体、芯片设计等
- **新能源**: 新能源汽车、光伏概念、锂电池、氢能源等  
- **医药类**: 生物医药、疫苗、基因测序、医疗器械等
- **消费类**: 白酒、食品饮料、电商、游戏等
- **工业类**: 军工概念、机器人、智能制造等

## 配置说明

### API配置 (config.py)

```python
API_CONFIG = {
    "base_url": "https://iiiii.pro/sc/search_stock_from_plate",
    "default_params": {
        "uid": "681b0a4992d4437dd4e7e936",
        "zoom_str": "5TUD1oCMGx4tV7gmQEEhKkG34MNUwa9m",
        "vip_level": 0,
        "type": "plate"
    }
}
```

### 爬虫配置

```python
CRAWLER_CONFIG = {
    "request_timeout": 30,    # 请求超时时间
    "retry_times": 3,         # 重试次数
    "request_delay": 1,       # 请求间隔
    "save_csv": True,         # 保存CSV文件
}
```

## 使用示例

### 基础爬虫示例

```bash
python example_usage.py
```

示例包含：
1. 单个题材数据爬取
2. 批量题材数据爬取
3. 自定义参数使用
4. 简单数据分析
5. 错误处理演示

### 数据库功能示例

```bash
python database_example.py
```

数据库示例包含：
1. 爬取数据并保存到数据库
2. 查询数据库数据
3. 数据库分析和统计
4. 特定题材分析
5. 导出数据到Excel
6. 高级查询示例

### 数据库管理工具

```bash
python database_manager.py
```

管理工具功能：
1. 查看数据库信息
2. 题材统计分析
3. 行业和地区分布
4. 数据导出
5. 数据清理

## 数据库结构

系统使用SQLite数据库存储数据，包含以下表：

### stocks表 (股票数据)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | INTEGER | 主键 |
| _id | TEXT | 原始数据ID |
| ts_code | TEXT | 股票代码 |
| name | TEXT | 股票名称 |
| symbol | TEXT | 股票简称 |
| fullname | TEXT | 公司全称 |
| industry | TEXT | 所属行业 |
| area | TEXT | 所在地区 |
| market | TEXT | 交易市场 |
| plates | TEXT | 相关题材 |
| plates_info | TEXT | 题材详细信息(JSON) |
| plate_name | TEXT | 爬取时的题材名称 |
| crawl_time | TIMESTAMP | 爬取时间 |

### plates表 (题材汇总)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | INTEGER | 主键 |
| plate_name | TEXT | 题材名称 |
| stock_count | INTEGER | 股票数量 |
| last_crawl_time | TIMESTAMP | 最后爬取时间 |

### crawl_logs表 (爬取日志)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | INTEGER | 主键 |
| plate_name | TEXT | 题材名称 |
| status | TEXT | 爬取状态 |
| stock_count | INTEGER | 爬取数量 |
| error_message | TEXT | 错误信息 |
| crawl_time | TIMESTAMP | 爬取时间 |

## 输出文件

### CSV文件格式

爬取的数据会保存为CSV文件，文件名格式：
```
{题材名称}_股票数据_{时间戳}.csv
```

例如：
```
汽车芯片_股票数据_20250813_143022.csv
```

### Excel导出

可以将数据库中的所有数据导出为Excel文件，包含多个工作表：
- 股票数据：所有股票的详细信息
- 题材汇总：各题材的统计信息
- 爬取日志：爬取操作的历史记录
- 题材统计：题材的详细统计
- 行业分布：各行业的股票分布
- 地区分布：各地区的股票分布

### 日志文件

程序运行日志保存在 `plate_crawler.log` 文件中，包含：
- 请求状态
- 数据解析结果
- 错误信息
- 执行时间等

## 注意事项

1. **请求频率**: 建议在请求间添加适当延时，避免过于频繁的请求
2. **参数有效性**: uid和zoom_str参数需要有效，否则可能无法获取数据
3. **网络环境**: 确保网络连接正常，可以访问目标网站
4. **数据结构**: API返回的数据结构可能会变化，需要相应调整解析逻辑
5. **合规使用**: 请遵守网站的使用条款和robots.txt规则

## 错误处理

程序包含完善的错误处理机制：

- 网络请求异常
- JSON解析错误  
- 数据格式异常
- 文件保存失败
- 参数验证错误

## 扩展功能

可以根据需要扩展以下功能：

1. **数据库存储**: 将数据保存到数据库
2. **定时任务**: 设置定时爬取任务
3. **数据分析**: 添加更多数据分析功能
4. **可视化**: 生成图表和报告
5. **API接口**: 提供Web API接口

## 许可证

MIT License

## 更新日志

- v1.0.0 (2025-08-13): 初始版本发布
  - 基本爬虫功能
  - CSV数据保存
  - 配置文件支持
  - 使用示例
