#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
题材数据爬虫配置文件
"""

# API配置
API_CONFIG = {
    "base_url": "https://iiiii.pro/sc/search_stock_from_plate",
    "default_params": {
        "uid": "681b0a4992d4437dd4e7e936",
        "zoom_str": "5TUD1oCMGx4tV7gmQEEhKkG34MNUwa9m",
        "vip_level": 0,
        "type": "plate"
    }
}

# 请求头配置
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Content-Type': 'application/json',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Referer': 'https://iiiii.pro/',
    'Origin': 'https://iiiii.pro'
}

# 热门题材列表
POPULAR_PLATES = [
    "汽车芯片",
    "人工智能",
    "新能源汽车", 
    "半导体",
    "5G概念",
    "光伏概念",
    "锂电池",
    "医疗器械",
    "军工概念",
    "数字货币",
    "区块链",
    "云计算",
    "大数据",
    "物联网",
    "虚拟现实",
    "增强现实",
    "工业互联网",
    "智能制造",
    "新基建",
    "碳中和",
    "氢能源",
    "储能",
    "充电桩",
    "智能驾驶",
    "车联网",
    "芯片设计",
    "芯片制造",
    "集成电路",
    "显示面板",
    "LED",
    "激光",
    "传感器",
    "机器人",
    "无人机",
    "3D打印",
    "生物医药",
    "疫苗",
    "基因测序",
    "医美",
    "CRO",
    "创新药",
    "仿制药",
    "中药",
    "医疗服务",
    "在线教育",
    "游戏",
    "直播",
    "短视频",
    "电商",
    "快递物流",
    "冷链物流",
    "跨境电商",
    "免税店",
    "白酒",
    "食品饮料",
    "农业",
    "种业",
    "养殖业",
    "饲料",
    "化肥",
    "农药",
    "有机硅",
    "钛白粉",
    "稀土",
    "锂矿",
    "钴",
    "镍",
    "铜",
    "黄金",
    "石油",
    "天然气",
    "煤炭",
    "钢铁",
    "水泥",
    "玻璃",
    "化工",
    "塑料",
    "橡胶",
    "纺织",
    "服装",
    "家具",
    "家电",
    "房地产",
    "建筑",
    "装修",
    "园林",
    "环保",
    "水务",
    "固废处理",
    "大气治理",
    "土壤修复",
    "银行",
    "保险",
    "券商",
    "信托",
    "租赁",
    "支付",
    "消费金融",
    "航空",
    "机场",
    "港口",
    "高速公路",
    "铁路",
    "船舶",
    "汽车",
    "汽车零部件",
    "轮胎",
    "玻璃",
    "钢铁",
    "有色金属"
]

# 爬虫配置
CRAWLER_CONFIG = {
    "request_timeout": 30,  # 请求超时时间(秒)
    "retry_times": 3,       # 重试次数
    "retry_delay": 2,       # 重试间隔(秒)
    "request_delay": 1,     # 请求间隔(秒)
    "max_concurrent": 5,    # 最大并发数
    "save_raw_data": True,  # 是否保存原始数据
    "save_csv": True,       # 是否保存CSV文件
    "save_json": False,     # 是否保存JSON文件
}

# 文件保存配置
FILE_CONFIG = {
    "output_dir": "./data",     # 输出目录
    "csv_encoding": "utf-8-sig", # CSV编码
    "json_encoding": "utf-8",   # JSON编码
    "filename_format": "{plate_name}_股票数据_{timestamp}",  # 文件名格式
    "timestamp_format": "%Y%m%d_%H%M%S",  # 时间戳格式
}

# 日志配置
LOG_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(levelname)s - %(message)s",
    "file": "plate_crawler.log",
    "encoding": "utf-8",
    "max_size": 10 * 1024 * 1024,  # 10MB
    "backup_count": 5,
}

# 数据字段映射（根据实际API响应调整）
FIELD_MAPPING = {
    "stock_code": "股票代码",
    "stock_name": "股票名称", 
    "current_price": "当前价格",
    "change_percent": "涨跌幅",
    "change_amount": "涨跌额",
    "volume": "成交量",
    "turnover": "成交额",
    "market_cap": "市值",
    "pe_ratio": "市盈率",
    "pb_ratio": "市净率",
    "industry": "所属行业",
    "concept": "概念题材",
}
