#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
查找最近30天ATR振幅在20%以上的可转债

从数据库查询可转债的历史价格数据，计算ATR振幅，筛选出振幅大于20%的转债
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict

# 导入数据库连接模块
try:
    import mysql.connector
    MYSQL_CONNECTOR_AVAILABLE = True
except ImportError:
    MYSQL_CONNECTOR_AVAILABLE = False

try:
    import pymysql
    import pymysql.cursors
    PYMYSQL_AVAILABLE = True
except ImportError:
    PYMYSQL_AVAILABLE = False

MYSQL_AVAILABLE = MYSQL_CONNECTOR_AVAILABLE or PYMYSQL_AVAILABLE


class HighATRBondFinder:
    """高ATR可转债查找器"""
    
    def __init__(self, db_config):
        """初始化"""
        self.db_config = db_config
        self.connection = None
        self.db_type = None
        
        # 加载可转债列表
        self._load_bond_list()
    
    def _load_bond_list(self):
        """加载可转债列表"""
        try:
            # 从完整数据中加载可转债代码
            df = pd.read_csv("../东方财富终极完整可转债_20250807_004218.csv", encoding='utf-8-sig')
            
            # 筛选正常状态的可转债
            normal_bonds = df[df['状态'] == '正常']
            
            # 提取6位可转债代码
            self.bond_codes = []
            for _, row in normal_bonds.iterrows():
                bond_code = str(row['债券代码']).strip()
                if len(bond_code) == 6 and bond_code.isdigit():
                    self.bond_codes.append(bond_code)
            
            print(f"✓ 加载可转债代码: {len(self.bond_codes)} 只")
            
        except Exception as e:
            print(f"✗ 加载可转债列表失败: {e}")
            self.bond_codes = []
    
    def connect_database(self):
        """连接数据库"""
        if not MYSQL_AVAILABLE:
            print("✗ 数据库连接失败: 未安装MySQL驱动")
            return False
        
        try:
            if MYSQL_CONNECTOR_AVAILABLE:
                import mysql.connector
                self.connection = mysql.connector.connect(**self.db_config)
                self.db_type = 'mysql.connector'
            elif PYMYSQL_AVAILABLE:
                import pymysql
                self.connection = pymysql.connect(**self.db_config)
                self.db_type = 'pymysql'
            else:
                print("✗ 未找到可用的MySQL驱动")
                return False
            
            print(f"✓ 数据库连接成功 (使用 {self.db_type})")
            return True
        except Exception as e:
            print(f"✗ 数据库连接失败: {e}")
            return False
    
    def get_bond_price_data(self, bond_code: str, days: int = 30) -> pd.DataFrame:
        """获取可转债价格数据"""
        
        # 计算日期范围
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days)
        
        # 构建查询SQL
        query = """
        SELECT symbol, day, open, high, low, close, volume
        FROM iquant_daily_price 
        WHERE symbol = %s 
        AND day >= %s 
        AND day <= %s
        ORDER BY day ASC
        """
        
        try:
            # 执行查询
            if self.db_type == 'mysql.connector':
                cursor = self.connection.cursor(dictionary=True)
                cursor.execute(query, (bond_code, start_date, end_date))
                results = cursor.fetchall()
            else:
                import pymysql.cursors
                cursor = self.connection.cursor(pymysql.cursors.DictCursor)
                cursor.execute(query, (bond_code, start_date, end_date))
                results = cursor.fetchall()
            
            cursor.close()
            
            if results:
                df = pd.DataFrame(results)
                return df
            else:
                return pd.DataFrame()
                
        except Exception as e:
            print(f"✗ 查询 {bond_code} 价格数据失败: {e}")
            return pd.DataFrame()
    
    def calculate_atr(self, df: pd.DataFrame, period: int = 14) -> float:
        """计算ATR (Average True Range)"""
        
        if len(df) < period:
            return 0.0
        
        try:
            # 计算True Range
            df = df.copy()
            df['prev_close'] = df['close'].shift(1)
            
            df['tr1'] = df['high'] - df['low']
            df['tr2'] = abs(df['high'] - df['prev_close'])
            df['tr3'] = abs(df['low'] - df['prev_close'])
            
            df['true_range'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)
            
            # 计算ATR (简单移动平均)
            atr = df['true_range'].rolling(window=period).mean().iloc[-1]
            
            # 计算ATR百分比 (相对于收盘价)
            latest_close = df['close'].iloc[-1]
            atr_percentage = (atr / latest_close) * 100 if latest_close > 0 else 0
            
            return atr_percentage
            
        except Exception as e:
            print(f"✗ 计算ATR失败: {e}")
            return 0.0
    
    def find_high_atr_bonds(self, days: int = 30, atr_threshold: float = 20.0) -> List[Dict]:
        """查找高ATR可转债"""
        
        print(f"🔍 查找最近{days}天ATR振幅>{atr_threshold}%的可转债...")
        
        high_atr_bonds = []
        processed_count = 0
        
        for bond_code in self.bond_codes:
            processed_count += 1
            
            if processed_count % 50 == 0:
                print(f"  已处理: {processed_count}/{len(self.bond_codes)}")
            
            # 获取价格数据
            price_df = self.get_bond_price_data(bond_code, days)
            
            if price_df.empty:
                continue
            
            # 计算ATR
            atr_percentage = self.calculate_atr(price_df)
            
            if atr_percentage >= atr_threshold:
                # 获取最新价格信息
                latest_data = price_df.iloc[-1]
                
                bond_info = {
                    'bond_code': bond_code,
                    'atr_percentage': atr_percentage,
                    'latest_close': latest_data['close'],
                    'latest_date': latest_data['day'],
                    'data_points': len(price_df),
                    'price_range': f"{price_df['low'].min():.2f}-{price_df['high'].max():.2f}"
                }
                
                high_atr_bonds.append(bond_info)
        
        print(f"✓ 处理完成，找到 {len(high_atr_bonds)} 只高ATR可转债")
        return high_atr_bonds
    
    def export_bond_codes(self, high_atr_bonds: List[Dict]) -> str:
        """导出可转债代码（空格间隔）"""
        
        if not high_atr_bonds:
            print("⚠️  没有找到符合条件的可转债")
            return ""
        
        # 按ATR降序排列
        sorted_bonds = sorted(high_atr_bonds, key=lambda x: x['atr_percentage'], reverse=True)
        
        # 提取6位代码
        bond_codes = [bond['bond_code'] for bond in sorted_bonds]
        
        # 用空格连接
        codes_string = ' '.join(bond_codes)
        
        # 保存到文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"高ATR可转债代码_{timestamp}.txt"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(codes_string)
        
        print(f"📁 可转债代码已导出: {output_file}")
        print(f"📋 代码列表: {codes_string}")
        
        return codes_string
    
    def display_detailed_results(self, high_atr_bonds: List[Dict]):
        """显示详细结果"""
        
        if not high_atr_bonds:
            return
        
        print(f"\n{'='*80}")
        print(f"高ATR可转债详细信息")
        print(f"{'='*80}")
        
        # 按ATR降序排列
        sorted_bonds = sorted(high_atr_bonds, key=lambda x: x['atr_percentage'], reverse=True)
        
        print(f"📊 统计信息:")
        print(f"  符合条件数量: {len(sorted_bonds)} 只")
        print(f"  平均ATR: {np.mean([b['atr_percentage'] for b in sorted_bonds]):.2f}%")
        print(f"  最高ATR: {sorted_bonds[0]['atr_percentage']:.2f}%")
        print(f"  最低ATR: {sorted_bonds[-1]['atr_percentage']:.2f}%")
        
        print(f"\n📋 详细列表:")
        print(f"{'排名':<4} {'代码':<8} {'ATR%':<8} {'最新价':<8} {'最新日期':<12} {'数据点':<6} {'价格区间':<15}")
        print("-" * 80)
        
        for i, bond in enumerate(sorted_bonds, 1):
            print(f"{i:<4} {bond['bond_code']:<8} {bond['atr_percentage']:<8.2f} "
                  f"{bond['latest_close']:<8.2f} {bond['latest_date']:<12} "
                  f"{bond['data_points']:<6} {bond['price_range']:<15}")
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            print("✓ 数据库连接已关闭")


def main():
    """主函数"""
    print("🚀 高ATR可转债查找工具")
    print("=" * 60)
    
    # 数据库配置
    db_config = {
        'host': '***********',
        'port': 3306,
        'user': 'iQuant',
        'password': 'NAAnwaRsb8YGN3F5',
        'database': 'iquant',
        'charset': 'utf8mb4'
    }
    
    try:
        # 初始化查找器
        finder = HighATRBondFinder(db_config)
        
        if not finder.bond_codes:
            print("❌ 未加载到可转债代码")
            return
        
        # 连接数据库
        if not finder.connect_database():
            print("❌ 数据库连接失败")
            return
        
        # 查找高ATR可转债
        high_atr_bonds = finder.find_high_atr_bonds(days=30, atr_threshold=20.0)
        
        if high_atr_bonds:
            # 显示详细结果
            finder.display_detailed_results(high_atr_bonds)
            
            # 导出代码
            codes_string = finder.export_bond_codes(high_atr_bonds)
            
            print(f"\n✅ 查找完成！")
            print(f"📊 找到 {len(high_atr_bonds)} 只ATR>20%的可转债")
            print(f"📋 代码: {codes_string}")
        else:
            print(f"\n⚠️  未找到ATR>20%的可转债")
            print(f"💡 建议降低ATR阈值或增加查询天数")
        
        # 关闭连接
        finder.close()
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
