"""
同花顺登录测试脚本
测试账号密码是否正确，显示登录状态
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ths_trader import ThsTrader
from config import get_config
import time

def test_login():
    """测试同花顺登录"""
    print("=== 同花顺登录测试 ===")
    
    # 获取配置
    config = get_config()
    
    # 显示配置信息
    print(f"用户名: {config['account_config']['user']}")
    print(f"密码: {'*' * len(config['account_config']['password'])}")
    print(f"客户端路径: {config['account_config']['exe_path']}")
    print()
    
    # 检查客户端文件是否存在
    exe_path = config['account_config']['exe_path']
    if not os.path.exists(exe_path):
        print(f"❌ 错误: 同花顺客户端文件不存在: {exe_path}")
        print("请检查客户端安装路径是否正确")
        return False
    else:
        print(f"✅ 同花顺客户端文件存在: {exe_path}")
    
    # 创建交易实例
    trader = ThsTrader(config)
    
    print("\n开始登录测试...")
    print("注意: 如果出现验证码，请在同花顺客户端中手动输入")
    print("-" * 50)
    
    try:
        # 尝试登录
        success = trader.login()
        
        if success:
            print("🎉 登录成功！")
            
            # 获取账户信息
            print("\n获取账户信息...")
            account_info = trader.get_account_info()
            
            if account_info:
                print("📊 账户状态:")
                balance = account_info.get('balance')
                position = account_info.get('position', [])
                
                if balance:
                    print(f"  💰 资金信息:")
                    for key, value in balance.items():
                        print(f"    {key}: {value}")
                
                print(f"  📈 持仓数量: {len(position)} 只股票")
                if position:
                    print("  持仓详情:")
                    for i, pos in enumerate(position[:5]):  # 只显示前5只
                        print(f"    {i+1}. {pos}")
                
                # 获取今日成交
                today_trades = trader.get_today_trades()
                if today_trades:
                    print(f"  📋 今日成交: {len(today_trades)} 笔")
                    if today_trades:
                        print("  最近成交:")
                        for i, trade in enumerate(today_trades[:3]):  # 只显示前3笔
                            print(f"    {i+1}. {trade}")
                else:
                    print("  📋 今日成交: 0 笔")
            else:
                print("⚠️  无法获取账户信息，但登录可能成功")
            
            return True
            
        else:
            print("❌ 登录失败")
            print("\n可能的原因:")
            print("1. 用户名或密码错误")
            print("2. 同花顺客户端未启动或版本不兼容")
            print("3. 需要验证码但未正确输入")
            print("4. 网络连接问题")
            print("5. 账户被锁定或其他安全限制")
            return False
            
    except Exception as e:
        print(f"❌ 登录过程中出现异常: {e}")
        print("\n详细错误信息:")
        import traceback
        traceback.print_exc()
        return False

def check_prerequisites():
    """检查前置条件"""
    print("=== 检查前置条件 ===")
    
    # 检查依赖包
    try:
        import easytrader
        print("✅ easytrader 已安装")
    except ImportError:
        print("❌ easytrader 未安装，请运行: pip install easytrader")
        return False
    
    try:
        import win32api
        print("✅ pywin32 已安装")
    except ImportError:
        print("❌ pywin32 未安装，请运行: pip install pywin32")
        return False
    
    # 检查配置
    config = get_config()
    account_config = config.get('account_config', {})
    
    if not account_config.get('user'):
        print("❌ 用户名未配置")
        return False
    
    if not account_config.get('password'):
        print("❌ 密码未配置")
        return False
    
    if not account_config.get('exe_path'):
        print("❌ 同花顺客户端路径未配置")
        return False
    
    print("✅ 配置检查通过")
    return True

def main():
    """主函数"""
    print("同花顺账号登录测试")
    print("=" * 50)
    
    # 检查前置条件
    if not check_prerequisites():
        print("\n❌ 前置条件检查失败，请先解决上述问题")
        return
    
    print("\n前置条件检查通过，开始登录测试...")
    
    # 提醒用户
    print("\n⚠️  重要提醒:")
    print("1. 请确保同花顺客户端已关闭（程序会自动启动）")
    print("2. 如果出现验证码，请在弹出的客户端窗口中输入")
    print("3. 登录过程可能需要1-2分钟，请耐心等待")
    
    input("\n按回车键继续...")
    
    # 执行登录测试
    success = test_login()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 登录测试成功！账号配置正确。")
        print("现在可以运行完整的交易系统了。")
    else:
        print("❌ 登录测试失败，请检查账号配置。")
        print("建议:")
        print("1. 检查用户名和密码是否正确")
        print("2. 确认同花顺客户端路径是否正确")
        print("3. 尝试手动启动同花顺客户端并登录")

if __name__ == '__main__':
    main()
