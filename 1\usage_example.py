#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
mootdx 自定义板块操作工具使用示例

展示如何使用测试工具类和股票 CRUD 工具类

作者: Usage Example
日期: 2025-08-06
"""

import os
import sys
from test_customize_tool import CustomizeTestTool
from stock_crud_tool import StockCRUDTool


def example_basic_operations():
    """基础操作示例"""
    print("=" * 60)
    print("基础操作示例")
    print("=" * 60)
    
    # 初始化工具（如果有通达信安装目录，请修改路径）
    tdx_dir = "C:/new_tdx"  # 请根据实际情况修改
    if not os.path.exists(tdx_dir):
        print(f"通达信目录不存在: {tdx_dir}")
        print("使用模拟模式进行演示...")
        tdx_dir = None
    
    tool = CustomizeTestTool(tdxdir=tdx_dir)
    
    # 1. 创建板块
    print("\n1. 创建自定义板块")
    tool.test_create_block("龙虎榜", ['600036', '600016'])
    tool.test_create_block("优质股", ['000001', '000002', '600519'])
    
    # 2. 查询板块
    print("\n2. 查询板块内容")
    tool.test_search_block("龙虎榜")
    tool.test_search_block("优质股")
    
    # 3. 更新板块
    print("\n3. 更新板块")
    tool.test_update_block("龙虎榜", ['600036', '600016', '600519'])
    
    # 4. 删除板块
    print("\n4. 删除板块")
    tool.test_remove_block("龙虎榜")
    tool.test_remove_block("优质股")


def example_stock_crud_operations():
    """股票 CRUD 操作示例"""
    print("\n" + "=" * 60)
    print("股票 CRUD 操作示例")
    print("=" * 60)
    
    # 初始化 CRUD 工具
    crud_tool = StockCRUDTool()
    
    block_name = "CRUD示例板块"
    
    # 1. 创建板块并添加股票 (Create)
    print("\n1. 创建板块并添加股票")
    result = crud_tool.create_block_with_stocks(
        block_name, 
        ['600036', '600016', '000001', 'invalid']  # 包含无效代码
    )
    print(f"结果: {result['message']}")
    if result['invalid_codes']:
        print(f"无效代码: {result['invalid_codes']}")
    
    # 2. 查询板块股票 (Read)
    print("\n2. 查询板块股票")
    result = crud_tool.get_block_stocks(block_name)
    print(f"结果: {result['message']}")
    if result['success']:
        print(f"股票列表: {result['stocks']}")
    
    # 3. 添加新股票 (Update - Add)
    print("\n3. 添加新股票")
    result = crud_tool.add_stocks_to_block(
        block_name, 
        ['000002', '600519', '600036']  # 包含重复代码
    )
    print(f"结果: {result['message']}")
    if result['duplicate_codes']:
        print(f"重复代码: {result['duplicate_codes']}")
    
    # 4. 移除股票 (Update - Remove)
    print("\n4. 移除股票")
    result = crud_tool.remove_stocks_from_block(
        block_name, 
        ['600016', '999999']  # 包含不存在的代码
    )
    print(f"结果: {result['message']}")
    if result['not_found_codes']:
        print(f"未找到代码: {result['not_found_codes']}")
    
    # 5. 替换所有股票 (Update - Replace)
    print("\n5. 替换所有股票")
    result = crud_tool.replace_block_stocks(
        block_name, 
        ['300059', '002415', '000858']
    )
    print(f"结果: {result['message']}")
    
    # 6. 最终查询
    print("\n6. 最终查询")
    result = crud_tool.get_block_stocks(block_name)
    print(f"结果: {result['message']}")
    if result['success']:
        print(f"最终股票列表: {result['stocks']}")
    
    # 7. 删除板块 (Delete)
    print("\n7. 删除板块")
    result = crud_tool.delete_block(block_name)
    print(f"结果: {result['message']}")


def example_batch_operations():
    """批量操作示例"""
    print("\n" + "=" * 60)
    print("批量操作示例")
    print("=" * 60)
    
    crud_tool = StockCRUDTool()
    
    # 定义批量操作
    batch_operations = [
        # 创建多个板块
        {
            'type': 'create',
            'block_name': '科技股',
            'stocks': ['000858', '002415', '300059']
        },
        {
            'type': 'create',
            'block_name': '银行股',
            'stocks': ['600036', '000001', '600016']
        },
        {
            'type': 'create',
            'block_name': '消费股',
            'stocks': ['600519', '000858', '002415']
        },
        
        # 查询所有板块
        {
            'type': 'read',
            'block_name': '科技股'
        },
        {
            'type': 'read',
            'block_name': '银行股'
        },
        {
            'type': 'read',
            'block_name': '消费股'
        },
        
        # 修改操作
        {
            'type': 'add',
            'block_name': '科技股',
            'stocks': ['300750', '688036']
        },
        {
            'type': 'remove',
            'block_name': '银行股',
            'stocks': ['600016']
        },
        {
            'type': 'replace',
            'block_name': '消费股',
            'stocks': ['600519', '000858', '002304']
        },
        
        # 最终查询
        {
            'type': 'read',
            'block_name': '科技股'
        },
        {
            'type': 'read',
            'block_name': '银行股'
        },
        {
            'type': 'read',
            'block_name': '消费股'
        },
        
        # 清理
        {
            'type': 'delete',
            'block_name': '科技股'
        },
        {
            'type': 'delete',
            'block_name': '银行股'
        },
        {
            'type': 'delete',
            'block_name': '消费股'
        }
    ]
    
    # 执行批量操作
    results = crud_tool.batch_operations(batch_operations)
    
    # 统计结果
    success_count = sum(1 for r in results if r['success'])
    print(f"\n批量操作完成:")
    print(f"总操作数: {len(results)}")
    print(f"成功: {success_count}")
    print(f"失败: {len(results) - success_count}")


def example_comprehensive_test():
    """综合测试示例"""
    print("\n" + "=" * 60)
    print("综合测试示例")
    print("=" * 60)
    
    # 使用测试工具进行综合测试
    tool = CustomizeTestTool()
    tool.run_comprehensive_test()


def example_error_handling():
    """错误处理示例"""
    print("\n" + "=" * 60)
    print("错误处理示例")
    print("=" * 60)
    
    crud_tool = StockCRUDTool()
    
    # 1. 无效股票代码
    print("1. 测试无效股票代码")
    result = crud_tool.create_block_with_stocks(
        "错误测试", 
        ['invalid', '12345', 'abcdef', '600036']
    )
    print(f"结果: {result['message']}")
    print(f"有效代码: {result['valid_codes']}")
    print(f"无效代码: {result['invalid_codes']}")
    
    # 2. 操作不存在的板块
    print("\n2. 测试操作不存在的板块")
    result = crud_tool.get_block_stocks("不存在的板块")
    print(f"结果: {result['message']}")
    
    # 3. 添加重复股票
    print("\n3. 测试添加重复股票")
    if crud_tool.custom:
        # 先创建一个板块
        crud_tool.create_block_with_stocks("重复测试", ['600036', '600016'])
        
        # 尝试添加重复股票
        result = crud_tool.add_stocks_to_block(
            "重复测试", 
            ['600036', '000001', '600016']
        )
        print(f"结果: {result['message']}")
        print(f"新增代码: {result['added_codes']}")
        print(f"重复代码: {result['duplicate_codes']}")
        
        # 清理
        crud_tool.delete_block("重复测试")
    
    # 清理错误测试板块
    crud_tool.delete_block("错误测试")


def main():
    """主函数"""
    print("mootdx 自定义板块操作工具使用示例")
    print("=" * 60)
    
    try:
        # 1. 基础操作示例
        example_basic_operations()
        
        # 2. 股票 CRUD 操作示例
        example_stock_crud_operations()
        
        # 3. 批量操作示例
        example_batch_operations()
        
        # 4. 综合测试示例
        example_comprehensive_test()
        
        # 5. 错误处理示例
        example_error_handling()
        
    except KeyboardInterrupt:
        print("\n\n用户中断操作")
    except Exception as e:
        print(f"\n\n发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("示例演示完成")
    print("=" * 60)


if __name__ == "__main__":
    main()
