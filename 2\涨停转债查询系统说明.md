# 涨停正股转债查询系统

## 📋 系统概述

这是一个完整的涨停正股转债查询系统，能够：
1. 查询最近N天有涨停的正股
2. 匹配对应的可转债信息
3. 生成详细的分析报告
4. 导出数据供进一步分析

## 🗄️ 数据库配置

### 数据库连接信息
```python
db_config = {
    'host': '***********',
    'port': 3306,
    'user': 'iQuant',
    'password': 'NAAnwaRsb8YGN3F5',
    'database': 'iquant',
    'charset': 'utf8mb4'
}
```

### 数据表结构
**表名**: `iquant_daily_price`

| 字段名 | 类型 | 说明 |
|--------|------|------|
| symbol | varchar(20) | 股票代码 |
| day | date | 交易日期 |
| open | decimal(10,2) | 开盘价 |
| high | decimal(10,2) | 最高价 |
| low | decimal(10,2) | 最低价 |
| close | decimal(10,2) | 收盘价 |
| pre_close | decimal(10,2) | 前收盘价 |
| volume | bigint | 成交量(股) |
| limit_up | tinyint(1) | 涨停标记(0-未涨停,1-涨停) |
| ma_price5 | decimal(16,6) | 5日均线 |
| ma_price10 | decimal(16,6) | 10日均线 |
| ma_price30 | decimal(16,6) | 30日均线 |

## 📁 文件结构

### 核心文件
1. **`涨停正股转债查询.py`** - 主查询工具
2. **`完整可转债正股映射_20250806_234133.csv`** - 映射关系数据
3. **`测试映射关系.py`** - 映射关系测试工具

### 数据文件
1. **`东方财富可转债数据_20250807_001449.csv`** - 爬取的可转债基本信息
2. **`修复价格的可转债列表.csv`** - 修复后的可转债价格数据

## 🔧 系统功能

### 1. 涨停股票查询
```python
# 查询最近10天的涨停股票
limit_up_df = query_tool.get_limit_up_stocks_recent_days(days=10)
```

### 2. 可转债映射
```python
# 查询涨停股票对应的可转债
result = query_tool.get_limit_up_stocks_with_bonds(days=10)
```

### 3. 数据导出
```python
# 导出分析结果
output_file = query_tool.export_limit_up_bonds(result)
```

## 📊 映射关系统计

### 基本统计
- **正股数量**: 673 只
- **可转债数量**: 758 只
- **平均每只正股对应**: 1.1 只可转债

### 交易所分布
- **上海可转债**: 165 只 (21.8%)
- **深圳可转债**: 132 只 (17.4%)

### 正股分布
- **单一可转债正股**: 600 只 (89.2%)
- **多只可转债正股**: 73 只 (10.8%)

## 🚀 使用方法

### 1. 环境准备
```bash
# 安装依赖
pip install pandas mysql-connector-python
# 或者
pip install pandas pymysql
```

### 2. 基本使用
```python
from 涨停正股转债查询 import LimitUpStockBondQuery

# 数据库配置
db_config = {
    'host': '***********',
    'user': 'iQuant',
    'password': 'NAAnwaRsb8YGN3F5',
    'database': 'iquant',
    'charset': 'utf8mb4'
}

# 初始化查询器
query_tool = LimitUpStockBondQuery(db_config)

# 查询最近10天涨停股票的可转债
result = query_tool.get_limit_up_stocks_with_bonds(days=10)

# 显示报告
query_tool.display_limit_up_bonds_report(result)

# 导出数据
output_file = query_tool.export_limit_up_bonds(result)

# 关闭连接
query_tool.close()
```

### 3. 测试映射关系
```bash
python 测试映射关系.py
```

## 📈 查询示例

### 测试结果示例
```
涨停股票总数: 11 只
有对应可转债: 7 只
无对应可转债: 4 只
对应可转债总数: 7 只

有对应可转债的涨停股票:
1. 600326 西藏天路 → 110060 天路转债 (价格: 451.01)
2. 600000 浦发银行 → 110059 浦发转债 (价格: 114.08)
3. 300058 蓝色光标 → 123001 蓝标转债
```

## 🎯 关键特性

### 1. 完整映射关系
- ✅ 支持 673 只正股查询
- ✅ 覆盖 758 只可转债
- ✅ 包含真实价格数据

### 2. 灵活查询
- 🔍 支持自定义查询天数
- 🔍 支持批量股票查询
- 🔍 支持双向映射查询

### 3. 详细报告
- 📊 涨停统计分析
- 📊 可转债价格信息
- 📊 溢价率计算（如有数据）

### 4. 数据导出
- 📁 CSV格式导出
- 📁 包含完整字段信息
- 📁 支持Excel打开

## ⚠️ 注意事项

### 1. 数据库连接
- 确保网络连接正常
- 确保数据库权限正确
- 建议使用连接池

### 2. 映射数据更新
- 定期更新映射关系文件
- 新发行可转债需要手动添加
- 退市可转债需要标记状态

### 3. 性能优化
- 大量查询时建议分批处理
- 可以添加缓存机制
- 考虑使用索引优化查询

## 🔄 扩展功能

### 可以添加的功能
1. **实时行情集成** - 获取可转债实时价格
2. **转股价值计算** - 计算转股价值和溢价率
3. **技术指标分析** - 添加技术指标计算
4. **预警系统** - 设置涨停预警
5. **Web界面** - 开发Web查询界面

### 数据源扩展
1. **多数据源对比** - 集成多个数据源
2. **历史数据分析** - 分析历史涨停规律
3. **关联分析** - 分析正股与可转债的相关性

## 📞 技术支持

如有问题，请检查：
1. 数据库连接配置是否正确
2. 映射关系文件是否存在
3. 依赖库是否正确安装
4. 网络连接是否正常

---

**最后更新**: 2025-08-07  
**版本**: v1.0  
**状态**: 生产就绪 ✅
