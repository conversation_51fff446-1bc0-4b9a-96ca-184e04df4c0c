"""
快速测试脚本
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ths_trader import ThsTrader
from config import get_test_config

def test_data_source():
    """测试数据源连接"""
    print("=== 测试数据源连接 ===")
    
    config = get_test_config()
    trader = ThsTrader(config)
    
    try:
        data = trader.get_data_source()
        if data:
            print(f"✓ 成功获取 {len(data)} 条记录")
            print("\n前3条记录:")
            for i, record in enumerate(data[:3]):
                print(f"  {i+1}. {record['name']}({record['code']}) - {record['bkname']} - {record['addtime']}")
            return True
        else:
            print("✗ 无法获取数据")
            return False
    except Exception as e:
        print(f"✗ 连接失败: {e}")
        return False

def test_new_records():
    """测试新记录检测"""
    print("\n=== 测试新记录检测 ===")
    
    config = get_test_config()
    trader = ThsTrader(config)
    
    try:
        # 第一次检查（初始化）
        new_records = trader.check_new_records()
        print(f"✓ 初始化完成，发现 {len(new_records)} 条新记录")
        
        # 第二次检查（应该没有新记录）
        new_records = trader.check_new_records()
        print(f"✓ 第二次检查，发现 {len(new_records)} 条新记录")
        
        return True
    except Exception as e:
        print(f"✗ 检测失败: {e}")
        return False

def test_stock_validation():
    """测试股票代码验证"""
    print("\n=== 测试股票代码验证 ===")
    
    config = get_test_config()
    trader = ThsTrader(config)
    
    test_codes = [
        ('603101', True),   # 有效代码
        ('000001', True),   # 有效代码
        ('60310', False),   # 长度不对
        ('60310a', False),  # 包含字母
        ('', False),        # 空字符串
        (None, False)       # None
    ]
    
    all_passed = True
    for code, expected in test_codes:
        result = trader.is_valid_stock_code(code)
        status = "✓" if result == expected else "✗"
        print(f"  {status} {code} -> {result} (期望: {expected})")
        if result != expected:
            all_passed = False
    
    return all_passed

def test_config():
    """测试配置"""
    print("\n=== 测试配置 ===")
    
    try:
        config = get_test_config()
        print(f"✓ 数据源URL: {config['data_source_url']}")
        print(f"✓ Token: {config['token'][:20]}...")
        print(f"✓ 测试模式: {config['trade_config']['dry_run']}")
        print(f"✓ 默认买入量: {config['trade_config']['default_amount']}手")
        return True
    except Exception as e:
        print(f"✗ 配置错误: {e}")
        return False

def main():
    """主测试函数"""
    print("同花顺交易接口快速测试")
    print("=" * 50)
    
    tests = [
        ("配置测试", test_config),
        ("数据源连接", test_data_source),
        ("新记录检测", test_new_records),
        ("股票代码验证", test_stock_validation)
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"\n{name}: ✓ 通过")
            else:
                print(f"\n{name}: ✗ 失败")
        except Exception as e:
            print(f"\n{name}: ✗ 异常 - {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统运行正常。")
    else:
        print("⚠️  部分测试失败，请检查配置和网络连接。")

if __name__ == '__main__':
    main()
