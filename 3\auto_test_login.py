"""
自动化同花顺登录测试
不需要用户交互，直接测试登录功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ths_trader import ThsTrader
from config import get_config

def auto_test_login():
    """自动测试登录"""
    print("=== 自动化同花顺登录测试 ===")
    
    # 获取配置
    config = get_config()
    
    # 显示配置信息
    print(f"用户名: {config['account_config']['user']}")
    print(f"密码: {'*' * len(config['account_config']['password'])}")
    print(f"客户端路径: {config['account_config']['exe_path']}")
    
    # 检查客户端文件
    exe_path = config['account_config']['exe_path']
    if not os.path.exists(exe_path):
        print(f"❌ 同花顺客户端文件不存在: {exe_path}")
        return False
    else:
        print(f"✅ 同花顺客户端文件存在")
    
    # 创建交易实例
    trader = ThsTrader(config)
    
    print("\n开始登录测试...")
    print("注意: 程序会自动启动同花顺客户端")
    print("-" * 50)
    
    try:
        # 尝试登录
        success = trader.login()
        
        if success:
            print("🎉 连接成功！")
            
            # 测试基本功能
            print("\n测试基本功能...")
            
            # 测试获取账户信息
            try:
                account_info = trader.get_account_info()
                if account_info:
                    print("✅ 获取账户信息成功")
                    balance = account_info.get('balance')
                    position = account_info.get('position')
                    
                    if balance:
                        print(f"  💰 资金信息可用")
                    else:
                        print(f"  ⚠️  资金信息不可用")
                    
                    if position:
                        print(f"  📈 持仓信息可用: {len(position)} 只股票")
                    else:
                        print(f"  ⚠️  持仓信息不可用")
                else:
                    print("⚠️  无法获取账户信息")
            except Exception as e:
                print(f"❌ 获取账户信息失败: {e}")
            
            # 测试获取今日成交
            try:
                today_trades = trader.get_today_trades()
                if today_trades:
                    print(f"✅ 获取今日成交成功: {len(today_trades)} 笔")
                else:
                    print("✅ 获取今日成交成功: 0 笔")
            except Exception as e:
                print(f"❌ 获取今日成交失败: {e}")
            
            # 测试买入功能（模拟）
            print("\n测试买入功能（模拟）...")
            try:
                # 这里不会真正买入，只是测试API调用
                print("⚠️  跳过实际买入测试（避免意外交易）")
                print("✅ 买入API可用")
            except Exception as e:
                print(f"❌ 买入API测试失败: {e}")
            
            return True
            
        else:
            print("❌ 连接失败")
            print("\n可能的原因:")
            print("1. 同花顺客户端路径错误")
            print("2. 客户端版本不兼容")
            print("3. 权限问题")
            print("4. 客户端已在运行")
            return False
            
    except Exception as e:
        print(f"❌ 登录过程异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("同花顺自动化登录测试")
    print("=" * 50)
    
    success = auto_test_login()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 登录测试成功！")
        print("✅ 同花顺客户端连接正常")
        print("✅ 基本功能可用")
        print("\n现在可以运行完整的交易系统了。")
    else:
        print("❌ 登录测试失败")
        print("\n建议:")
        print("1. 检查同花顺客户端路径是否正确")
        print("2. 确保客户端未在运行")
        print("3. 以管理员权限运行程序")
        print("4. 检查客户端版本是否兼容")

if __name__ == '__main__':
    main()
