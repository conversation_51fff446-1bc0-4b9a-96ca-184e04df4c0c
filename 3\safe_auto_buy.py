"""
安全自动买入脚本
不会点击当日成交或其他不必要的界面元素
专注于买入操作
"""
import sys
import os
import time
import pyautogui
from datetime import datetime
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ths_trader import ThsTrader
from config import get_config

class SafeAutoBuy:
    """安全自动买入系统 - 不点击当日成交"""
    
    def __init__(self):
        self.config = get_config()
        # 设置pyautogui参数
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.3
        
    def safe_connect_ths(self):
        """安全连接同花顺（不获取当日成交）"""
        print("🔗 安全连接同花顺...")
        trader = ThsTrader(self.config)
        
        # 连接但不获取敏感信息
        if trader.login():
            print("✅ 同花顺连接成功")
            return trader
        else:
            print("❌ 同花顺连接失败")
            return None
    
    def safe_input_stock_code(self, stock_code):
        """安全输入股票代码"""
        print(f"🔤 安全输入股票代码: {stock_code}")
        
        try:
            # 方法1: 精确点击证券代码输入框
            print("   点击证券代码输入框...")
            pyautogui.click(x=320, y=85)  # 证券代码框位置
            time.sleep(0.5)
            
            # 清空输入框
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.2)
            pyautogui.press('delete')
            time.sleep(0.2)
            
            # 输入股票代码
            pyautogui.write(stock_code)
            time.sleep(0.5)
            pyautogui.press('enter')
            time.sleep(1.5)
            
            print(f"✅ 股票代码输入完成: {stock_code}")
            return True
            
        except Exception as e:
            print(f"❌ 股票代码输入失败: {e}")
            return False
    
    def safe_input_price(self, price):
        """安全输入买入价格"""
        print(f"💰 安全输入买入价格: {price}")
        
        try:
            # 点击买入价格输入框
            pyautogui.click(x=320, y=123)  # 买入价格框位置
            time.sleep(0.3)
            
            # 清空并输入价格
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.2)
            pyautogui.write(str(price))
            time.sleep(0.5)
            
            print(f"✅ 买入价格输入完成: {price}")
            return True
            
        except Exception as e:
            print(f"❌ 买入价格输入失败: {e}")
            return False
    
    def safe_input_amount(self, amount):
        """安全输入买入数量"""
        print(f"📦 安全输入买入数量: {amount}")
        
        try:
            # 点击买入数量输入框
            pyautogui.click(x=320, y=178)  # 买入数量框位置
            time.sleep(0.3)
            
            # 清空并输入数量
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.2)
            pyautogui.write(str(amount))
            time.sleep(0.5)
            
            print(f"✅ 买入数量输入完成: {amount}")
            return True
            
        except Exception as e:
            print(f"❌ 买入数量输入失败: {e}")
            return False
    
    def safe_click_buy_button(self):
        """安全点击买入按钮"""
        print("🛒 安全点击买入按钮...")
        
        try:
            # 使用快捷键点击买入
            pyautogui.hotkey('alt', 'b')
            time.sleep(1)
            print("✅ 买入按钮点击完成")
            return True
            
        except Exception as e:
            print(f"❌ 买入按钮点击失败: {e}")
            return False
    
    def safe_auto_buy(self, stock_code, stock_name, price, amount):
        """安全自动买入（不点击当日成交）"""
        print("🛡️ 安全自动买入系统")
        print("=" * 50)
        print("特点: 不会点击当日成交或其他敏感界面元素")
        print()
        
        total_amount = price * amount
        
        print(f"📊 交易参数:")
        print(f"   股票代码: {stock_code}")
        print(f"   股票名称: {stock_name}")
        print(f"   买入价格: {price}元")
        print(f"   买入数量: {amount}股")
        print(f"   总金额: {total_amount}元")
        
        # 1. 安全连接同花顺
        trader = self.safe_connect_ths()
        if not trader:
            return False
        
        # 2. 确认界面准备
        print(f"\n🖥️ 界面准备...")
        print("请确保同花顺买入界面已打开")
        input("准备就绪后按回车继续...")
        
        # 3. 倒计时
        print(f"\n⏰ 开始自动输入...")
        for i in range(3, 0, -1):
            print(f"   {i}秒后开始...")
            time.sleep(1)
        
        # 4. 安全输入股票代码
        if not self.safe_input_stock_code(stock_code):
            print("❌ 股票代码输入失败")
            return False
        
        # 5. 安全输入买入价格
        if not self.safe_input_price(price):
            print("❌ 买入价格输入失败")
            return False
        
        # 6. 安全输入买入数量
        if not self.safe_input_amount(amount):
            print("❌ 买入数量输入失败")
            return False
        
        # 7. 确认信息
        print(f"\n🔍 请确认同花顺界面中的信息:")
        print(f"   - 证券代码: {stock_code}")
        print(f"   - 证券名称: {stock_name}")
        print(f"   - 买入价格: {price}")
        print(f"   - 买入数量: {amount}")
        print(f"   - 总金额: {total_amount}")
        
        confirm = input("\n信息正确请输入 'YES' 确认买入: ").strip()
        
        if confirm == 'YES':
            # 8. 安全点击买入按钮
            if self.safe_click_buy_button():
                print("✅ 买入操作完成！")
                
                # 记录日志
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                log_entry = f"{timestamp} - 安全买入 {stock_name}({stock_code}) {amount}股 @{price}元 总额{total_amount}元\n"
                
                with open("safe_auto_buy_log.txt", "a", encoding="utf-8") as f:
                    f.write(log_entry)
                
                print("📝 交易日志已记录")
                return True
            else:
                print("⚠️ 请手动点击买入按钮")
                return True
        else:
            print("❌ 用户取消买入")
            return False

def buy_000528():
    """安全买入000528柳工"""
    safe_buy = SafeAutoBuy()
    
    return safe_buy.safe_auto_buy(
        stock_code='000528',
        stock_name='柳工',
        price=8.52,
        amount=100
    )

def buy_custom():
    """安全买入自定义股票"""
    safe_buy = SafeAutoBuy()
    
    print("📝 请输入股票信息:")
    stock_code = input("股票代码: ").strip()
    stock_name = input("股票名称: ").strip()
    price = float(input("买入价格: ").strip())
    amount = int(input("买入数量: ").strip())
    
    return safe_buy.safe_auto_buy(stock_code, stock_name, price, amount)

def main():
    """主函数"""
    print("🛡️ 安全自动买入系统")
    print("=" * 50)
    print("✅ 不会点击当日成交")
    print("✅ 不会访问敏感界面元素")
    print("✅ 专注于买入操作")
    print()
    
    print("请选择操作:")
    print("1. 买入000528 (柳工)")
    print("2. 自定义买入")
    print("3. 退出")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    try:
        if choice == '1':
            success = buy_000528()
        elif choice == '2':
            success = buy_custom()
        elif choice == '3':
            print("👋 程序退出")
            return
        else:
            print("❌ 无效选择")
            return
        
        print("\n" + "=" * 50)
        if success:
            print("🎉 安全自动买入完成！")
            print("✅ 未点击任何敏感界面元素")
            print("📋 请在同花顺中查看委托状态")
        else:
            print("❌ 安全自动买入失败")
            print("🔄 可以重新尝试")
        print("=" * 50)
        
    except KeyboardInterrupt:
        print("\n❌ 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")

if __name__ == "__main__":
    main()
