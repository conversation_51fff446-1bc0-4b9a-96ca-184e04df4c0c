"""
手动买入指导脚本
当自动买入失败时，指导用户手动操作
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def manual_buy_guide_000528():
    """000528手动买入指导"""
    print("=" * 60)
    print("📋 柳工(000528) 手动买入指导")
    print("=" * 60)
    
    print("🔔 由于自动输入功能可能不稳定，请按以下步骤手动买入:")
    print()
    
    print("📝 第1步: 在同花顺买入界面中")
    print("   1. 找到 '证券代码' 输入框")
    print("   2. 手动输入: 000528")
    print("   3. 按回车确认，系统会自动填入股票名称 '柳工'")
    print()
    
    print("💰 第2步: 设置买入价格")
    print("   1. 在 '买入价格' 输入框中输入: 8.52")
    print("   2. 这是卖二价，确保能够成交")
    print()
    
    print("📦 第3步: 设置买入数量")
    print("   1. 在 '买入数量' 输入框中输入: 100")
    print("   2. 或者在 '买入金额' 中输入: 852")
    print("   3. 系统会自动计算对应的股数")
    print()
    
    print("✅ 第4步: 确认买入")
    print("   1. 检查所有信息是否正确:")
    print("      - 股票代码: 000528")
    print("      - 股票名称: 柳工")
    print("      - 买入价格: 8.52元")
    print("      - 买入数量: 100股 (1手)")
    print("      - 总金额: 852元")
    print("   2. 点击 '买入' 按钮")
    print("   3. 确认委托信息")
    print()
    
    print("🔍 第5步: 验证委托")
    print("   1. 查看委托状态")
    print("   2. 确认订单已提交")
    print("   3. 等待成交确认")
    print()
    
    print("⚠️  注意事项:")
    print("   - 确保账户资金充足（至少852元）")
    print("   - 当前价格可能有变动，请以实时价格为准")
    print("   - 如果价格变动较大，可以调整买入价格")
    print("   - 建议使用略高于卖一价的价格确保成交")
    print()
    
    print("📊 实时价格参考:")
    print("   - 当前价格: 8.50元")
    print("   - 卖一价: 8.51元")
    print("   - 卖二价: 8.52元 ← 建议使用此价格")
    print("   - 买一价: 8.49元")
    print("   - 买二价: 8.48元")
    print()
    
    print("=" * 60)

def wait_for_manual_completion():
    """等待用户完成手动操作"""
    print("⏳ 请在同花顺中完成手动买入操作...")
    print("完成后按回车键继续，或输入 'q' 退出")
    
    while True:
        user_input = input("操作状态 (完成请按回车，退出输入q): ").strip().lower()
        
        if user_input == 'q':
            print("❌ 用户取消操作")
            return False
        elif user_input == '':
            print("✅ 用户确认完成手动买入")
            return True
        else:
            print("请按回车确认完成，或输入 'q' 退出")

def check_manual_buy_result():
    """检查手动买入结果"""
    print("\n🔍 检查买入结果...")
    print("请在同花顺中查看:")
    print("1. 委托状态 - 确认订单已提交")
    print("2. 成交记录 - 查看是否已成交")
    print("3. 持仓情况 - 确认股票已入账")
    print("4. 资金变动 - 确认资金已扣除")
    
    result = input("\n买入是否成功？(y/n): ").strip().lower()
    
    if result == 'y':
        print("🎉 恭喜！手动买入成功！")
        print("📝 建议记录以下信息:")
        print("   - 买入时间")
        print("   - 买入价格")
        print("   - 买入数量")
        print("   - 委托编号")
        return True
    else:
        print("❌ 买入可能未成功")
        print("💡 建议:")
        print("   1. 检查委托是否已提交")
        print("   2. 确认价格是否合适")
        print("   3. 检查资金是否充足")
        print("   4. 重新尝试买入")
        return False

def main():
    """主函数"""
    print("柳工(000528) 手动买入指导程序")
    print("当自动买入功能无法正常工作时使用")
    print()
    
    # 显示买入指导
    manual_buy_guide_000528()
    
    # 等待用户完成操作
    if wait_for_manual_completion():
        # 检查结果
        success = check_manual_buy_result()
        
        if success:
            print("\n🎊 手动买入流程完成！")
            print("建议继续关注股价变动和持仓情况。")
        else:
            print("\n🔄 如需重新尝试，可以重新运行此程序。")
    else:
        print("\n👋 程序已退出。")

if __name__ == "__main__":
    main()
